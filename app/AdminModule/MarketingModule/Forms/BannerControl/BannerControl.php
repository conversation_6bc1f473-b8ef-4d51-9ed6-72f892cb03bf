<?php

namespace tipli\AdminModule\MarketingModule\Forms;

use DateTime;
use Nette;
use Nette\Application\UI\Form;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\FrontModule\Presenters\HomepagePresenter;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Cache\CacheFacade;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Images\ImageStorage;
use tipli\Model\Marketing\BannerFacade;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Rewards\Entities\MoneyRewardCampaign;
use tipli\Model\Rewards\MoneyRewardFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;

class BannerControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var array */
	public $onNewVersion = [];

	/** @var Cache */
	private $homepageCache;

	private const BANNER_TYPE_LINK = 'link';
	private const BANNER_TYPE_SHOP = 'shop';

	public function __construct(
		private Banner|null $banner = null,
		private User $admin,
		private BannerFacade $bannerFacade,
		private ImageStorage $imageStorage,
		private MoneyRewardFacade $moneyRewardFacade,
		private ShopFacade $shopFacade,
		Storage $storage,
		private CacheFacade $cacheFacade,
		private DealFacade $dealFacade,
	) {
		$this->homepageCache = new Cache($storage, HomepagePresenter::class);
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addLocalizationSelect('localization')
			->setRequired('Zvolte lokalizaci');

		$form->addText('name', 'Název:')
			->setRequired('Vložte název.');

		$form->addRadioList('linkType', 'Přesměrovat na:')
			->setItems([
				self::BANNER_TYPE_SHOP => 'Do obchodu',
				self::BANNER_TYPE_LINK => 'Na URL odkaz',
			])
			->setDefaultValue('shop')
			->setRequired('Vyberte jednu z možností.')
			->addCondition(Form::EQUAL, 'link')
			->toggle('form-link-container');

		$form->addText('link', 'Odkaz:')
			->addConditionOn($form['linkType'], Form::EQUAL, 'link') // Make it required only if 'link' is selected
			->setRequired('Zadejte odkaz.')
			->addRule(Form::URL, 'Odkaz je v nesprávném tvaru.');

		$form->addCheckbox('openInNewWindow', 'Otevřít v novém okně')
			->setDefaultValue(true);

		$form->addText('validSince', 'Aktivní od:')
			->setRequired('Zadejte od kdy má být banner aktivní.')
			->setAttribute('class', 'datetimepicker')
			->setDefaultValue((new DateTime())->format('Y-m-d H:i:s'));

		$form->addText('validTill', 'Aktivní do:')
			->setRequired('Zadejte do kdy má být banner aktivní.')
			->setAttribute('class', 'datetimepicker');

		$form->addText('priority', 'Priorita:')
			->setRequired('Zadejte prioritu')
			->addRule(Form::INTEGER, 'Priorita musí být číslo.');

		$form->addSelect('format', 'Místo:')
			->setPrompt('- místo -')
			->setRequired('Zadejte místo')
			->setItems(Banner::getFormats())
			->addCondition(Form::NOT_EQUAL, Banner::FORMAT_NAVIGATION_LINK)
			->toggle('specific-settings');

		$form->addSelect('size', 'Velikost:')
			->setPrompt('- vyberte -')
			->setItems(Banner::getSizes());
//            ->addConditionOn($form['format'], Form::EQUAL, Banner::FORMAT_HOMEPAGE)
//            ->setRequired('Zadejte velikost');

		$form->addSelect('segment', 'Segment:')
			->setPrompt('- vyberte -')
			->setItems(Banner::getSegments());

		$moneyRewardCampaigns = [];
		/** @var MoneyRewardCampaign $moneyRewardCampaign */
		foreach ($this->moneyRewardFacade->getValidCampaigns()->getQuery()->getResult() as $moneyRewardCampaign) {
			$moneyRewardCampaigns[$moneyRewardCampaign->getId()] = $moneyRewardCampaign->getName();
		}

		$form->addSelect('moneyRewardCampaignId', 'MoneyReward kampaň')
			->setPrompt('- vyberte -')
			->setItems($moneyRewardCampaigns);

		$groupIdentifier = $form->addMultiSelect('groupIdentifier', 'Vybrat bannery do svazku')
				->setRequired(false);

		$form->addShopSelect('shop', 'Obchod:')
			->setLocalizationInput($form['localization'])
			->addConditionOn($form['linkType'], Nette\Forms\Form::EQUAL, self::BANNER_TYPE_SHOP)
			->setRequired('Vyberte obchod');

		$form->addTextArea('description', 'Popis:')
			->setHtmlAttribute('redactor')
		;

		$form->addUpload('image', "Obrázek:")
			->addRule(Form::IMAGE, 'Obrázek musí být v JPEG, PNG nebo GIF');

		$form->addUpload('imageMobile', 'Obrázek pro telefon:')
			->addRule(Form::IMAGE, 'Obrázek musí být v JPEG, PNG nebo GIF');

		if (!$this->banner) {
			$form['image']
				->addConditionOn($form['format'], Form::NOT_EQUAL, Banner::FORMAT_NAVIGATION_LINK)
				->addConditionOn($form['size'], Form::NOT_EQUAL, Banner::SIZE_MINI)
				->setRequired('Není vybrán žádný obrázek pro banner');

			$form['imageMobile']
				->addConditionOn($form['format'], Form::NOT_EQUAL, Banner::FORMAT_NAVIGATION_LINK)
				->addConditionOn($form['size'], Form::NOT_EQUAL, Banner::SIZE_MINI)
				->setRequired('Není vybrán žádný obrázek pro mobilní banner');
		}

		$form->addCheckbox('visibleMobileApp', 'Zobrazit banner v mobilní aplikaci')
			->setDefaultValue(true);

		$form->addCheckbox('visibleWeb', 'Zobrazit banner na webu')
			->setDefaultValue(true);

		$form->addCheckbox('showLogo', 'Ukázat logo na banneru');

		// join
		$form->addMultiSelect('dealIds', 'Deal')
			->setHtmlAttribute('data-search-url', $this->presenter->link('searchDeals!'));

		$form->addTextArea('conditions', 'Speciální podmínky')
			->setNullable();

		$form->addSubmit('submit', 'Uložit');

		if ($this->banner) {
			$form['localization']->setDisabled();

			$groupedBanners = $this->bannerFacade->findGroupedBanners($this->banner);
			$groupPairs = [];
			$groupDefaultValues = [];

			/** @var Banner $groupedBanner */
			foreach ($groupedBanners as $groupedBanner) {
				if ($groupedBanner->getId() !== $this->banner->getId()) {
					$groupDefaultValues[] = $groupedBanner->getId();
					$groupPairs[$groupedBanner->getId()] = $groupedBanner->getName();
				}
			}

			$groupIdentifier->setItems($groupPairs);
			$form->setDefaults([
				'localization' => $this->banner->getLocalization(),
				'name' => $this->banner->getName(),
				'linkType' => $this->banner->getLink() == null ? 'shop' : 'link',
				'link' => $this->banner->getLink(),
				'validSince' => $this->banner->getValidSince()->format('Y-m-d H:i:s'),
				'validTill' => $this->banner->getValidTill()->format('Y-m-d H:i:s'),
				'priority' => $this->banner->getPriority(),
				'shop' => $this->banner->getShop(),
				'format' => $this->banner->getFormat(),
				'openInNewWindow' => $this->banner->getOpenInNewWindow(),
				'showLogo' => $this->banner->getShowLogo(),
				'segment' => $this->banner->getSegment() ? $this->banner->getSegment() : '',
				'moneyRewardCampaignId' => $this->banner->getMoneyRewardCampaign() !== null ? $this->banner->getMoneyRewardCampaign()->getId() : null,
				'size' => $this->banner->getSize() ? : null,
				'groupIdentifier' => $groupDefaultValues,
				'visibleMobileApp' => $this->banner->isVisibleMobileApp(),
				'visibleWeb' => $this->banner->isVisibleWeb(),
				'conditions' => $this->banner->getConditions(),
				'description' => $this->banner->getDescription(),
			]);

			if ($this->banner->getDeal()) {
				$selectedDeals = [$this->banner->getDeal()->getId() => $this->banner->getDeal()->getName()];
				$form['dealIds']->setItems($selectedDeals);
				$form['dealIds']->setDefaultValue(array_keys($selectedDeals));
			}
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$link = $values->link;
			/** @var Shop $shop */
			$shop = $this->shopFacade->find($values->shop);

			if ($values->linkType === self::BANNER_TYPE_SHOP) {
				$link = null;
			}

			$bannerLink = new Url($link);

			if ($this->banner) {
				$localization = $this->banner->getLocalization();
			} else {
				$localization = $values->localization;
			}

			if ($values->linkType == self::BANNER_TYPE_LINK && !Strings::contains($bannerLink->getHost(), $localization->getBaseDomain())) {
				throw new InvalidArgumentException('Link může směřovat pouze na domény ' . $localization->getBaseDomain());
			}

			if (!$this->banner && !$values->image->hasFile() && $values->format !== Banner::FORMAT_NAVIGATION_LINK && $values->size !== Banner::SIZE_MINI) {
				throw new InvalidArgumentException('Nahrajte prosím obrázek.');
			}

			$validSince = new DateTime($values->validSince);
			$validTill = new DateTime($values->validTill);

			if ($validTill < $validSince) {
				throw new InvalidArgumentException('Datum aktivní do musí být větší než aktivní od.');
			}

//            if ($values->image->name) {
//                $imageWidth = $values->image->getImageSize()[0];
//                $imageHeight = $values->image->getImageSize()[1];
//
//                if ($values->format == Banner::FORMAT_HOMEPAGE && $values->size) {
//                    list($minWidth, $minHeight) = Banner::getSizeValues($values->size);
//
//                    if ($imageWidth != $minWidth || $imageHeight != $minHeight) {
//                        throw new InvalidArgumentException('Neplatná velikost obrázku. Nahrajte prosím obrázek o rozměrech: ' . $minWidth . 'x' . $minHeight . 'px');
//                    }
//                } else if ($imageWidth < 770 || $imageHeight < 370) {
//                    throw new InvalidArgumentException('Obrázek má příliš malé rozlišení.');
//                }
//            }

			$segment = empty($values->segment) ? null : $values->segment;

			if ($this->banner) {
				$banner = $this->banner;
				$banner->setName($values->name);
				$banner->setLink($link);
				$banner->setPriority($values->priority);
				$banner->setValidSince($validSince);
				$banner->setValidTill($validTill);
				$banner->setFormat($values->format);
				$banner->setSegment($segment);
				$this->bannerFacade->resetBannerGroups($banner);
			} else {
				$banner = $this->bannerFacade->createBanner($values->localization, $values->name, $link, $validSince, $validTill, $values->priority, $this->admin, $segment, $shop);
			}

			if ($values->image->hasFile()) {
				$banner->setImage($this->imageStorage->saveImage(
					$values->image,
					'marketing-banner-image',
					null,
					null
				));
			}

			if ($values->imageMobile->hasFile()) {
				$banner->setImageMobile($this->imageStorage->saveImage(
					$values->imageMobile,
					'marketing-banner-image',
					null,
					null
				));
			}

			if (isset($shop)) {
				$banner->setShop($shop);
			}

			$banner->setDescription($values->description);

			$banner->setShowLogo($values->showLogo);
			$banner->setOpenInNewWindow($values->openInNewWindow);
			$banner->setFormat($values->format);
			$banner->setSize($values->size ? : null);
			$this->bannerFacade->saveBanner($banner);

			$groupIdentifier = $form->getHttpData($form::DATA_TEXT | $form::DATA_KEYS, 'groupIdentifier[]');
			$countInGroup = count($groupIdentifier) + 1;
			$bannerNumber = 1;

			$banner->setGroupIdentifier($groupIdentifier ? $banner->getId() : null);
			$banner->setCountInGroup($countInGroup);
			$banner->setNumberInGroup($bannerNumber);

			$banner->setVisibleMobileApp($values->visibleMobileApp);
			$banner->setVisibleWeb($values->visibleWeb);

			$moneyRewardCampaign = $values->moneyRewardCampaignId !== null ? $this->moneyRewardFacade->findCampaign($values->moneyRewardCampaignId) : null;
			$banner->setMoneyRewardCampaign($moneyRewardCampaign);

			$dealId = $form->getHttpData($form::DATA_TEXT | $form::DATA_KEYS, 'dealIds[]')[0] ?? null;

			if ($dealId !== null) {
				$deal = $this->dealFacade->find($dealId);
				$banner->setDeal($deal);
			} else {
				$banner->setDeal(null);
			}

			if (empty($values->conditions)) {
				$values->conditions = null;
			}

			if ($dealId !== null && $values->conditions !== null) {
				throw new InvalidArgumentException('Připojit lze pouze deal nebo speciální podmínky.');
			}

			$banner->setConditions($values->conditions);

			$this->bannerFacade->saveBanner($banner);

			if ($groupIdentifier) {
				foreach ($groupIdentifier as $bannerId) {
					/** @var Banner $groupedBanner */
					$groupedBanner = $this->bannerFacade->find($bannerId);
					$groupedBanner->setGroupIdentifier($banner->getId());
					$groupedBanner->setCountInGroup($countInGroup);
					$groupedBanner->setNumberInGroup($bannerNumber + 1);
					$groupedBanner->setPriority($values->priority);
					$groupedBanner->setValidSince($validSince);
					$groupedBanner->setValidTill($validTill);
					$groupedBanner->setFormat($values->format);
					$groupedBanner->setSegment($segment);
					$groupedBanner->setFormat($values->format);
					$groupedBanner->setSize($values->size ? : null);
					$this->bannerFacade->saveBanner($groupedBanner);
				}
			}

			$this->homepageCache->clean([Cache::TAGS => ['banners']]);
			$this->cacheFacade->cleanByTags([CacheFacade::getBannersTag()]);

			$this->onNewVersion($banner, $this->admin);
			$this->onSuccess($banner);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IBannerControlFactory
{
	/**
	 * @param Banner|null $banner
	 * @param User $admin
	 * @return BannerControl
	 */
	public function create(Banner $banner = null, User $admin);
}
