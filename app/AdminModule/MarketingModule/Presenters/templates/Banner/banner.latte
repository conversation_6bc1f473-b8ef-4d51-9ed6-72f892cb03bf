{var $title = 'Nastavení banneru'}
{var $freeContent = true}

{block title}{$title}{/block}

{block content}

{form bannerControl-form}
<div class="row">
	<div class="col-md-6 offset-md-3">
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Základní nastavení</strong>
			</div>
			<div class="card-body">
				<div n:if="$form->hasErrors()">
					<ul class="errors">
						<li n:foreach="$form->errors as $error">{$error |noescape}</li>
					</ul>
				</div>

				<div class="row">
					<div class="form-group col-md-4">
						<label>Lokalizace</label>
						{input localization, class => "form-control"}
						{$form['localization']->getCustomHtml() |noescape}
					</div>

					<div class="form-group col-md-8">
						{label name /}
						{input name, class => "form-control"}
					</div>
				</div>

				<div class="row">
					<div class="form-group col-md-4">
						{label linkType /}
						<br />
						{input linkType, class => "form-control"}
					</div>

					<div class="form-group col-md-8" id="form-link-container">
						{label link /}
						{input link, class => "form-control"}
					</div>
				</div>

				<div class="form-group">
					<label>
						<input type="checkbox" n:name="openInNewWindow">
						Otevřít v novém okně (zaškrnout vždy, když posíláme uživatele pryč z Tipli webu)
					</label>
				</div>
			</div>
		</div>

		<div class="card card-accent-primary">
			<div class="card-header"><strong>Parametry</strong></div>
			<div class="card-body">
				<div class="d-flex">
					<div class="form-group">
						{label validSince /}
						{input validSince, class => "form-control datetimepicker", autocomplete => "off"}
					</div>

					<div class="form-group ml-2">
						{label validTill /}
						{input validTill, class => "form-control datetimepicker", autocomplete => "off"}
					</div>
				</div>

				<div class="form-group">
					{label priority /}
					{input priority, class => "form-control"}
				</div>

				<div class="form-group">
					{label format /}
					{input format, class => "form-control"}
				</div>

				<div class="form-group">
					{label size /}
					{input size, class => "form-control"}
				</div>

				<div class="form-group">
					{label segment /}
					{input segment, class => "form-control"}
				</div>

				<div class="form-group">
					{label moneyRewardCampaignId /}<br />
					{input moneyRewardCampaignId, class => "form-control"}
				</div>

				<div class="form-group">
					{label groupIdentifier /}
					{input groupIdentifier, class => "form-control"}
				</div>

				<div class="form-group">
					{label shop /}
					{input shop, class => "form-control"}
					{$form['shop']->getCustomHtml() |noescape}
				</div>

				<div class="form-group">
					{label description /}
					{input description, class => "form-control"}
				</div>
			</div>
		</div>

		<div id="specific-settings" class="card card-accent-primary">
			<div class="card-header"><strong>Obrázky</strong></div>
			<div class="card-body">
				<div class="form-group">
					<div class="row">
						<div class="col-4">
							<label>Obrázek pro web</label>
							{input image}
						</div>

						<div n:ifset='$banner' class="col-4">
							<img src="{$banner->getImage() |image:863,248,'exact'}">
						</div>
					</div>
				</div>

				<hr>

				<div class="form-group">
					<div class="row">
						<div class="col-4">
							<label>Obrázek pro mobil</label>
							{input imageMobile}
						</div>

						{ifset $banner}
							<div class="col-4">
								{if $banner->getImageMobile()}
									<img src="{$banner->getImageMobile() |image:345,194,'exact'}" class="d-inline">
								{else}
									<div class="alert alert-warning">Obrázek pro mobil nebyl nahrán.</div>
								{/if}
							</div>
						{/ifset}
					</div>
				</div>

				<hr>

				<div class="form-group">
					<label>
						<input type="checkbox" n:name="visibleMobileApp">
						Zobrazit banner v aplikaci
					</label>
				</div>

				<div class="form-group">
					<label>
						<input type="checkbox" n:name="visibleWeb">
						Zobrazit banner na webu
					</label>
				</div>

				<div class="form-group">
					<label>
						<input type="checkbox" n:name="showLogo">
						Ukázat logo na banneru
					</label>
				</div>
			</div>
		</div>

		<div class="card card-accent-primary" n:ifset="$banner">
			<div class="card-header">
				<strong>Propojení</strong>
			</div>
			<div class="card-body">
				<div class="row">
					<div class="form-group col-md-12">
						{label dealIds /}
						{input dealIds, class => "form-control search-input select-items"}
					</div>
				</div>

				<div style="display: flex; align-items: center; text-align: center;">
					<hr style="flex: 1; border: none; border-top: 1px solid #ccc;" />
					<span style="padding: 0 10px; color: #666;font-weight:bold;">NEBO</span>
					<hr style="flex: 1; border: none; border-top: 1px solid #ccc;" />
				</div>

				<div class="row">
					<div class="form-group col-md-12">
						{label conditions /}
						<textarea n:name="conditions" class="form-control"></textarea>
					</div>
				</div>
			</div>
		</div>

		<div class="card card-accent-primary">
			<div class="card-body">
				<div class="row">
					<div class="form-group col-md-12 text-center">
						{input submit, class => "btn btn-primary"}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
{/form}

{block scripts}
<script>
	var searchInputs = $('.search-input');

	searchInputs.each(function(){
		var searchInputTargetUrl = $(this).data('searchUrl');

		$(this).select2({
			minimumInputLength: 2,
			maximumSelectionLength: 1,
			closeOnSelect: true,
			ajax: {
				url: searchInputTargetUrl,
				dataType: 'json',
				delay: 500,
				type: 'GET',
				cache: true,
				processResults: function (data) {
					return {
						results: $.map(data, function (item, index) {
							return {
								text:  item,
								id: index
							}
						})
					};
				}
			}
		});
	});
</script>
<script type="text/javascript">
	var pageGroupSelect = $('#frm-bannerControl-form-groupIdentifier');
	$(pageGroupSelect).select2({
		minimumInputLength: 2,
		ajax: {
			url: {link searchBannerByName!},
			dataType: 'json',
			delay: 500,
			cache: true,
			processResults: function (data) {
				return {
					results: $.map(data, function (item, index) {
						return {
							text: item.name,
							id: index
						}
					})
				};
			}
		}
	});
</script>
{/block}
