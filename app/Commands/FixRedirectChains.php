<?php

namespace tipli\Commands;

use Nette\Database\Context;

class FixRedirect<PERSON>hai<PERSON> extends Job
{
	public function __construct(private Context $context)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:fix-redirect-chains:run');
	}

	public function start()
	{
		$linksToRemove = [
			'https://apps.apple.com/cz/app/czc-cz/id1020875138',
			'https://apps.apple.com/cz/app/electro-world-smart-app/id1458615292?l=cs',
			'https://apps.apple.com/cz/app/fler-prodej/id1078166060?l=cs',
			'https://apps.apple.com/cz/app/foodora-cz-jídlo-a-nákupy/id582501441?l=cs',
			'https://apps.apple.com/cz/app/moje-garáž/id1156437245',
			'https://cz.bellfor.info/',
			'https://gate.shop/cz/reklamacni-rad',
			'https://gate.shop/cz/vraceni-zbozi',
			'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/33FRDKu6ivNwP0RVQtlS92SdiH8Tgom8_7etI7yx_I0/resize:fit:900:0:1/gravity:no/quality:90/aHR0cHM6Ly9zdGF0aWMudGlwbGkuY3ovdC91cGxvYWQvaW1hZ2VzL3BpY3R1cmVzLXBpY3R1cmUtaW1hZ2UvMmVkMi8xMTQweC9maXQvMjgyNzYzLmpwZz92PTEuOQ.png',
			'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/RkheyTtMfFVIBHzIy9D-gwN3ttyDQAUR7JPj624cBiA/resize:fill:268:268:1/gravity:no/quality:90/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMzE1LzMxNTE1Ny8zNGFmZDQzNmM5NTQzNDNjLjdrajZibjdiZ3FmNS5qcGc.jpg',
			'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/uuF36aKXwy2eqjI9rwPzwRjr8Od8pTN89a2EPrbNmYw/resize:fill:268:268:1/gravity:no/quality:90/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMzE1LzMxNTM0OS81aTE4cDZ5cWFwdXlrdG5pdXd6cjk4b3AuanBn.jpg',
			'https://ivoprax.cz/cinska-mesta-vs-hdp-statu/',
			'https://navody.rajce.idnes.cz/Jak_pridat_nove_album',
			'https://nejlepsiceskacasina.com/mezinarodni-online-casino',
			'https://play.google.com/store/apps/details?id=cz.czc.app',
			'https://play.google.com/store/apps/details?id=cz.ew.ewsmartapp&hl=cs&gl=US',
			'https://play.google.com/store/apps/details?id=cz.rollingmobile.fler',
			'https://play.google.com/store/apps/details?id=eneba.eneba.eneba8.enebaapp',
			'https://www.alza.cz/hracky/halloween-party/18889484.htm',
			'https://www.bonprix.cz/servis/objednavky/objednavkovy-formular/',
			'https://www.electroworld.cz/data/newsletter-ew/Docs/OoKS_12_2020.pdf',
			'https://www.notino.cz/armani',
			'https://www.notino.cz/beautyblog/tutorialy/halloween-make-up-tutorial-crazy-joker/',
			'https://www.notino.cz/makeup-revolution/by-petra-paleta-ocnich-stinu/',
			'https://www.rockpoint.cz/data/files/RP_obchodni_podminky_A4_RP_final_formular(1).pdf',
			'https://www.tipli.cz/akce/souteze',
			'https://www.tipli.cz/letaky/benu-lekarna',
			'https://www.tipli.cz/letaky/dr-max',
			'https://www.tipli.cz/letaky/eva-cz',
			'https://www.tipli.cz/letaky/jysk',
			'https://www.tipli.cz/letaky/makro',
			'https://www.tipli.cz/letaky/mobelix',
			'https://www.tipli.cz/letaky/mountfield',
			'https://www.tipli.cz/letaky/planeo',
			'https://www.tipli.cz/letaky/tchibo',
			'https://www.tipli.cz/letaky/terno',
			'https://www.tipli.cz/letaky/trefa',
			'https://www.tipli.cz/letaky/tupperware',
			'https://www.tipli.sk/letaky/fresh',
			'https://www.umenivina.cz/limitovana-nabidka-vin/',
		];

		$this->output->writeln('<info>Removing links from content...</info>');
		$this->output->writeln('<comment>Links to remove: ' . implode(', ', $linksToRemove) . '</comment>');

		$totalProcessed = 0;

		foreach ($linksToRemove as $link) {
			$processed = $this->removeLinkFromContent($link);
			$totalProcessed += $processed;
			$this->output->writeln("<info>Processed {$processed} records for link: {$link}</info>");
		}

		$this->output->writeln("<success>Total processed records: {$totalProcessed}</success>");
	}

	private function removeLinkFromContent(string $linkToRemove): int
	{
		$processedCount = 0;

		// Process articles
		$processedCount += $this->processArticles($linkToRemove);

		// Process shop description blocks
		$processedCount += $this->processShopDescriptionBlocks($linkToRemove);

		return $processedCount;
	}

	private function processArticles(string $linkToRemove): int
	{
		$articles = $this->context->query('
			SELECT id, content
			FROM tipli_articles_article
			WHERE content LIKE ? AND content IS NOT NULL
		', '%' . $linkToRemove . '%')->fetchAll();

		$processedCount = 0;

		foreach ($articles as $article) {
			$originalContent = $article->content;
			$newContent = $this->removeLinksFromHtmlContent($originalContent, $linkToRemove);

			if ($originalContent !== $newContent) {
				$this->context->query('
					UPDATE tipli_articles_article
					SET content = ?
					WHERE id = ?
				', $newContent, $article->id);

				$processedCount++;
				$this->output->writeln("<comment>Updated article ID: {$article->id}</comment>");
			}
		}

		return $processedCount;
	}

	private function processShopDescriptionBlocks(string $linkToRemove): int
	{
		$descriptionBlocks = $this->context->query('
			SELECT id, description
			FROM tipli_shops_shop_description_block
			WHERE description LIKE ? AND description IS NOT NULL
		', '%' . $linkToRemove . '%')->fetchAll();

		$processedCount = 0;

		foreach ($descriptionBlocks as $block) {
			$originalDescription = $block->description;
			$newDescription = $this->removeLinksFromHtmlContent($originalDescription, $linkToRemove);

			if ($originalDescription !== $newDescription) {
				$this->context->query('
					UPDATE tipli_shops_shop_description_block
					SET description = ?
					WHERE id = ?
				', $newDescription, $block->id);

				$processedCount++;
				$this->output->writeln("<comment>Updated shop description block ID: {$block->id}</comment>");
			}
		}

		return $processedCount;
	}

	private function removeLinksFromHtmlContent(?string $content, string $linkToRemove): ?string
	{
		if (empty($content)) {
			return $content;
		}

		// Escape the link for use in regex
		$escapedLink = preg_quote($linkToRemove, '/');

		// More robust regex patterns for different anchor tag formats
		$patterns = [
			// Standard anchor tags with href containing the link
			'/<a\s+[^>]*href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is',
			// Anchor tags with href as first attribute
			'/<a\s+href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is',
			// Anchor tags with additional attributes before href
			'/<a\s+[^>]*href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is',
		];

		$modifiedContent = $content;
		$replacementsMade = false;

		foreach ($patterns as $pattern) {
			$modifiedContent = preg_replace_callback($pattern, function ($matches) use ($linkToRemove, &$replacementsMade) {
				// Check if the href actually contains our target link
				if (strpos($matches[1], $linkToRemove) !== false) {
					$replacementsMade = true;
					$linkText = trim(strip_tags($matches[2]));

					// Log the replacement for debugging
					$this->output->writeln("<info>Removing link: {$matches[0]}</info>");
					$this->output->writeln("<info>Replacing with text: {$linkText}</info>");

					return $linkText;
				}

				// Return original match if it doesn't contain our target link
				return $matches[0];
			}, $modifiedContent);
		}

		// Additional cleanup for any remaining malformed links
		$modifiedContent = $this->cleanupMalformedLinks($modifiedContent, $linkToRemove);

		return $modifiedContent;
	}

	private function cleanupMalformedLinks(string $content, string $linkToRemove): string
	{
		// Handle cases where links might be malformed or have unusual formatting
		$escapedLink = preg_quote($linkToRemove, '/');

		// Remove any remaining href attributes that contain the link
		$content = preg_replace('/href\s*=\s*["\'][^"\']*' . $escapedLink . '[^"\']*["\']/', '', $content);

		// Clean up any empty anchor tags that might be left
		$content = preg_replace('/<a\s*[^>]*>\s*<\/a>/', '', $content);
		$content = preg_replace('/<a\s*>\s*<\/a>/', '', $content);

		// Remove any standalone anchor tags without content
		$content = preg_replace('/<a\s*[^>]*>\s*/', '', $content);
		$content = preg_replace('/<\/a>/', '', $content);

		return $content;
	}

	private function validateHtmlContent(string $content): bool
	{
		// Basic validation to ensure we haven't broken the HTML structure
		$openTags = substr_count($content, '<a');
		$closeTags = substr_count($content, '</a>');

		// If there are unmatched tags, we might have an issue
		if ($openTags !== $closeTags) {
			$this->output->writeln("<warning>Warning: Unmatched anchor tags detected in content</warning>");
			return false;
		}

		return true;
	}
}
