<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Transactions\Entities\Transaction;

class Sandbox extends Job
{
	public function __construct(private Context $context)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:sandbox:run');
		$this->addArgument('type');
	}

	public function start()
	{
		echo "Running sandbox command\n";

		$rows = $this->context->query('SELECT user_id, MIN(created_at) AS min_created_at FROM tipli_transactions_transaction WHERE created_at >= (NOW() - INTERVAL 2 YEAR) AND type = ? AND user_id IS NOT NULL GROUP BY user_id', Transaction::TYPE_COMMISSION);

		foreach ($rows as $row) {
			echo "Processing user ID: {$row->user_id}, Min Created At: {$row->min_created_at->format('Y-m-d H:i:s')}\n";
			$this->context->query('UPDATE tipli_account_segment_data sd SET sd.first_transaction_at = "' . $row->min_created_at->format('Y-m-d H:i:s') . '" WHERE sd.user_id = ' . $row->user_id);
		}
	}
}
