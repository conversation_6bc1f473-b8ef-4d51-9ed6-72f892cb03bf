<?php

namespace tipli\Model\Addon\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Random;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Addon\Repositories\AlternativeOfferRepository")
 * @ORM\Table(name="tipli_addon_alternative_offer", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="unique_id", columns={"unique_id"})
 * })
 */
class AlternativeOffer
{
	public const VISIBILITY_HIDDEN = 'draft';
	public const VISIBILITY_ADMIN = 'admin';
	public const VISIBILITY_PUBLIC = 'public';

	public const FREQUENCY_UNLIMITED = 0;
	public const FREQUENCY_1_HOUR = 1;
	public const FREQUENCY_24_HOURS = 24;
	public const FREQUENCY_7_DAYS = 7;
	public const FREQUENCY_10_YEARS = 10 * 365;

	public const URL_MASK_TYPE_STARTS_WITH = 'startsWith';
	public const URL_MASK_TYPE_DOMAIN_CONTAINS = 'domainContains';

	public const POPUP_TYPE_SHOPS_LIST = 'shopsList';
	public const POPUP_TYPE_BETTER_OFFER = 'betterOffer';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private Localization $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private ?Shop $shop = null;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="popup_shop_id", referencedColumnName="id")
	 */
	private ?Shop $popupShop = null;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="better_shop_id", referencedColumnName="id")
	 */
	private ?Shop $betterShop = null;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Addon\Entities\AlternativeOfferShop", mappedBy="alternativeOffer")
	 * @ORM\OrderBy({"priority" = "DESC"})
	 */
	private Collection $shops;

	/**
	 * @ORM\Column(type="string", length=19)
	 */
	private string $uniqueId;

	/**
	 * @ORM\Column(type="string", length=16)
	 */
	private string $visibility = AlternativeOffer::VISIBILITY_HIDDEN;

	/**
	 * @ORM\Column(type="string", length=128)
	 */
	private string $name;

	/**
	 * @ORM\Column(type="string", length=32)
	 */
	private string $urlMaskType;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $urlMask;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $frequency;

	/**
	 * @ORM\Column(type="string", length=32)
	 */
	private string $popupType;

	/**
	 * @ORM\Column(type="string", length=64, nullable=true)
	 */
	private ?string $sadText = null;

	/**
	 * @ORM\Column(type="string", length=128)
	 */
	private string $title;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $description;

	/**
	 * @ORM\Column(type="string", length=64, nullable=true)
	 */
	private ?string $bottomTitle = null;

	/**
	 * @ORM\Column(type="string", length=64, nullable=true)
	 */
	private ?string $bottomButtonTitle = null;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $bottomUrl = null;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 */
	private ?bool $generateShops = null;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 */
	private ?bool $skipShopsWhereUserHasTransaction = null;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $countOfShops = null;

	/**
	 * @ORM\Column(type="string", length=64, nullable=true)
	 */
	private ?string $challengeTitle = null;

	/**
	 * @ORM\Column(type="string", length=32, nullable=true)
	 */
	private ?string $challengeButtonTitle = null;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $betterShopDeepUrl = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $removedAt = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(
		Localization $localization,
		?Shop $shop,
		string $name,
		string $urlMaskType,
		string $urlMask,
		string $frequency,
		string $popupType,
		string $title
	) {
		$this->uniqueId = Random::generate(6) . '-' . Random::generate(12);
		$this->localization = $localization;
		$this->shops = new ArrayCollection();
		$this->shop = $shop;
		$this->name = $name;
		$this->urlMaskType = $urlMaskType;
		$this->urlMask = $urlMask;
		$this->frequency = $frequency;
		$this->popupType = $popupType;
		$this->title = $title;
		$this->createdAt = new \DateTime();
	}

	public function getId()
	{
		return $this->id;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getShop(): ?Shop
	{
		return $this->shop;
	}

	public function getShops(): Collection
	{
		return $this->shops;
	}

	public function setShop(?Shop $shop): void
	{
		$this->shop = $shop;
	}

	public function getBetterShop(): ?Shop
	{
		return $this->betterShop;
	}

	public function setBetterShop(?Shop $betterShop): void
	{
		$this->betterShop = $betterShop;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getUrlMaskType(): string
	{
		return $this->urlMaskType;
	}

	public function setUrlMaskType(string $urlMaskType): void
	{
		$this->urlMaskType = $urlMaskType;
	}

	public function getUrlMask(): string
	{
		return $this->urlMask;
	}

	public function setUrlMask(string $urlMask): void
	{
		$this->urlMask = $urlMask;
	}

	public function getFrequency(): string
	{
		return $this->frequency;
	}

	public function setFrequency(string $frequency): void
	{
		$this->frequency = $frequency;
	}

	public function getPopupType(): string
	{
		return $this->popupType;
	}

	public function setPopupType(string $popupType): void
	{
		$this->popupType = $popupType;
	}

	public function getSadText(): ?string
	{
		return $this->sadText;
	}

	public function setSadText(?string $sadText): void
	{
		$this->sadText = $sadText;
	}

	public function getTitle(): string
	{
		return $this->title;
	}

	public function setTitle(string $title): void
	{
		$this->title = $title;
	}

	public function getBottomTitle(): ?string
	{
		return $this->bottomTitle;
	}

	public function setBottomTitle(?string $bottomTitle): void
	{
		$this->bottomTitle = $bottomTitle;
	}

	public function getBottomButtonTitle(): ?string
	{
		return $this->bottomButtonTitle;
	}

	public function setBottomButtonTitle(?string $bottomButtonTitle): void
	{
		$this->bottomButtonTitle = $bottomButtonTitle;
	}

	public function getGenerateShops(): ?bool
	{
		return $this->generateShops;
	}

	public function setGenerateShops(?bool $generateShops): void
	{
		$this->generateShops = $generateShops;
	}

	public function shouldSkipShopsWhereUserHasTransaction(): ?bool
	{
		return $this->skipShopsWhereUserHasTransaction;
	}

	public function setSkipShopsWhereUserHasTransaction(?bool $skipShopsWhereUserHasTransaction): void
	{
		$this->skipShopsWhereUserHasTransaction = $skipShopsWhereUserHasTransaction;
	}

	public function getCountOfShops(): ?int
	{
		return $this->countOfShops;
	}

	public function setCountOfShops(?int $countOfShops): void
	{
		$this->countOfShops = $countOfShops;
	}

	public function getChallengeTitle(): ?string
	{
		return $this->challengeTitle;
	}

	public function setChallengeTitle(?string $challengeTitle): void
	{
		$this->challengeTitle = $challengeTitle;
	}

	public function getChallengeButtonTitle(): ?string
	{
		return $this->challengeButtonTitle;
	}

	public function setChallengeButtonTitle(?string $challengeButtonTitle): void
	{
		$this->challengeButtonTitle = $challengeButtonTitle;
	}

	public function getBetterShopDeepUrl(): ?string
	{
		return $this->betterShopDeepUrl;
	}

	public function setBetterShopDeepUrl(?string $betterShopDeepUrl): void
	{
		$this->betterShopDeepUrl = $betterShopDeepUrl;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	public function remove(): void
	{
		$this->removedAt = new \DateTime();
	}

	public function isRemoved(): bool
	{
		return $this->removedAt !== null;
	}

	public function getUniqueId(): string
	{
		return $this->uniqueId;
	}

	public function getBottomUrl(): ?string
	{
		return $this->bottomUrl;
	}

	public function setBottomUrl(?string $bottomUrl): void
	{
		$this->bottomUrl = $bottomUrl;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function setDescription(?string $description): void
	{
		$this->description = $description;
	}

	public function getVisibility(): string
	{
		return $this->visibility;
	}

	public function setVisibility(string $visibility): void
	{
		$this->visibility = $visibility;
	}

	public function getPopupShop(): ?Shop
	{
		return $this->popupShop;
	}

	public function setPopupShop(?Shop $popupShop): void
	{
		$this->popupShop = $popupShop;
	}
}
