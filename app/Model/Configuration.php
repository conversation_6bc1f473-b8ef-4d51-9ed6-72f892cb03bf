<?php

namespace tipli\Model;

use Nette\Http\Request;
use Nette\Security\User;
use Nette\SmartObject;
use tipli\InvalidArgumentException;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;

class Configuration
{
	use SmartObject;

	/**
	 * @var string
	 */
	private $thumbnailsPath;

	/**
	 * @var string
	 */
	private $imagesPath;

	/**
	 * @var bool
	 */
	private $trackingAllowed = true;

	/**
	 * @var array
	 */
	private $facebookApps;

	/**
	 * @var array
	 */
	private $gaAccounts;

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	/**
	 * @var array
	 */
	private $bonusFriendRewardCampaigns;

	/**
	 * @var array
	 */
	private $allowedPhoneCountryCodes;

	/**
	 * @var array
	 */
	private $facebookPages;

	/**
	 * @var array
	 */
	private $smartsUppGroups;

	/**
	 * @var array
	 */
	private $urls;

	/**
	 * @var array
	 */
	private $unLoggedRedirectionUsers;

	/** @var string */
	private $localeDir;

	/** @var array */
	private $localesShortcuts;

	/**
	 * @var array
	 */
	private $smsbranaCredentials = [];

	/**
	 * @var string
	 */
	private $filesPath;

	/**
	 * @var array
	 */
	private $fbAdAccountIds = [];

	/**
	 * @var string
	 */
	private $leafletsApiKey;

	/**
	 * @var string
	 */
	private $storesApiKey;

	/**
	 * @var string
	 */
	private $ipDataApiKey;

	/**
	 * @var string
	 */
	private $mode;

	/**
	 * @var array
	 */
	private $googleIOSClientId;

	/**
	 * @var string
	 */
	private $googleWebClientId;

	/**
	 * @var string
	 */
	private $appleClientId;

	/**
	 * @var string
	 */
	private $appleClientSecret;

	/**
	 * @var string
	 */
	private $tickApiKey;

	/**
	 * @var string
	 */
	private $collabimApiKey;

	/**
	 * @var array
	 */
	private $twistoApiKeys;

	/**
	 * @var boolean
	 */
	private $rabbitMqAllowed = true;

	/**
	 * @var array
	 */
	private $zasilkovnaShareRewardCampaignIds;

	/**
	 * @var array
	 */
	private $zasilkovnaMoneyRewardCampaignIds;

	/**
	 * @var array
	 */
	private $zasilkovnaPartnerOrganizationIds;

	/** @var Request */
	private $httpRequest;

	/** @var User */
	private $netteUser;

	/** @var array */
	private $mobileAppStoreLinks;

	/** @var array */
	private $freshdeskGroupsIds;

	/** @var array */
	private $freshdeskEmailConfigIds;

	/** @var array */
	private $fioAccounts;

	/** @var string */
	private $tempDir;

	/** @var array */
	private $websites;

	/** @var string */
	private $mailchimpApiKey;

	/** @var string */
	private $mailchimpServer;

	/** @var array */
	private $mailchimpLists;

	/** @var int */
	private $mailkitId;

	/** @var string */
	private $mailkitMd5;

	/** @var array */
	private $mailkitLists;

	public function __construct(LocalizationFacade $localizationFacade, Request $httpRequest, User $netteUser)
	{
		$this->localizationFacade = $localizationFacade;
		$this->httpRequest = $httpRequest;
		$this->netteUser = $netteUser;
	}

	/**
	 * @return string
	 */
	public function getImagesPath()
	{
		return $this->imagesPath;
	}

	/**
	 * @param string $imagesPath
	 */
	public function setImagesPath($imagesPath)
	{
		$this->imagesPath = $imagesPath;
	}

	/**
	 * @return string
	 */
	public function getThumbnailsPath()
	{
		return $this->thumbnailsPath;
	}

	/**
	 * @param string $thumbnailsPath
	 */
	public function setThumbnailsPath($thumbnailsPath)
	{
		$this->thumbnailsPath = $thumbnailsPath;
	}

	/**
	 * Algolia Search index name for shops
	 * @param Localization $localization
	 * @return string
	 */
	public function getShopsIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_shops';
	}

	/**
	 * Algolia Search index name for all shops
	 * @return string
	 */
	public function getAllShopsIndexName(): string
	{
		return 'all_shops';
	}

	/**
	 * Algolia Search index name for tags
	 * @param Localization $localization
	 * @return string
	 */
	public function getTagsIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_tags';
	}

	/**
	 * Algolia Search index name for sales
	 * @param Localization $localization
	 * @return string
	 */
	public function getSalesIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_sales';
	}

	/**
	 * Algolia Search index name for articles
	 * @param Localization $localization
	 * @return string
	 */
	public function getArticlesIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_articles';
	}

	/**
	 * Algolia Search index name for leaflets
	 * @param Localization $localization
	 * @return string
	 */
	public function getLeafletsIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_leaflets';
	}

	public function getDealsIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_deals';
	}

	/**
	 * Algolia Search index name for regions
	 * @param Localization $localization
	 * @return string
	 */
	public function getRegionsIndexName(Localization $localization)
	{
		return $localization->getLocale() . '_regions';
	}

	/**
	 * @param boolean $trackingAllowed
	 */
	public function setTrackingAllowed($trackingAllowed)
	{
		$this->trackingAllowed = $trackingAllowed;
	}

	/**
	 * @return boolean
	 */
	public function isTrackingAllowed()
	{
		return $this->trackingAllowed;
	}

	/**
	 * @param array $facebookApps
	 */
	public function setFacebookApps($facebookApps)
	{
		$this->facebookApps = $facebookApps;
	}

	/**
	 * @return string|null
	 */
	public function getFacebookAppId()
	{
		$locale = $this->localizationFacade->getCurrentLocalization()->getLocale();

		if (array_key_exists($locale, $this->facebookApps)) {
			return $this->facebookApps[$locale];
		}

		return null;
	}

	/**
	 * @param array $gaAccounts
	 */
	public function setGaAccounts($gaAccounts)
	{
		$this->gaAccounts = $gaAccounts;
	}

	/**
	 * @return string
	 */
	public function getGaAccountId(Localization $localization = null)
	{
		return $this->gaAccounts[$localization ? $localization->getLocale() : $this->localizationFacade->getCurrentLocalization()->getLocale()];
	}

	public function setBonusFriendRewardCampaigns($bonusFriendRewardCampaigns)
	{
		$this->bonusFriendRewardCampaigns = $bonusFriendRewardCampaigns;
	}

	public function getBonusFriendRewardCampaignId(Localization $localization)
	{
		return $this->bonusFriendRewardCampaigns[$localization->getLocale()];
	}

	public function setFacebookPages($pages)
	{
		$this->facebookPages = $pages;
	}

	public function getFacebookPage(Localization $localization)
	{
		return $this->facebookPages[$localization->getLocale()];
	}

	public function setAllowedPhoneCountryCodes(array $allowedPhoneCountryCodes)
	{
		$this->allowedPhoneCountryCodes = $allowedPhoneCountryCodes;
	}

	public function getAllowedPhoneCountryCodes(Localization $localization)
	{
		return $this->allowedPhoneCountryCodes[$localization->getLocale()];
	}

	public function setSmartsUppGroups(array $groups)
	{
		$this->smartsUppGroups = $groups;
	}

	public function getSmartsUppGroup(Localization $localization)
	{
		return $this->smartsUppGroups[$localization->getLocale()];
	}

	public function setUrls(array $urls)
	{
		$this->urls = $urls;
	}

	public function getUrl(Localization $localization)
	{
		return $this->urls[$localization->getLocale()];
	}

	public function getUrls()
	{
		return $this->urls;
	}

	public function setUnLoggedRedirectionUsers(array $users)
	{
		$this->unLoggedRedirectionUsers = $users;
	}

	public function getUnLoggedRedirectionUser(Localization $localization)
	{
		return $this->unLoggedRedirectionUsers[$localization->getLocale()];
	}

	public function getUnLoggedRedirectionUserIds()
	{
		return $this->unLoggedRedirectionUsers;
	}

	public function getLocaleDir()
	{
		return $this->localeDir;
	}

	public function setLocaleDir($localeDir)
	{
		$this->localeDir = $localeDir;
	}

	public function getLocaleShortcut(Localization $localization)
	{
		return $this->localesShortcuts[$localization->getLocale()];
	}

	public function setLocalesShortcuts($localesShortcuts)
	{
		$this->localesShortcuts = $localesShortcuts;
	}

	public function getSmsbranaCredentials()
	{
		return $this->smsbranaCredentials;
	}

	public function setSmsbranaCredentials($smsbranaCredentials)
	{
		$this->smsbranaCredentials = $smsbranaCredentials;
	}

	/**
	 * @return string
	 */
	public function getFilesPath()
	{
		return $this->filesPath;
	}

	/**
	 * @param string $filesPath
	 */
	public function setFilesPath($filesPath)
	{
		$this->filesPath = $filesPath;
	}

	/**
	 * @return mixed
	 */
	public function getFbAdAccountIds()
	{
		return $this->fbAdAccountIds;
	}

	/**
	 * @param mixed $fbAdAccountIds
	 */
	public function setFbAdAccountIds($fbAdAccountIds)
	{
		$this->fbAdAccountIds = $fbAdAccountIds;
	}

	/**
	 * @return string
	 */
	public function getLeafletsApiKey()
	{
		return $this->leafletsApiKey;
	}

	/**
	 * @param string $leafletsApiKey
	 */
	public function setLeafletsApiKey($leafletsApiKey)
	{
		$this->leafletsApiKey = $leafletsApiKey;
	}

	/**
	 * @return string
	 */
	public function getStoresApiKey()
	{
		return $this->storesApiKey;
	}

	/**
	 * @param string $storesApiKey
	 */
	public function setStoresApiKey(string $storesApiKey)
	{
		$this->storesApiKey = $storesApiKey;
	}

	/**
	 * @return string
	 */
	public function getIpDataApiKey()
	{
		return $this->ipDataApiKey;
	}

	/**
	 * @param string $ipDataApiKey
	 */
	public function setIpDataApiKey(string $ipDataApiKey)
	{
		$this->ipDataApiKey = $ipDataApiKey;
	}

	/**
	 * @return string
	 */
	public function getMode()
	{
		if (getenv('TIPLI_DEBUG') === 'dd72daf3c3654258475a34a21' && isset($_COOKIE['acceptance']) && $_COOKIE['acceptance'] === 'tests') {
			return 'test';
		}

		return $this->mode;
	}

	/**
	 * @param string $mode
	 */
	public function setMode($mode)
	{
		$this->mode = $mode;
	}

	/**
	 * @return string
	 */
	public function getTickApiKey(): string
	{
		return $this->tickApiKey;
	}

	/**
	 * @param string $tickApiKey
	 */
	public function setTickApiKey(string $tickApiKey)
	{
		$this->tickApiKey = $tickApiKey;
	}

	/**
	 * @return string
	 */
	public function getCollabimApiKey(): string
	{
		return $this->collabimApiKey;
	}

	/**
	 * @param string $collabimApiKey
	 */
	public function setCollabimApiKey(string $collabimApiKey)
	{
		$this->collabimApiKey = $collabimApiKey;
	}

	/**
	 * @return mixed
	 */
	public function getTwistoApiKeys()
	{
		return $this->twistoApiKeys;
	}

	/**
	 * @param mixed $twistoApiKeys
	 */
	public function setTwistoApiKeys($twistoApiKeys)
	{
		$this->twistoApiKeys = $twistoApiKeys;
	}

	/**
	 * @return boolean
	 */
	public function isRabbitMqAllowed()
	{
		return $this->rabbitMqAllowed === true;
	}

	public function setRabbitMqAllowed(bool $rabbitMqAllowed)
	{
		$this->rabbitMqAllowed = $rabbitMqAllowed;
	}

	/**
	 * @return int
	 */
	public function getZasilkovnaShareRewardCampaignId(Localization $localization): int
	{
		return $this->zasilkovnaShareRewardCampaignIds[$localization->getLocale()];
	}

	/**
	 * @param array $zasilkovnaShareRewardCampaignIds
	 */
	public function setZasilkovnaShareRewardCampaignIds(array $zasilkovnaShareRewardCampaignIds)
	{
		$this->zasilkovnaShareRewardCampaignIds = $zasilkovnaShareRewardCampaignIds;
	}

	/**
	 * @return int
	 */
	public function getZasilkovnaMoneyRewardCampaignId(Localization $localization): int
	{
		return $this->zasilkovnaMoneyRewardCampaignIds[$localization->getLocale()];
	}

	/**
	 * @param array $zasilkovnaMoneyRewardCampaignIds
	 */
	public function setZasilkovnaMoneyRewardCampaignIds(array $zasilkovnaMoneyRewardCampaignIds)
	{
		$this->zasilkovnaMoneyRewardCampaignIds = $zasilkovnaMoneyRewardCampaignIds;
	}

	/**
	 * @return int
	 */
	public function getZasilkovnaPartnerOrganizationId(Localization $localization): int
	{
		return $this->zasilkovnaPartnerOrganizationIds[$localization->getLocale()];
	}

	/**
	 * @param array $zasilkovnaPartnerOrganizationIds
	 */
	public function setZasilkovnaPartnerOrganizationIds(array $zasilkovnaPartnerOrganizationIds)
	{
		$this->zasilkovnaPartnerOrganizationIds = $zasilkovnaPartnerOrganizationIds;
	}

	public function getAppleClientId(): string
	{
		return $this->appleClientId;
	}

	/**
	 * @param string $appleClientId
	 */
	public function setAppleClientId(string $appleClientId)
	{
		$this->appleClientId = $appleClientId;
	}

	/**
	 * @return string
	 */
	public function getAppleClientSecret(): string
	{
		return $this->appleClientSecret;
	}

	/**
	 * @param string $appleClientSecret
	 */
	public function setAppleClientSecret(string $appleClientSecret)
	{
		$this->appleClientSecret = $appleClientSecret;
	}

	/**
	 * @return string
	 */
	public function getGoogleWebClientId(): string
	{
		return $this->googleWebClientId;
	}

	/**
	 * @param string $googleWebClientId
	 */
	public function setGoogleWebClientId(string $googleWebClientId): void
	{
		$this->googleWebClientId = $googleWebClientId;
	}

	/**
	 * @return string
	 */
	public function getGoogleIOSClientId(Localization $localization): string
	{
		return $this->googleIOSClientId[$localization->getLocale()];
	}

	/**
	 * @param array $googleIOSClientId
	 */
	public function setGoogleIOSClientId(array $googleIOSClientId): void
	{
		$this->googleIOSClientId = $googleIOSClientId;
	}

	public function isImageCdnAllowed(): bool
	{
		return false;

//		if (Strings::contains($this->httpRequest->getUrl()->getAbsoluteUrl(), 'api/')) {
//			return false;
//		}
//
//        if (Strings::contains($this->httpRequest->getUrl()->getAbsoluteUrl(), 'admin/')) {
//            return false;
//        }
//
//		if (!self::isProductionEnvironment()) {
//			return false;
//		}
//
//		if (PHP_SAPI === 'cli') {
//			return false;
//		}
//
//		if ($this->httpRequest->getCookie('proxy-images') !== null || ($this->netteUser->isLoggedIn() && random_int(1, 2) === 1)) {
//			return true;
//		}
//
//		return false;
	}

	public function getDefaultConfirmationTreshold(Localization $localization)
	{
		switch ($localization->getLocale()) {
			case Localization::LOCALE_CZECH:
				return 100;
			case Localization::LOCALE_SLOVAK:
			case Localization::LOCALE_BULGARIA:
			case Localization::LOCALE_CROATIA:
			case Localization::LOCALE_SLOVENIA:
				return 4;
			case Localization::LOCALE_HUNGARIAN:
				return 1500;
			case Localization::LOCALE_ROMANIAN:
			case Localization::LOCALE_POLISH:
				return 20;
			default:
				throw new InvalidArgumentException('missing treshold');
		}
	}

	public function isThereMobileApp(Localization $localization)
	{
		return $localization->isCzech()
			|| $localization->isSlovak()
			|| $localization->isPolish()
			|| $localization->isRomanian()
			|| $localization->isBulgarian()
			|| $localization->isCroatian()
			|| $localization->isSlovenian()
			|| $localization->isHungarian()
		;
	}

	public function isThereAddon(Localization $localization): bool
	{
		return $localization->isCzech()
			|| $localization->isSlovak()
			|| $localization->isPolish()
			|| $localization->isRomanian()
			|| $localization->isBulgarian()
			|| $localization->isCroatian()
			|| $localization->isSlovenian()
			|| $localization->isHungarian()
		;
	}

	public function isThereLeaflets(Localization $localization): bool
	{
		return $localization->isCzech()
			|| $localization->isSlovak()
			|| $localization->isPolish()
			|| $localization->isRomanian()
			|| $localization->isHungarian();
	}

	public function getMobileAppStoreLink(Localization $localization, $platform)
	{
		if (!$this->isThereMobileApp($localization)) {
			throw new InvalidArgumentException('Mobile app not exists on ' . $localization->getName());
		}

		return $this->mobileAppStoreLinks[$platform][$localization->getLocale()];
	}

	public function setMobileAppStoreLinks(array $mobileAppStoreLinks)
	{
		$this->mobileAppStoreLinks = $mobileAppStoreLinks;
	}

	public function setFreshdeskGroupsIds(array $groups)
	{
		$this->freshdeskGroupsIds = $groups;
	}

	public function getFreshdeskGroupId(Localization $localization): int
	{
		if (!$this->freshdeskGroupsIds[$localization->getLocale()]) {
			throw new InvalidArgumentException(('unknown freshdesk group for localization: ' . $localization->getId()));
		}

		return $this->freshdeskGroupsIds[$localization->getLocale()];
	}

	public function setFreshdeskEmailConfigIds(array $emailConfigIds)
	{
		$this->freshdeskEmailConfigIds = $emailConfigIds;
	}

	public function getFreshdeskEmailConfigId(Localization $localization)
	{
		if (!$this->freshdeskEmailConfigIds[$localization->getLocale()]) {
			throw new InvalidArgumentException(('unknown freshdesk email config id: ' . $localization->getId()));
		}

		return $this->freshdeskEmailConfigIds[$localization->getLocale()];
	}

	public function getFioAccounts(): array
	{
		return $this->fioAccounts;
	}

	public function setFioAccounts(array $fioAccounts): void
	{
		$this->fioAccounts = $fioAccounts;
	}

	public function getTempDir(): string
	{
		return $this->tempDir;
	}

	public function setTempDir(string $tempDir): void
	{
		$this->tempDir = $tempDir;
	}

	/**
	 * @return array
	 */
	public function getWebsites(): array
	{
		return $this->websites;
	}

	/**
	 * @param array $websites
	 */
	public function setWebsites(array $websites): void
	{
		$this->websites = $websites;
	}

	/**
	 * @return string
	 */
	public function getMailchimpServer(): string
	{
		return $this->mailchimpServer;
	}

	/**
	 * @param string $mailchimpServer
	 */
	public function setMailchimpServer(string $mailchimpServer): void
	{
		$this->mailchimpServer = $mailchimpServer;
	}

	/**
	 * @return array
	 */
	public function getMailchimpLists(): array
	{
		return $this->mailchimpLists;
	}

	/**
	 * @param array $mailchimpLists
	 */
	public function setMailchimpLists(array $mailchimpLists): void
	{
		$this->mailchimpLists = $mailchimpLists;
	}

	/**
	 * @return string
	 */
	public function getMailchimpApiKey(): string
	{
		return $this->mailchimpApiKey;
	}

	/**
	 * @param string $mailchimpApiKey
	 */
	public function setMailchimpApiKey(string $mailchimpApiKey): void
	{
		$this->mailchimpApiKey = $mailchimpApiKey;
	}

	public function getMailchimpList(string $locale): string
	{
		if (!isset($this->mailchimpLists[$locale])) {
			throw new InvalidArgumentException('Mailchimp list for locale ' . $locale . ' not found');
		}

		return $this->mailchimpLists[$locale];
	}

	public function getMailchimpLocaleByListId(string $listId): string
	{
		$locale = array_search($listId, $this->mailchimpLists, true);
		if ($locale === false) {
			throw new InvalidArgumentException('Mailchimp locale for list ID ' . $listId . ' not found');
		}

		return $locale;
	}

	public function hasNewDesign(Localization $localization): bool
	{
		return true;
	}

	public function setMailkitId(int $mailkitId): void
	{
		$this->mailkitId = $mailkitId;
	}

	public function setMailkitMd5(string $mailkitMd5): void
	{
		$this->mailkitMd5 = $mailkitMd5;
	}

	public function setMailkitLists(array $mailkitLists): void
	{
		$this->mailkitLists = $mailkitLists;
	}

	public function getMailkitId(): int
	{
		return $this->mailkitId;
	}

	public function getMailkitMd5(): string
	{
		return $this->mailkitMd5;
	}

	public function getMailkitLists(): array
	{
		return $this->mailkitLists;
	}

	public function getMailkitLocaleByListId(int $listId): ?string
	{
		$locale = array_search($listId, $this->mailkitLists, true);
		if ($locale === false) {
			return null;
		}

		return $locale;
	}

	public function getMailkitList(string $locale): string
	{
		if (!isset($this->mailkitLists[$locale])) {
			throw new InvalidArgumentException('Mailkit list for locale ' . $locale . ' not found');
		}

		return $this->mailkitLists[$locale];
	}

	public function isThereLuckyShops(Localization $localization): bool
	{
		return $localization->isCzech()
			|| $localization->isSlovak()
			|| $localization->isPolish()
			|| $localization->isRomanian()
			|| $localization->isHungarian()
			|| $localization->isCroatian()
		;
	}
}
