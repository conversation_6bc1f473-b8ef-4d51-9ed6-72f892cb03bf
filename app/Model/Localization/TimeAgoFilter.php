<?php

namespace tipli\Model\Localization;

use Nette\Localization\Translator;
use DateTime;
use DateTimeInterface;

class TimeAgoFilter
{
	public function __construct(
		private readonly Translator $translator,
		private readonly LocalizationFacade $localizationFacade,
		private readonly DateTimeZoneResolver $dateTimeZoneResolver
	) {
	}

	/**
	 * Converts a DateTime object to a human-readable relative time format.
	 * For dates 4+ days old, shows regular date format instead of relative time.
	 * Uses aggressive rounding - e.g., 25 hours shows as "2 days ago".
	 *
	 * @param DateTimeInterface $dateTime The datetime to convert.
	 * @return string Translated time ago string or formatted date.
	 */
	public function __invoke(DateTimeInterface $dateTime): string
	{
		$now = new DateTime();
		$diff = $now->diff($dateTime);
		$totalHours = ($diff->days * 24) + $diff->h;
		$totalMinutes = ($totalHours * 60) + $diff->i;
		$totalSeconds = ($totalMinutes * 60) + $diff->s;

		if ($diff->y > 0 || $diff->m > 0 || $diff->d >= 4) {
			return $this->formatDate($dateTime);
		}

		// Aggressive rounding: if more than 24 hours, round up to days
		if ($totalHours >= 24) {
			$days = ceil($totalHours / 24);
			return $this->translator->translate('newFront.timeAgo.days', $days);
		}

		// Aggressive rounding: if more than 60 minutes, round up to hours
		if ($totalMinutes >= 60) {
			$hours = ceil($totalMinutes / 60);
			return $this->translator->translate('newFront.timeAgo.hours', $hours);
		}

		// Aggressive rounding: if more than 60 seconds, round up to minutes
		if ($totalSeconds >= 60) {
			$minutes = ceil($totalSeconds / 60);
			return $this->translator->translate('newFront.timeAgo.minutes', $minutes);
		}

		return $this->translator->translate('newFront.timeAgo.moments');
	}

	/**
	 * Format date according to current localization
	 *
	 * @param DateTimeInterface $dateTime
	 * @return string
	 */
	private function formatDate(DateTimeInterface $dateTime): string
	{
		$localization = $this->localizationFacade->getCurrentLocalization();
		$format = 'd.m.Y';

		if ($localization->isHungarian()) {
			$format = 'Y. n. j';
		}

		return $this->dateTimeZoneResolver->resolve($dateTime, $localization)->format($format);
	}
}
