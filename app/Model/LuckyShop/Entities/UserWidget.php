<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use tipli\Model\Marketing\Entities\Banner;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\UserWidgetRepository")
 * @ORM\Table(name="tipli_lucky_shop_user_widget", indexes={
 * @ORM\Index(name="user_date_idx", columns={"user_id", "valid_since", "valid_till"})
 * })
 */
class UserWidget
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false)
	 */
	private User $user;

	/**
	 * @ORM\ManyToOne(targetEntity="WidgetType")
	 * @ORM\JoinColumn(name="widget_type_id", referencedColumnName="id", nullable=false)
	 */
	private WidgetType $widgetType;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Marketing\Entities\Banner")
	 * @ORM\JoinColumn(name="banner_id", referencedColumnName="id", nullable=true)
	 */
	private ?Banner $banner = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validTill;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $priority;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $countOfImpressions = 1;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(User $user, WidgetType $widgetType, \DateTime $validSince, \DateTime $validTill, int $priority, ?Banner $banner = null)
	{
		$this->user = $user;
		$this->widgetType = $widgetType;
		$this->validSince = $validSince;
		$this->validTill = $validTill;
		$this->priority = $priority;
		$this->banner = $banner;
		$this->createdAt = new \DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getUser(): User
	{
		return $this->user;
	}

	public function getWidgetType(): WidgetType
	{
		return $this->widgetType;
	}

	public function getValidSince(): \DateTime
	{
		return $this->validSince;
	}

	public function getValidTill(): \DateTime
	{
		return $this->validTill;
	}

	public function setBanner(Banner $banner): void
	{
		$this->banner = $banner;
	}

	public function getBanner(): ?Banner
	{
		return $this->banner;
	}
}
