<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\Shops\Entities\Shop;

class UserLuckyShopRepository extends BaseRepository
{
	public function getUserLuckyShops(): QueryBuilder
	{
		return $this->createQueryBuilder('uls');
	}

	public function findUserLuckyShopBySource(User $user, string $source, bool $onlyValid): ?UserLuckyShop
	{
		$qb = $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', $source)
		;

		if ($onlyValid) {
			$qb->andWhere('uls.validSince <= :now')
				->andWhere('uls.validTill >= :now')
				->setParameter('now', new \DateTime());
		}

		return $qb->getQuery()
			->setMaxResults(1)
			->getOneOrNullResult()
		;
	}

	public function findUserLuckyShops(Localization $localization, \DateTime $validSince, \DateTime $validTill, ?int $limit = null)
	{
		$qb = $this->getUserLuckyShops()
			->andWhere('uls.validSince <= :validSince')
			->setParameter('validSince', $validSince)
			->andWhere('uls.validTill >= :validTill')
			->setParameter('validTill', $validTill)
			->andWhere('uls.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('uls.shop IS NOT NULL')
			->addGroupBy('uls.shop')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()->getResult();
	}

	public function findUserLuckyShopsForLuckyShop(User $user, LuckyShop $luckyShop)
	{
		$luckyShopProcessedAt = $luckyShop->getValidSince();

		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.validSince <= :processedAt')
			->andWhere('uls.validTill >= :processedAt')
			->setParameter('processedAt', $luckyShopProcessedAt)
			->andWhere('uls.shop IS NOT NULL')
			->getQuery()
			->getResult();
	}

	public function findValidUserLuckyShops(User $user, bool $onlyWithoutShop = false, bool $onlyWithShop = false, ?string $source = null)
	{
		$qb = $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.validTill >= :now')
			->setParameter('now', new \DateTime())
			->addOrderBy('uls.originalValidSince', 'ASC');

		if ($onlyWithoutShop) {
			$qb->andWhere('uls.shop IS NULL');
		}

		if ($onlyWithShop) {
			$qb->andWhere('uls.shop IS NOT NULL');
		}

		if ($source !== null) {
			$qb->andWhere('uls.source = :source')
				->setParameter('source', $source);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findUserLuckyShopsForCampaign(User $user, LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.validSince <= :validSince')
			->setParameter('validSince', $luckyShopCampaign->getLastLuckyShopValidSince())
			->andWhere('uls.validTill >= :validTill')
			->setParameter('validTill', $luckyShopCampaign->getLastLuckyShopValidSince())
			->getQuery()
			->getResult();
	}

	public function findValidUserLuckyShopByShop(User $user, Shop $shop, \DateTime $luckyShopProcessedAt): ?UserLuckyShop
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('uls.validSince <= :validSince')
			->setParameter('validSince', $luckyShopProcessedAt)
			->andWhere('uls.validTill >= :validTill')
			->setParameter('validTill', $luckyShopProcessedAt)
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findUserLuckyShopForLuckyShop(User $user, LuckyShop $luckyShop)
	{
		$shop = $luckyShop->getShop();

		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('uls.validSince <= :validSince')
			->setParameter('validSince', $luckyShop->getValidSince())
			->andWhere('uls.validTill >= :validTill')
			->setParameter('validTill', $luckyShop->getValidSince())
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findUsersLuckyShopsForShop(Shop $shop, \DateTime $validAt)
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('uls.validSince <= :validSince')
			->setParameter('validSince', $validAt)
			->andWhere('uls.validTill >= :validTill')
			->setParameter('validTill', $validAt)
			->getQuery()
			->getResult()
		;
	}

	public function findDefaultUserLuckyShopForUser(User $user): ?UserLuckyShop
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', UserLuckyShop::SOURCE_DEFAULT)
			->andWhere('uls.validTill >= :now')
			->setParameter('now', new \DateTime())
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findValidUserLuckyShopsWithoutShop(User $user)
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.validTill >= :now')
			->setParameter('now', new \DateTime())
			->andWhere('uls.shop IS NULL')
			->getQuery()
			->getResult()
		;
	}

	public function findValidUserLuckyShopWithoutShop(User $user, ?string $source = null): ?UserLuckyShop
	{
		$qb = $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.validTill >= :now')
			->setParameter('now', new \DateTime())
			->andWhere('uls.shop IS NULL');

		if ($source) {
			$qb->andWhere('uls.source = :source')
				->setParameter('source', $source);
		}

		$qb->setMaxResults(1);

		return $qb->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findUserLuckyShopByOriginalValidSince(User $user, string $source, \DateTime $originalValidSince)
	{
		$startOfDay = (clone $originalValidSince)->setTime(0, 0, 0);
		$endOfDay = (clone $originalValidSince)->setTime(23, 59, 59);

		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', $source)
			->andWhere('uls.originalValidSince >= :startOfDay')
			->setParameter('startOfDay', $startOfDay)
			->andWhere('uls.originalValidSince <= :endOfDay')
			->setParameter('endOfDay', $endOfDay)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findUserLuckyShopBySourceAndDate(User $user, string $source, \DateTime $date): ?UserLuckyShop
	{
		$startOfDay = (clone $date)->setTime(0, 0, 0);
		$endOfDay = (clone $date)->setTime(23, 59, 59);

		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', $source)
			->andWhere('uls.createdAt >= :startOfDay')
			->setParameter('startOfDay', $startOfDay)
			->andWhere('uls.createdAt <= :endOfDay')
			->setParameter('endOfDay', $endOfDay)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findAllUserLuckyShops(User $user)
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->addOrderBy('uls.originalValidSince', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findLatestUserLuckyShopBySource(User $user, string $source): ?UserLuckyShop
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', $source)
			->addOrderBy('uls.originalValidSince', 'DESC')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findLastUserLuckyShopBySource(User $user, string $source): ?UserLuckyShop
	{
		return $this->getUserLuckyShops()
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->andWhere('uls.source = :source')
			->setParameter('source', $source)
			->addOrderBy('uls.originalValidSince', 'DESC')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}
}
