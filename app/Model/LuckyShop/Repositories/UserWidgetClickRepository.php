<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\LuckyShop\Entities\WidgetType;

class UserWidgetClickRepository extends BaseRepository
{
	public function getUserWidgetClicks(): QueryBuilder
	{
		return $this->createQueryBuilder('uwc')
			->innerJoin('uwc.widgetType', 'wt')
			->addSelect('wt');
	}

	public function findUserWidgetClicksByUser(User $user): array
	{
		return $this->getUserWidgetClicks()
			->andWhere('uwc.user = :user')
			->setParameter('user', $user)
			->addOrderBy('uwc.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findUserWidgetClicksByUserAndType(User $user, WidgetType $widgetType): array
	{
		return $this->getUserWidgetClicks()
			->andWhere('uwc.user = :user')
			->setParameter('user', $user)
			->andWhere('uwc.widgetType = :widgetType')
			->setParameter('widgetType', $widgetType)
			->addOrderBy('uwc.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}
}
