<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\UserWidgetClick;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\Doctrine\EntityManager;

class UserWidgetClickManager
{
	public function __construct(private EntityManager $em)
	{
	}

	public function createUserWidgetClick(User $user, UserWidget $userWidget, WidgetType $widgetType): UserWidgetClick
	{
		$userWidgetClick = new UserWidgetClick($user, $userWidget, $widgetType);

		$this->saveUserWidgetClick($userWidgetClick);

		return $userWidgetClick;
	}

	public function saveUserWidgetClick(UserWidgetClick $userWidgetClick): UserWidgetClick
	{
		$this->em->persist($userWidgetClick);
		$this->em->flush($userWidgetClick);

		return $userWidgetClick;
	}
}
