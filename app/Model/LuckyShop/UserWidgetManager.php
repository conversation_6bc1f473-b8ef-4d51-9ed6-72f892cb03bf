<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Marketing\Entities\Banner;

class UserWidgetManager
{
	public function __construct(private EntityManager $em)
	{
	}

	public function createUserWidget(User $user, WidgetType $widget, \DateTime $validSince, \DateTime $validTill, int $priority, ?Banner $banner = null): UserWidget
	{
		$userWidget = new UserWidget($user, $widget, $validSince, $validTill, $priority, $banner);

		$this->saveUserWidget($userWidget);

		return $userWidget;
	}

	public function saveUserWidget(UserWidget $userWidget): UserWidget
	{
		$this->em->persist($userWidget);
		$this->em->flush($userWidget);

		return $userWidget;
	}
}
