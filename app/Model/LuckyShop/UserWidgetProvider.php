<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\UserWidgetRepository;
use tipli\Model\Marketing\BannerFacade;

class UserWidgetProvider
{
	public function __construct(
		private readonly WidgetResolver $widgetResolver,
		private readonly WidgetFacade $widgetFacade,
		private readonly UserWidgetRepository $userWidgetRepository,
		private readonly BannerFacade $bannerFacade
	) {
	}

	/** @return array<int, UserWidget> */
	public function provideUserWidgets(User $user, \DateTime $validSince, \DateTime $validTill): array
	{
		$userWidgets = $this->userWidgetRepository->findUserWidgets($user, $validSince, $validTill);

		if (!empty($userWidgets)) {
			return $userWidgets;
		}

		$allowedWidgetTypes = $this->widgetResolver->resolveAllowedWidgetTypesForUser($user, $validSince, true);

		$newUserWidgets = [];

		foreach ($allowedWidgetTypes as $widgetType) {
			if ($widgetType->getType() === WidgetType::TYPE_TIP) {
				$banner = $this->bannerFacade->findValidBannerForWidget($user->getLocalization(), $validTill);

				if ($banner === null) {
					continue;
				}
			}

			$userWidget = $this->widgetFacade->createUserWidget(
				$user,
				$widgetType,
				$validSince,
				$validTill,
				$widgetType->getPriority(),
				$banner ?? null
			);

			$newUserWidgets[] = $userWidget;
		}

		return $newUserWidgets;
	}
}
