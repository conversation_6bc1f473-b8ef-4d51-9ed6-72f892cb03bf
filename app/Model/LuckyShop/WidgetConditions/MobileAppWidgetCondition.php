<?php

namespace tipli\Model\LuckyShop\WidgetConditions;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\WidgetType;

class MobileAppWidgetCondition implements WidgetConditionInterface
{
	public function isAllowed(User $user, WidgetType $widgetType): bool
	{
		$lastMobileAppVisitAt = $user->getSegmentData()->getLastMobileAppVisitAt();

		return $lastMobileAppVisitAt === null || $lastMobileAppVisitAt < (new \DateTime('-30 days'));
	}
}
