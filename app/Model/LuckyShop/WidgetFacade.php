<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\UserWidgetClick;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\UserWidgetRepository;
use tipli\Model\Marketing\Entities\Banner;

class WidgetFacade
{
	public function __construct(
		private readonly UserWidgetManager $userWidgetManager,
		private readonly UserWidgetRepository $userWidgetRepository,
		private readonly UserWidgetClickManager $userWidgetClickManager
	) {
	}

	public function createUserWidget(User $user, WidgetType $widget, \DateTime $validSince, \DateTime $validTill, int $priority, ?Banner $banner): UserWidget
	{
		return $this->userWidgetManager->createUserWidget($user, $widget, $validSince, $validTill, $priority, $banner);
	}

	public function findUserWidgetById(int $id): ?UserWidget
	{
		return $this->userWidgetRepository->find($id);
	}

	public function findUserWidget(User $user, WidgetType $widget, \DateTime $validSince, \DateTime $validTill): ?UserWidget
	{
		return $this->userWidgetRepository->findUserWidget($user, $widget, $validSince, $validTill);
	}

	public function createUserWidgetClick(User $user, UserWidget $userWidget, WidgetType $widgetType): UserWidgetClick
	{
		return $this->userWidgetClickManager->createUserWidgetClick($user, $userWidget, $widgetType);
	}
}
