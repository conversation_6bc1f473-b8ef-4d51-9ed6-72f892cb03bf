<?php

namespace tipli\Model\Marketing;

use DateTime;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Marketing\Repositories\BannerClickRepository;
use tipli\Model\Marketing\Repositories\BannerRepository;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Repositories\TransactionRepository;

class BannerFacade
{
	public function __construct(
		private readonly BannerManager $bannerManager,
		private readonly BannerRepository $bannerRepository,
		private readonly TransactionRepository $transactionRepository,
		private readonly BannerClickRepository $bannerClickRepository
	) {
	}

	public function findBanners()
	{
		return $this->bannerRepository->findBanners();
	}

	public function getBanners()
	{
		return $this->bannerRepository->getBanners();
	}

	public function findValidBannerForWidget(Localization $localization, DateTime $validTill): ?Banner
	{
		return $this->bannerRepository->findValidBannerForWidget($localization, $validTill);
	}

	public function findValidBanners(Localization $localization, $limit = 2, $format = Banner::FORMAT_HOMEPAGE, $segment = null, $size = null, $exceptBanners = [], ?User $user = null, ?Shop $shop = null, $visibleOnMobileApp = null, $visibleOnWeb = null, bool $personalized = false)
	{
		$moneyRewardCampaigns = $user !== null ? $user->getValidMoneyRewardCampaigns() : [];

		$result = $this->bannerRepository->findValidBanners(
			$localization,
			$limit,
			$format,
			$segment,
			$size,
			$exceptBanners,
			$user,
			$shop,
			$visibleOnMobileApp,
			$visibleOnWeb,
			$moneyRewardCampaigns
		);

		if ($personalized && $user) {
			$this->applyPersonalization($result, $user);
		}

		return $result;
	}

	/**
	 * @param Banner[] $banners
	 * @param User $user
	 * @return void
	 */
	private function applyPersonalization(array &$banners, User $user): void
	{
		$userId = $user->getId();
		$bannerIds = array_map(static fn ($banner) => $banner->getId(), $banners);
		$shopIds = array_reduce($banners, static function ($result, $banner) {
			$result[$banner->getShopId()] = $banner->getShopId();
			return $result;
		}, []);
		$clickedBannerIds = $this->bannerClickRepository->findClickedBannersByUser($userId, $bannerIds);
		$shopTransactions = $this->transactionRepository->findUserTransactionsByShopIds($userId, $shopIds);

		$scores = [];

		foreach ($banners as $banner) {
			$score = 0;
			if ($banner === reset($banners)) {
				$score += 2;
			}

			if (in_array($banner->getId(), $clickedBannerIds)) {
				$score -= 2;
			}

			if ($banner->getShop() && in_array($banner->getShop()->getId(), $shopTransactions)) {
				$score += 1;
			}

			$scores[$banner->getId()] = $score;
		}

		usort($banners, static function (Banner $a, Banner $b) use ($scores) {
			$scoreA = $scores[$a->getId()] ?? 0;
			$scoreB = $scores[$b->getId()] ?? 0;
			return $scoreB <=> $scoreA;
		});
	}

	public function find($id)
	{
		return $this->bannerRepository->find($id);
	}

	public function createBanner(Localization $localization, $name, $link, DateTime $validSince, DateTime $validTill, $priority, User $user, $segment = null, Shop $shop = null)
	{
		return $this->bannerManager->createBanner($localization, $name, $link, $validSince, $validTill, $priority, $user, $segment, $shop);
	}

	public function saveBanner(Banner $banner)
	{
		return $this->bannerManager->saveBanner($banner);
	}

	public function removeBanner(Banner $banner)
	{
		$this->resetBannerGroups($banner);

		return $this->bannerManager->removeBanner($banner);
	}

	public function trackClick(Banner $banner, User $user = null, string $source = null, string $platform = null)
	{
		$this->bannerManager->trackClick($banner, $user, $source, $platform);
	}

	public function findByIds(array $ids)
	{
		return $this->bannerRepository->findByIds($ids);
	}

	public function findGroupedBanners(Banner $banner)
	{
		return $this->getBanners()
			->where('b.groupIdentifier = :groupIdentifier')->setParameter('groupIdentifier', $banner->getGroupIdentifier())
			->getQuery()->getResult();
	}

	public function searchBanners(?Localization $localization = null, ?string $term = null, int $limit = 15): array
	{
		$data = [];

		$qb = $this->getBanners()
			->andWhere('b.groupIdentifier IS NULL')
			->setMaxResults($limit);

		if ($term) {
			$qb->andWhere('b.name LIKE :term')
				->setParameter('term', '%' . $term . '%');
		}

		if ($localization !== null) {
			$qb->andWhere('b.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function searchBannerByName(?Banner $banner, $term)
	{
		$data = [];

		$qb = $this->getBanners()
			->andWhere('b.groupIdentifier IS NULL')
			->setMaxResults(15);

		if ($term) {
			$qb->andWhere('b.name LIKE :term')->setParameter('term', '%' . $term . '%');
		}

		if ($banner) {
			$qb->andWhere('b.localization = :localization')->setParameter('localization', $banner->getLocalization())
				->andWhere('b.id != :notSelf')->setParameter('notSelf', $banner->getId());
		}

		$result = $qb->getQuery()->getResult();

		if ($result) {
			/** @var Banner $item */
			foreach ($result as $item) {
				$data[$item->getId()] = ['name' => $item->getName()];
			}
		}

		return $data;
	}

	public function resetBannerGroups(Banner $banner)
	{
		$groupedBanners = $this->findGroupedBanners($banner);

		/** @var Banner $banner */
		foreach ($groupedBanners as $banner) {
			$banner->setGroupIdentifier(null);
			$this->saveBanner($banner);
		}
	}

	public function findValidBannersByLocalization(Localization $localization, $size = null)
	{
		$qb = $this->getBanners()
			->andWhere('b.localization = :localization')->setParameter('localization', $localization)
			->andWhere('b.validSince <= CURRENT_TIMESTAMP()')
			->andWhere('b.validTill >= CURRENT_TIMESTAMP()');

		if ($size) {
			$qb->andWhere('b.size = :size')->setParameter('size', $size);
		}

		return $qb->getQuery()->getResult();
	}
}
