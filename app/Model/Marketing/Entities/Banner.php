<?php

namespace tipli\Model\Marketing\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Rewards\Entities\MoneyRewardCampaign;
use tipli\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="\tipli\Model\Marketing\Repositories\BannerRepository")
 * @ORM\Table(name="tipli_marketing_banner")
 * })
 */
class Banner
{
	public const FORMAT_HOMEPAGE = 'homepage';
	public const FORMAT_HOMEPAGE2 = 'homepage2';

	public const FORMAT_HOMEPAGE_2024 = 'homepage2024';
	public const FORMAT_DEALS_2024 = 'deals2024';
	public const FORMAT_MOBILE_APP_ACCOUNT = 'mobile_app_account';
	public const FORMAT_NAVIGATION_LINK = 'navigation_link';
	public const FORMAT_LP = 'landing_page';
	public const FORMAT_NEWSLETTER = 'newsletter';

	public const SEGMENT_NEW = 'new';
	public const SEGMENT_ACTIVE = 'active';

	public const SIZE_FULL = 'full';
	public const SIZE_HALF = 'half';
	public const SIZE_THIRD = 'third';
	public const SIZE_MINI = 'mini';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Rewards\Entities\MoneyRewardCampaign")
	 * @ORM\JoinColumn(name="money_reward_campaign_id", referencedColumnName="id")
	 */
	private ?MoneyRewardCampaign $moneyRewardCampaign;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 */
	private $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Deals\Entities\Deal")
	 * @ORM\JoinColumn(name="deal_id", referencedColumnName="id", nullable=true)
	 */
	private ?Deal $deal;

	/**
	 * @ORM\Column(type="string")
	 */
	private $format = self::FORMAT_HOMEPAGE;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $size = self::SIZE_FULL;

	/**
	 * @ORM\Column(type="string")
	 */
	private $name;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private ?string $description = null;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EAGER")
	 * @ORM\JoinColumn(name="image_id", referencedColumnName="id")
	 */
	private $image;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EAGER")
	 * @ORM\JoinColumn(name="image_mobile_id", referencedColumnName="id")
	 */
	private $imageMobile;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $link;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $openInNewWindow = true;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private ?string $conditions;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $countOfClicks = 0;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $gaCountOfClicks = 0;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $gaCountOfViews = 0;

	/**
	 * @ORM\Column(type="float")
	 */
	private $gaCtr = 0;

	/**
	 * @ORM\Column(type="float")
	 */
	private $totalCommissionAmount = 0;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $countOfTransactions = 0;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $segment;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $showLogo = false;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private $groupIdentifier;

	/**
	 * @ORM\Column(type="integer", options={"default": 1})
	 */
	private $countInGroup = 1;

	/**
	 * @ORM\Column(type="integer", options={"default": 1})
	 */
	private $numberInGroup = 1;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $visibleMobileApp = true;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $visibleWeb = true;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="created_by_user_id", referencedColumnName="id")
	 * @var User
	 */
	private User $createdBy;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Marketing\Entities\BannerClick", mappedBy="banner")
	 */
	private $clicks;

	public function __construct(
		Localization $localization,
		$name,
		$link,
		DateTime $validSince,
		DateTime $validTill,
		$priority,
		$segment = null
	) {
		$this->localization = $localization;
		$this->name = $name;
		$this->link = $link;
		$this->validSince = $validSince;
		$this->validTill = $validTill;
		$this->priority = $priority;
		$this->segment = $segment;
		$this->createdAt = new DateTime();
		$this->shop = null;
		$this->clicks = new ArrayCollection();
	}

  /**
   * @return int
   */
	public function getId(): int
	{
		return $this->id;
	}

  /**
   * @return Localization
   */
	public function getLocalization(): Localization
	{
		return $this->localization;
	}

  /**
   * @return String
   */
	public function getFormat(): string
	{
		return $this->format;
	}

  /**
   * @param String $format
   */
	public function setFormat(string $format)
	{
		$this->format = $format;
	}

  /**
   * @return String
   */
	public function getName(): string
	{
		return $this->name;
	}

  /**
   * @param String $name
   */
	public function setName(string $name)
	{
		$this->name = $name;
	}

	/**
	 * @return string|null
	 */
	public function getLink(): ?string
	{
		return $this->link;
	}

	/**
	 * @param string|null $link
	 */
	public function setLink(?string $link = null)
	{
		$this->link = $link;
	}

	/**
	 * @return mixed
	 */
	public function getOpenInNewWindow()
	{
		return $this->openInNewWindow;
	}

	/**
	 * @param mixed $openInNewWindow
	 */
	public function setOpenInNewWindow($openInNewWindow)
	{
		$this->openInNewWindow = $openInNewWindow;
	}

  /**
   * @return DateTime
   */
	public function getValidSince(): \DateTime
	{
		return $this->validSince;
	}

  /**
   * @param DateTime $validSince
   */
	public function setValidSince(\DateTime $validSince)
	{
		$this->validSince = $validSince;
	}

  /**
   * @return DateTime
   */
	public function getValidTill(): \DateTime
	{
		return $this->validTill;
	}

  /**
   * @param DateTime $validTill
   */
	public function setValidTill(\DateTime $validTill)
	{
		$this->validTill = $validTill;
	}

  /**
   * @return int
   */
	public function getPriority(): int
	{
		return $this->priority;
	}

  /**
   * @param int $priority
   */
	public function setPriority(int $priority)
	{
		$this->priority = $priority;
	}

  /**
   * @return mixed
   */
	public function getSegment()
	{
		return $this->segment;
	}

	/**
	 * @return Image
	 */
	public function getImage()
	{
		return $this->image;
	}

	public function setImage(Image $image)
	{
		$this->image = $image;
	}

	/**
	 * @return Image
	 */
	public function getImageMobile()
	{
		return $this->imageMobile;
	}

	/**
	 * @param mixed $imageMobile
	 */
	public function setImageMobile($imageMobile)
	{
		$this->imageMobile = $imageMobile;
	}

	public function getShowLogo()
	{
		return $this->showLogo;
	}

	public function setShowLogo(bool $showLogo)
	{
		$this->showLogo = $showLogo;
	}

  /**
   * @param mixed $segment
   */
	public function setSegment($segment)
	{
		$this->segment = $segment;
	}

	public function getShopId()
	{
		return $this->shop->getId();
	}

	public function setShop(Shop $shop)
	{
		$this->shop = $shop;
	}

	public function getDeal(): ?Deal
	{
		return $this->deal;
	}

	public function setDeal(?Deal $deal): void
	{
		$this->deal = $deal;
	}

	public function getConditions(): ?string
	{
		return $this->conditions;
	}

	public function setConditions(?string $conditions): void
	{
		$this->conditions = $conditions;
	}

	public function isValid()
	{
		return $this->validSince <= new DateTime() && $this->validTill >= new DateTime();
	}

	public function getFormatName()
	{
		return self::getFormats()[$this->format];
	}

	public static function getFormats()
	{
		return [
			self::FORMAT_HOMEPAGE => 'Domovská stránka',
			self::FORMAT_HOMEPAGE2 => 'Domovská stránka 2',
			self::FORMAT_HOMEPAGE_2024 => 'Homepage 2024',
			self::FORMAT_DEALS_2024 => 'Slevy 2024',
			self::FORMAT_MOBILE_APP_ACCOUNT => 'Mob. aplikace - sekce můj účet',
			self::FORMAT_NAVIGATION_LINK => 'Odkaz v menu',
			self::FORMAT_LP => 'Landing page',
			self::FORMAT_NEWSLETTER => 'Newsletter generator',
		];
	}

	public static function getSegments()
	{
		return [
			null => 'Všichni',
			self::SEGMENT_ACTIVE => 'Aktivní',
			self::SEGMENT_NEW => 'Neaktivní',
		];
	}

	/**
	 * @return mixed
	 */
	public function getCountOfClicks()
	{
		return $this->countOfClicks;
	}

	public function getCreatedBy(): User
	{
		return $this->createdBy;
	}

	public function setCreatedBy(User $createdBy): void
	{
		$this->createdBy = $createdBy;
	}

	/**
	 * @return mixed
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getSize()
	{
		return $this->size;
	}

	public static function getSizes()
	{
		return [
			self::SIZE_FULL => 'Plná (1164x568px) ',
			self::SIZE_HALF => 'Poloviční (568x568px)',
			self::SIZE_THIRD => 'Třetina (200x200px)',
			self::SIZE_MINI => 'Mini',
		];
	}

	public static function getSizeValues($size)
	{
		if ($size === self::SIZE_FULL) {
			return [1164, 568];
		}

		if ($size === self::SIZE_HALF) {
			return [568, 568];
		}

		if ($size === self::SIZE_THIRD) {
			return [200, 200];
		}
	}

	/**
	 * @param mixed $size
	 */
	public function setSize($size = null)
	{
		if ($size) {
			$sizes = self::getSizes();

			if (!isset($sizes[$size])) {
				throw new InvalidArgumentException('Invalid size.');
			}
		}

		$this->size = $size;
	}

	/**
	 * @return mixed
	 */
	public function getGaCountOfViews()
	{
		return $this->gaCountOfViews;
	}

	/**
	 * @param mixed $gaCountOfViews
	 */
	public function setGaCountOfViews($gaCountOfViews)
	{
		$this->gaCountOfViews = $gaCountOfViews;
	}

	/**
	 * @return mixed
	 */
	public function getGaCtr()
	{
		return $this->gaCtr;
	}

	/**
	 * @param string $gaCtr
	 */
	public function setGaCtr($gaCtr)
	{
		$this->gaCtr = $gaCtr;
	}

	/**
	 * @return mixed
	 */
	public function getGaCountOfClicks()
	{
		return $this->gaCountOfClicks;
	}

	/**
	 * @param mixed $gaCountOfClicks
	 */
	public function setGaCountOfClicks($gaCountOfClicks)
	{
		$this->gaCountOfClicks = $gaCountOfClicks;
	}

	/**
	 * @return Shop|null
	 */
	public function getShop()
	{
		return $this->shop;
	}

	/**
	 * @return mixed
	 */
	public function getTotalCommissionAmount()
	{
		return $this->totalCommissionAmount;
	}

	/**
	 * @return mixed
	 */
	public function getCountOfTransactions()
	{
		return $this->countOfTransactions;
	}

	/**
	 * @return mixed
	 */
	public function getGroupIdentifier()
	{
		return $this->groupIdentifier;
	}

	/**
	 * @param mixed $groupIdentifier
	 */
	public function setGroupIdentifier($groupIdentifier)
	{
		$this->groupIdentifier = $groupIdentifier;
	}

	/**
	 * @return mixed
	 */
	public function getCountInGroup()
	{
		return $this->countInGroup;
	}

	/**
	 * @param mixed $countInGroup
	 */
	public function setCountInGroup($countInGroup)
	{
		$this->countInGroup = $countInGroup;
	}

	/**
	 * @return mixed
	 */
	public function getNumberInGroup()
	{
		return $this->numberInGroup;
	}

	/**
	 * @param mixed $numberInGroup
	 */
	public function setNumberInGroup($numberInGroup)
	{
		$this->numberInGroup = $numberInGroup;
	}

	public static function getGroupColorHex($groupIdentifier)
	{
		$colors = [
			'#811cfc',
			'#056ceb',
			'#8acf1b',
			'#f28e02',
			'#7468c4',
			'#07bbdb',
			'#af3eb5',
			'#09bda2',
			'#e83a0e',
			'#09bd2a',
		];

		return $colors[$groupIdentifier % count($colors)];
	}

	/**
	 * @return bool
	 */
	public function isVisibleMobileApp(): bool
	{
		return $this->visibleMobileApp;
	}

	/**
	 * @param bool $visibleMobileApp
	 */
	public function setVisibleMobileApp(bool $visibleMobileApp)
	{
		$this->visibleMobileApp = $visibleMobileApp;
	}

	/**
	 * @return bool
	 */
	public function isVisibleWeb(): bool
	{
		return $this->visibleWeb;
	}

	/**
	 * @param bool $visibleWeb
	 */
	public function setVisibleWeb(bool $visibleWeb): void
	{
		$this->visibleWeb = $visibleWeb;
	}

	public function toDocument()
	{
		$data =  [
			'name' => $this->getName(),
			'shop' => $this->getShop(),
			'link' => $this->getLink(),
			'image' => $this->getImage() ? $this->getImage()->getIdentifier() : '',
			'imageMobile' => $this->getImageMobile() ? $this->getImageMobile()->getIdentifier() : '',
			'openInNewWindow' => $this->getOpenInNewWindow(),
			'validSince' => $this->getValidSince()->format('d.m.Y H:i:s'),
			'validTill' => $this->getValidTill()->format('d.m.Y H:i:s'),
			'format' => $this->getFormat(),
			'size' => $this->getSize(),
			'segment' => $this->getSegment(),
			'visibleMobileApp' => $this->isVisibleMobileApp(),
			'shopLogoOnBanner' => $this->getShowLogo(),
			'priority' => $this->getPriority(),
		];

		$document = [];
		foreach ($data as $key => $value) {
			$document[] = $key . ":\n" . $value;
		}

		$document = implode("\n\n***\n\n", $document);

		return $document;
	}

	public function getMoneyRewardCampaign(): ?MoneyRewardCampaign
	{
		return $this->moneyRewardCampaign;
	}

	public function setMoneyRewardCampaign(?MoneyRewardCampaign $moneyRewardCampaign): void
	{
		$this->moneyRewardCampaign = $moneyRewardCampaign;
	}

	/**
	 * @return string
	 */
	public function getRedirectionLink(): string
	{
		return $this->getShop() !== null ? $this->getShop()->getPartnerSystemRedirectUrl() : $this->getLink();
	}

	public function setDescription(?string $description): void
	{
		$this->description = $description;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}
}
