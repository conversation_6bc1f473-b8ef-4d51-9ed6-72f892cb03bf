<?php

namespace tipli\Model\Marketing\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Shops\Entities\Shop;

class BannerRepository extends BaseRepository
{
	public function findValidBanners(
		Localization $localization,
		$limit = 2,
		$format = Banner::FORMAT_HOMEPAGE,
		$segment = null,
		$size = null,
		$exceptBanners = [],
		?User $user = null,
		?Shop $shop = null,
		$visibleOnMobileApp = null,
		$visibleOnWeb = null,
		array $moneyRewardCampaigns = []
	) {
		$qb = $this->createQueryBuilder('b');

		$qb->andWhere('b.localization = :localization')
			->setParameter('localization', $localization);

		$qb->andWhere('b.validSince <= CURRENT_TIMESTAMP()');
		$qb->andWhere('b.validTill >= CURRENT_TIMESTAMP()');

		$qb->andWhere('b.format = :format')
			->setParameter('format', $format);

		$qb->addOrderBy('b.priority', 'desc');

		if ($segment) {
			$qb->andWhere('b.segment = :segment OR b.segment IS NULL')
				->setParameter('segment', $segment);
		} else {
			$qb->andWhere('b.segment IS NULL');
		}

		if ($size) {
			$qb->andWhere('b.size = :size')
				->setParameter('size', $size);
		} else {
			$qb->andWhere('b.size IS NULL');
		}

		if ($exceptBanners) {
			$qb->andWhere('b.id NOT IN (:exceptBanners)')
				->setParameter('exceptBanners', $exceptBanners);
		}

		$qb->andWhere('(b.moneyRewardCampaign IS NULL OR b.moneyRewardCampaign IN (:moneyRewardCampaigns))')
			->setParameter('moneyRewardCampaigns', $moneyRewardCampaigns);
//
//        if ($user) {
//            $qb->andWhere('MOD(:userId, b.countInGroup) = (b.numberInGroup - 1)')
//                ->setParameter('userId', $user->getId());
//        }

		if ($shop) {
			$qb->andWhere('b.shop = :shop')
				->setParameter('shop', $shop);
		}

		if ($visibleOnMobileApp) {
			$qb->andWhere('b.visibleMobileApp = true');
		}

		if ($visibleOnWeb) {
			$qb->andWhere('b.visibleWeb = true');
		}

		$qb->setMaxResults($limit);

		return $qb->getQuery()->getResult();
	}

	public function findValidBannerById($id)
	{
		$qb = $this->createQueryBuilder('b');

		$qb->andWhere('b.id = :id')
			->setParameter('id', $id);

		$qb->andWhere('b.validSince <= CURRENT_TIMESTAMP()');
		$qb->andWhere('b.validTill >= CURRENT_TIMESTAMP()');

		$qb->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findBanners()
	{
		return $this->getBanners()->getQuery()->getResult();
	}

	public function getBanners()
	{
		$qb = $this->createQueryBuilder('b');

		return $qb;
	}

	public function findByIds($ids): array
	{
		return $this->getBanners()
			->andWhere('b.id IN (:ids)')
			->addOrderBy('FIELD(b.id, :ids)')
			->setParameter('ids', $ids)
			->getQuery()
			->getResult();
	}

	public function findValidBannerForWidget(Localization $localization, \DateTime $validTill): ?Banner
	{
		return $this->getBanners()
			->andWhere('b.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('b.validTill >= :validTill')
			->setParameter('validTill', $validTill)
			->andWhere('b.validSince <= :validSince')
			->setParameter('validSince', new \DateTime())
			->andWhere('b.format = :format')
			->setParameter('format', Banner::FORMAT_HOMEPAGE_2024)
			->andWhere('b.size = :size')
			->setParameter('size', Banner::SIZE_MINI)
			->andWhere('b.description IS NOT NULL')
			->addOrderBy('b.priority', 'desc')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}
}
