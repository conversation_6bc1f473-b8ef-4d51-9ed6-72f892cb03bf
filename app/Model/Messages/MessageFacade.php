<?php

namespace tipli\Model\Messages;

use tipli\Model\Account\Entities\Change;
use tipli\Model\Account\Entities\User;
use tipli\Model\Campaign\Entities\CampaignSubscription;
use tipli\Model\Freshdesk\Entities\Ticket;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Marketing\DataObjects\EmailDataObject;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\Sms;
use tipli\Model\Messages\Repositories\EmailRepository;
use tipli\Model\Messages\Repositories\SmsRepository;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Reviews\Entities\ReviewRequest;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Rewards\Entities\ShareReward;
use tipli\Model\Rewards2\Entities\UserReward;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Transactions\Entities\Transaction;
use Tracy\Debugger;

class MessageFacade
{
	public function __construct(
		public EmailFactory $emailFactory,
		public EmailTemplateFactory $emailTemplateFactory,
		public EmailManager $emailManager,
		public SmsManager $smsManager,
		public SmsFactory $smsFactory,
		public EmailRepository $emailRepository,
		public SmsRepository $smsRepository,
		public MandrillManager $mandrillManager,
		public OneSignalNotificationManager $oneSignalNotificationManager
	) {
	}

	public function getSentEmails()
	{
		return $this->emailManager->getSentEmails();
	}

	public function find($id)
	{
		return $this->emailManager->find($id);
	}

	public function sendTellFriendEmail(User $recommender, $friendEmail)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createTellFriendEmail($recommender, $friendEmail)
		);
	}

	public function sendRegistrationEmail(User $user)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createRegistrationEmail($user)
		);
	}

	public function sendRegistrationRecommenderEmail(User $user, User $recommendedUser)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createRegistrationRecommenderEmail($user, $recommendedUser)
		);
	}

	public function sendAfterRegistration1DayAutoResponder(User $user)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAfterRegistration1DayAutoResponder($user)
		);
	}

	public function sendAfterRegistration3DaysAutoResponder(User $user, array $recommendedShops, int $countOfShops)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAfterRegistration3DaysAutoResponder($user, $recommendedShops, $countOfShops)
		);
	}

	public function sendAfterRegistration9DaysAutoResponder(User $user)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAfterRegistration9DaysAutoResponder($user)
		);
	}

	public function sendTransactionRegistrationEmail($transaction): void
	{
		if ($transaction instanceof Transaction && $transaction->getUser()) {
			$emailDataObject = $this->emailTemplateFactory->buildTransactionRegistrationEmail($transaction);

			$this->sendEmailByDTO($transaction->getUser(), $emailDataObject, null, 50);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createTransactionRegistrationEmail($transaction)
			);
		}
	}

	public function sendTransactionRecommendationBonusRegistrationEmail(Transaction $transaction)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createTransactionRecommendationBonusRegistrationEmail($transaction)
		);
	}

	public function sendBonusTransactionRecommendationBonusRegistrationEmail(Transaction $transaction)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createBonusTransactionRegistrationEmail($transaction)
		);
	}

	public function sendTransactionConfirmationEmail($transaction)
	{
		if ($transaction instanceof Transaction && $transaction->getUser()) {
			$emailDataObject = $this->emailTemplateFactory->buildTransactionConfirmationEmail($transaction);

			$this->sendEmailByDTO($transaction->getUser(), $emailDataObject, null, 50);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createTransactionConfirmationEmail($transaction)
			);
		}
	}

	public function sendPayoutCreationEmail(Payout $payout)
	{
		if (
			$payout->getUser() && (
			$payout->getUser()->getLocalization()->isSlovak() ||
			$payout->getUser()->getLocalization()->isCzech() ||
			$payout->getUser()->getLocalization()->isPolish() ||
			$payout->getUser()->getLocalization()->isHungarian() ||
			$payout->getUser()->getLocalization()->isRomanian() ||
			$payout->getUser()->getLocalization()->isSlovenian() ||
			$payout->getUser()->getLocalization()->isBulgarian() ||
			$payout->getUser()->getLocalization()->isCroatian())
		) {
			$emailDataObject = $this->emailTemplateFactory->buildPayoutCreationEmail($payout);

			$this->sendEmailByDTO($payout->getUser(), $emailDataObject, 'payout-creation-' . $payout->getId(), 400);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createPayoutCreationEmail($payout)
			);
		}
	}

	public function sendAccountNumberChangeVerificationEmail(Change $change)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAccountNumberChangeVerificationEmail($change)
		);
	}

	public function sendPayoutConfirmationEmail(Payout $payout)
	{
		if (
			$payout->getUser() && (
			$payout->getUser()->getLocalization()->isSlovak() ||
			$payout->getUser()->getLocalization()->isCzech() ||
			$payout->getUser()->getLocalization()->isPolish() ||
			$payout->getUser()->getLocalization()->isHungarian() ||
			$payout->getUser()->getLocalization()->isRomanian() ||
			$payout->getUser()->getLocalization()->isSlovenian()) ||
			$payout->getUser()->getLocalization()->isBulgarian() ||
			$payout->getUser()->getLocalization()->isCroatian()
		) {
			$emailDataObject = $this->emailTemplateFactory->buildPayoutConfirmationEmail($payout);

			$this->sendEmailByDTO($payout->getUser(), $emailDataObject, 'payout-confirmation-' . $payout->getId(), 400);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createPayoutConfirmationEmail($payout)
			);
		}
	}

	public function sendMoneyRewardExpirationEmail(MoneyReward $moneyReward)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createMoneyRewardExpirationEmail($moneyReward)
		);
	}

	public function sendShareRewardExpirationEmail(ShareReward $shareReward)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createShareRewardReminderEmail($shareReward)
		);
	}

	public function sendMoneyRewardReminderEmail(MoneyReward $moneyReward)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createMoneyRewardReminderEmail($moneyReward)
		);
	}

	public function sendVerificationEmail(User $user)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createVerificationEmail($user)
		);
	}

	public function sendFreshdeskFeedbackEmail(Ticket $ticket)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createFreshdeskFeedbackEmail($ticket)
		);
	}

	public function sendReviewRequestEmail(ReviewRequest $reviewRequest): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createReviewRequestEmail($reviewRequest)
		);
	}

	public function sendReviewRequestResponse(ReviewRequest $reviewRequest): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createReviewRequestResponseEmail($reviewRequest)
		);
	}

	public function sendTextEmail($to, $subject, $body, $type, $campaign, $fromEmail, $priority, $scheduledAt = null, $duplicityProtection = true)
	{
		if ($to instanceof User) {
			$email = $to->getEmail();
		} else {
			$email = $to;
		}

		if (!$scheduledAt) {
			$scheduledAt = new \DateTime();
		}

		$protectionDuplicityHash = $duplicityProtection ? md5($email . $subject . $body . $fromEmail) : null;

		$this->emailManager->scheduleSendEmail(
			new Email($protectionDuplicityHash, $to, $subject, $body, $type, $campaign, $scheduledAt, $fromEmail, $fromEmail, $priority)
		);
	}

	public function sendVerificationSms(User $user, $code, \DateTime $validTill)
	{
		if ($user->getPhoneNumber() === null) {
			Debugger::log('verification - empty phone number ' . $user->getId() . ' |' . $user->getLocalization()->getLocale(), 'sms-warning');
			return;
		}

		$sms = $this->smsManager->scheduleSms(
			$this->smsFactory->createVerificationSms($user, $code, $validTill)
		);

		if ($sms) {
			$this->smsManager->sendSms($sms);
		}
	}

	public function sendSessionVerificationSms(User $user, $code)
	{
		if ($user->getPhoneNumber() === null) {
			Debugger::log('session verification - empty phone number ' . $user->getId() . ' |' . $user->getLocalization()->getLocale(), 'sms-warning');
			return;
		}

		$sms = $this->smsManager->scheduleSms(
			$this->smsFactory->createSessionVerificationSms($user, $code)
		);

		if ($sms) {
			$this->smsManager->sendSms($sms);
		}
	}

	public function clearUserEmails(User $user)
	{
		$emails = $this->emailRepository->findUserEmails($user);

		/** @var Email $email */
		foreach ($emails as $email) {
			$this->emailManager->clearEmailUserData($email);
		}
	}

	public function clearUserSms(User $user)
	{
		$smsList = $this->smsRepository->findUserSms($user);

		/** @var Sms $sms */
		foreach ($smsList as $sms) {
			$this->smsManager->clearSmsUserData($sms);
		}
	}

	public function createOrUpdateMandrillTag($tag, $sent, $hardBounces, $softBounces, $rejects, $complaints, $unsubscribes, $opens, $clicks, $uniqueOpens, $uniqueClicks, $turnover, $countActivatedUsers, $countReactivatedUsers, $countAddonInstalls, $createdAt)
	{
		$this->mandrillManager->createOrUpdateTag($tag, $sent, $hardBounces, $softBounces, $rejects, $complaints, $unsubscribes, $opens, $clicks, $uniqueOpens, $uniqueClicks, $turnover, $countActivatedUsers, $countReactivatedUsers, $countAddonInstalls, $createdAt);
	}

	public function sendUserRewardExpirationNotificationEmail(UserReward $userReward, $recommendedShops, \DateTime $scheduledAt = null)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createUserRewardExpirationNotificationEmail($userReward, $recommendedShops, $scheduledAt)
		);
	}

	public function sendUserRewardToNewUsersNotificationEmail(UserReward $userReward, $recommendedShops, \DateTime $scheduledAt = null)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createUserRewardToNewUsersNotificationEmail($userReward, $recommendedShops, $scheduledAt)
		);
	}

	public function sendAfterRedirectionAutoResponderEmail(User $user)
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAfterRedirectionAutoResponderEmail($user)
		);
	}

	public function sendCampaignAutoresponder(CampaignSubscription $campaignSubscription, string $type, ?Transaction $transaction = null, ?Transaction $bonusTransaction = null): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createCampaignAutoResponderEmail($campaignSubscription, $type, $transaction, $bonusTransaction)
		);
	}

	public function createOrUpdateOneSignalNotification(Localization $localization, $uniqueId, $name, $contents, $clicks, $countOfSuccessful, $countOfFailed, $countOfRemaining, $turnover, $countOfActivatedUsers, $countOfReactivatedUsers, $countAddonInstalls, $startSendingAt, $completedAt)
	{
		$this->oneSignalNotificationManager->createOrUpdateNotification($localization, $uniqueId, $name, $contents, $clicks, $countOfSuccessful, $countOfFailed, $countOfRemaining, $turnover, $countOfActivatedUsers, $countOfReactivatedUsers, $countAddonInstalls, $startSendingAt, $completedAt);
	}

	public function sendTextSms(User $user = null, $toPhoneNumber, $text, $campaign, \DateTime $scheduledAt = null, $priority = Sms::PRIORITY_MEDIUM)
	{
		if ($toPhoneNumber === null) {
			if ($user === null || $user->getPhoneNumber() === null) {
				Debugger::log(
					'sendTextSms - empty phone number ' .
					($user ? $user->getId() : 'no-user') . ' | ' .
					($user && $user->getLocalization() ? $user->getLocalization()->getLocale() : 'unknown'),
					'sms-warning'
				);
				return;
			}
		}

		$sms = $this->smsManager->scheduleSms(
			$this->smsFactory->createSms($user, $toPhoneNumber, $text, $campaign, $scheduledAt, $priority)
		);

		if ($sms && $sms->getPriority() >= Sms::PRIORITY_SEND_IMMEDIATELY) {
			$this->smsManager->sendSms($sms);
		}
	}

	public function sendHowToVideoAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createHowToVideoAutoResponderEmail($user)
		);
	}

	public function sendMobileAppAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createMobileAppAutoResponderEmail($user)
		);
	}

	public function sendAddonAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAddonAutoResponderEmail($user)
		);
	}

	public function sendHowToGetRewardsAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createHowToGetRewardsResponderEmail($user)
		);
	}

	public function sendElectronicsAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createElectronicsResponderEmail($user)
		);
	}

	public function sendTravelAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createTravelResponderEmail($user)
		);
	}

	public function sendAliExpressAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createAliExpressResponderEmail($user)
		);
	}

	public function sendShopsAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createShopsAutoResponderEmail($user)
		);
	}

	public function sendFashionAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createFashionAutoResponderEmail($user)
		);
	}

	public function sendHealthAutoresponder(User $user): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createHealthAutoResponderEmail($user)
		);
	}

	public function sendFeedbackAfterRedirectionAutoresponder(Redirection $redirection): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createFeedbackAfterRedirectionAutoResponderEmail($redirection)
		);
	}

	public function sendDealsAutoresponder(User $user, array $deals): void
	{
		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createDealsAutoResponderEmail($user, $deals)
		);
	}

	public function sendAccountNumberVerificationEmail(User $user)
	{
		if ($user->isSlovak() || $user->isCzech() || $user->isPolish() || $user->isHungarian() || $user->isRomanian() || $user->isSlovenian() || $user->isBulgarian() || $user->isCroatian()) {
			$emailDataObject = $this->emailTemplateFactory->buildAccountNumberVerificationEmail($user);

			$this->sendEmailByDTO($user, $emailDataObject, null, 500);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createAccountNumberVerificationEmail($user)
			);
		}
	}

	public function sendNewPasswordEmail(User $user)
	{
		if ($user->isSlovak() || $user->isCzech() || $user->isPolish() || $user->isRomanian() || $user->isHungarian() || $user->isSlovenian() || $user->isBulgarian() || $user->isCroatian()) {
			$emailDataObject = $this->emailTemplateFactory->buildNewPasswordEmail($user);

			$this->sendEmailByDTO($user, $emailDataObject, null, 500);
		} else {
			$this->emailManager->scheduleSendEmail(
				$this->emailFactory->createNewPasswordEmail($user)
			);
		}
	}

	public function sendEmailByDTO(User $user, EmailDataObject $emailDataObject, ?string $protectionDuplicityHash = null, int $priority = 1): void
	{
		if ($emailDataObject->getProtectionDuplicityHash()) {
			$protectionDuplicityHash = $emailDataObject->getProtectionDuplicityHash();
		}

		$this->emailManager->scheduleSendEmail(
			$this->emailFactory->createEmailFromEmailDataObject($user, $emailDataObject, $protectionDuplicityHash, $priority)
		);
	}

	public function findScheduledLuckyShopEmail(User $user): ?Email
	{
		return $this->emailRepository->findScheduledLuckyShopEmail($user);
	}

	public function findLastAccountNumberVerificationEmail(User $user): ?Email
	{
		return $this->emailRepository->findLastAccountNumberVerificationEmail($user);
	}

	public function findTodayLuckyShopEmailForUser(User $user): ?Email
	{
		return $this->emailRepository->findTodayLuckyShopEmailForUser($user);
	}
}
