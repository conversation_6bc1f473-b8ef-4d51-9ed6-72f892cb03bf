<?php

namespace tipli\Model\Monitors;

use Admitad\Api\Model;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserLogin;
use tipli\Model\Account\UserFacade;
use tipli\Model\Accounting\Entities\Payment;
use tipli\Model\Admitad\AdmitadClient;
use tipli\Model\Commands\Entities\WebhookRequest;
use tipli\Model\Currencies\Currency;
use tipli\Model\Currencies\CurrencyFacade;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Log\Entities\BadRequest;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\Messages\ISmsSender;
use tipli\Model\OpsGenie\OpsGenieClient;
use tipli\Model\OpsGenie\Producers\OpsGenieProducer;
use tipli\Model\PartnerOrganizations\Entities\PartnerOrganization;
use tipli\Model\PartnerOrganizations\PartnerOrganizationFacade;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Products2\ProductFacade;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Reports\AggregatedMetricFacade;
use tipli\Model\Reports\Entities\Metric;
use tipli\Model\Reports\Entities\MetricValue;
use tipli\Model\Reports\MetricFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Transactions\Entities\Transaction;

class MonitorProcessor
{
	/** @var StatisticDataProvider */
	private $statisticDataProvider;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var PartnerOrganizationFacade */
	private $partnerOrganizationFacade;

	/** @var UserFacade */
	private $userFacade;

	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var PartnerSystemFacade */
	private $partnerSystemFacade;

	/** @var ISmsSender */
	private $smsSender;

	/** @var CurrencyFacade */
	private $currencyFacade;

	/** @var AdmitadClient */
	private $admitadClient;

	/** @var OpsGenieProducer */
	private $opsGenieProducer;

	/** @var ProductFacade */
	private $productFacade;

	public function __construct(
		StatisticDataProvider $statisticDataProvider,
		ShopFacade $shopFacade,
		LocalizationFacade $localizationFacade,
		PartnerOrganizationFacade $partnerOrganizationFacade,
		UserFacade $userFacade,
		LeafletFacade $leafletFacade,
		PartnerSystemFacade $partnerSystemFacade,
		ISmsSender $smsSender,
		CurrencyFacade $currencyFacade,
		private AggregatedMetricFacade $aggregatedMetricFacade,
		AdmitadClient $admitadClient,
		OpsGenieProducer $opsGenieProducer,
		ProductFacade $productFacade,
		private MetricFacade $metricFacade,
		private LuckyShopFacade $luckyShopFacade
	) {
		$this->statisticDataProvider = $statisticDataProvider;
		$this->shopFacade = $shopFacade;
		$this->localizationFacade = $localizationFacade;
		$this->partnerOrganizationFacade = $partnerOrganizationFacade;
		$this->userFacade = $userFacade;
		$this->leafletFacade = $leafletFacade;
		$this->partnerSystemFacade = $partnerSystemFacade;
		$this->smsSender = $smsSender;
		$this->currencyFacade = $currencyFacade;
		$this->admitadClient = $admitadClient;
		$this->opsGenieProducer = $opsGenieProducer;
		$this->productFacade = $productFacade;
	}

	public function checkTransactions(): void
	{
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			foreach (['aliexpress', 'temu', 'shein'] as $slug) {
				$from = (new \DateTime())->modify('-1 hour');
				$to = new \DateTime();

				/** @var Shop|null $shop */
				$shop = $this->shopFacade->findBySlug($slug, $localization);

				if (!$shop) {
					continue;
				}

				if ($shop->isCashbackAllowed() === false) {
					continue;
				}

				$countOfTransactions = $this->statisticDataProvider->getCountOfTransactions($from, $to, $localization, null, $shop);

				if ($countOfTransactions === 0) {
					$message = 'Za poslední hodinu žádná transakce v obchodě  ' . $shop->getName() . ' - ' . $localization->getLocale();

					$this->opsGenieProducer->scheduleAlert(
						OpsGenieClient::PRIORITY_MODERATE,
						'#ctc' . $shop->getId(),
						$message
					);
				}
			}
		}
	}

	public function checkLoginsPerUserLocalization(): void
	{
		$from = (new \DateTime())->modify('-8 hours');
		$to = new \DateTime();

		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$userLogins = $this->statisticDataProvider->getCountOfUserLoginByLocalizationPlatformAndMethod($localization, $from, $to);

			$countOfLogins = [];

			foreach ($userLogins as $userLogin) {
				$countOfLogins[$userLogin['platform']][$userLogin['method']] = $userLogin['countOfLogins'] ;
			}

			$platforms = [
				UserLogin::PLATFORM_WEB => [
					UserLogin::METHOD_PASSWORD => 20,
					UserLogin::METHOD_GOOGLE => 10,
					UserLogin::METHOD_FACEBOOK => 10,
				],
			];

			foreach ($platforms as $platform => $methods) {
				foreach ($methods as $method => $threshold) {
					if (!isset($countOfLogins[$platform][$method]) || $countOfLogins[$platform][$method] < $threshold) {
						$message = sprintf(
							'Málo přihlášení z platformy: %s pomocí: %s, lokalizace %s za posledních 8h',
							$platform,
							$method,
							$localization->getLocale()
						);

						$this->opsGenieProducer->scheduleAlert(
							OpsGenieClient::PRIORITY_HIGH,
							'#clpl' . $platform . $method,
							$message
						);
					}
				}
			}
		}
	}

	public function checkLogins(): void
	{
		$from = (new \DateTime())->modify('-3 hours');
		$to = new \DateTime();

		$userLogins = $this->statisticDataProvider->getCountOfUserLoginByPlatformAndMethod($from, $to);

		$countOfLogins = [];

		foreach ($userLogins as $userLogin) {
			$countOfLogins[$userLogin['platform']][$userLogin['method']] = $userLogin['countOfLogins'] ;
		}

		$platforms = [
			UserLogin::PLATFORM_WEB => [
				UserLogin::METHOD_PASSWORD => $this->isNight() ? 30 : 50,
				UserLogin::METHOD_GOOGLE => $this->isNight() ? 20 : 10,
				UserLogin::METHOD_FACEBOOK => $this->isNight() ? 20 : 10,
			],
		];

		foreach ($platforms as $platform => $methods) {
			foreach ($methods as $method => $threshold) {
				if (!isset($countOfLogins[$platform][$method]) || $countOfLogins[$platform][$method] === 0) {
					$message = sprintf(
						'Žádné přihlášení z platformy: %s pomocí: %s za poslední 3h',
						$platform,
						$method
					);

					$this->opsGenieProducer->scheduleAlert(
						OpsGenieClient::PRIORITY_HIGH,
						'#cl' . $platform . $method,
						$message
					);
				} elseif ($countOfLogins[$platform][$method] < $threshold) {
					$message = sprintf(
						'Málo přihlášení (%s/%s) z platformy: %s pomocí: %s za poslední 3h',
						$countOfLogins[$platform][$method],
						$threshold,
						$platform,
						$method
					);

					$this->opsGenieProducer->scheduleAlert(
						OpsGenieClient::PRIORITY_MODERATE,
						'#cl' . $platform . $method,
						$message
					);
				}
			}
		}
	}

	public function checkLoginsByPlatforms(): void
	{
		$from = (new \DateTime())->modify('-24 hours');
		$to = new \DateTime();

		$userLogins = $this->statisticDataProvider->getCountOfUserLoginByPlatformAndMethod($from, $to);

		$countOfLogins = [];

		foreach ($userLogins as $userLogin) {
			$countOfLogins[$userLogin['platform']][$userLogin['method']] = $userLogin['countOfLogins'] ;
		}

		$platforms = [
			UserLogin::PLATFORM_WEB => [
				UserLogin::METHOD_APPLE => 1,
			],
			UserLogin::PLATFORM_IOS => [
				UserLogin::METHOD_PASSWORD => 1,
				UserLogin::METHOD_APPLE => 1,
				UserLogin::METHOD_FACEBOOK => 1,
				UserLogin::METHOD_GOOGLE => 1,
			],
			UserLogin::PLATFORM_ANDROID => [
				UserLogin::METHOD_PASSWORD => 1,
				UserLogin::METHOD_FACEBOOK => 1,
				UserLogin::METHOD_GOOGLE => 1,
			],
		];

		$incidents = [];

		foreach ($platforms as $platform => $methods) {
			foreach ($methods as $method => $threshold) {
				if (!isset($countOfLogins[$platform][$method]) || $countOfLogins[$platform][$method] < $threshold) {
					$incidents[] = sprintf(
						'Málo přihlášení (%s/%s) z platformy: %s pomocí: %s',
						$countOfLogins[$platform][$method] ?: 0,
						$threshold,
						$platform,
						$method
					);
				}
			}
		}

		if ($incidents) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#clp',
				implode(PHP_EOL, $incidents)
			);
		}
	}

	public function checkRondoUsers(): void
	{

		$from = (new \DateTime())->modify('-8 hours');
		$to = new \DateTime();

		/** @var PartnerOrganization $partnerOrganization */
		foreach ($this->partnerOrganizationFacade->find(32) as $partnerOrganization) {
			$usersQuery = $this->userFacade->createUsersQuery()
				->withPartnerOrganization($partnerOrganization)
				->onlyActive()
				->createdBetween($from, $to);

			$countOfUsers = $this->userFacade->fetch($usersQuery)->count();

			if ($countOfUsers === 0) {
				$message = 'Za posledních 8 hodin se nikdo neregistroval skrz ' . $partnerOrganization->getName();
				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cru' . $partnerOrganization->getId(),
					$message
				);
			}
		}
	}

	public function checkWhiteLabeledUsers(): void
	{
		$whiteLabelPartnerOrganizations = [
			$this->partnerOrganizationFacade->find(26),
			$this->partnerOrganizationFacade->find(34),
		];

		$from = (new \DateTime())->modify('-24 hours');

		$to = new \DateTime();

		/** @var PartnerOrganization $partnerOrganization */
		foreach ($whiteLabelPartnerOrganizations as $partnerOrganization) {
			$usersQuery = $this->userFacade->createUsersQuery()
				->withPartnerOrganization($partnerOrganization)
				->onlyActive()
				->createdBetween($from, $to);

			$countOfUsers = $this->userFacade->fetch($usersQuery)->count();

			$sql = '
				SELECT *
				FROM tipli_account_user
				WHERE partner_organization_id = ' . $partnerOrganization->getId() . '
				AND created_at >= "' . $from->format('Y-m-d H:i:s') . '"
			';

			if ($countOfUsers === 0) {
				$message = 'Za posledních 24 hodin se nikdo neregistroval skrz  ' . $partnerOrganization->getName();

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cwlu' . $partnerOrganization->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkLeaflets(): void
	{
		if (in_array(date('N'), [6, 7])) {
			return;
		}

		$leafletsQuery = $this->leafletFacade->createLeafletsQuery()
			->newestFirst()
			->createdBetween(new \DateTime('-12 hours'), new \DateTime());

		if ($this->leafletFacade->fetch($leafletsQuery)->count() === 0) {
			$message = 'Za posledních 12 hodin nebyl vytvořený žádný leták!';

			$sql = '
				SELECT *
				FROM tipli_leaflets_leaflet
				WHERE created_at >= "' . (new \DateTime('-12 hours'))->format('Y-m-d H:i:s') . '"
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cleaf',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkShopRedirections(): void
	{
		$redirections = $this->shopFacade->getShopRedirections()
			->select('COUNT(r.id)')
			->addOrderBy('r.id', 'DESC')
			->andWhere('r.createdAt >= :createdFrom')->setParameter('createdFrom', new \DateTime('-1 hour'));

		if ((int) $redirections->getQuery()->getSingleScalarResult() <= 10) {
			$message = 'Za poslední hodinu bylo méně než 10 přesměrování do obchodů';

			$sql = '
				SELECT *
				FROM tipli_shops_redirection
				WHERE created_at >= "' . (new \DateTime('-1 hour'))->format('Y-m-d H:i:s') . '"
				LIMIT 1000
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_CRITICAL,
				'#csr',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkCommissions(): void
	{
		$dateFrom = date('h') >= 23 || date('h') <= 8 ? new \DateTime('-4 hours') : new \DateTime('-30 minutes');

		$countOfTransactions = (int) $this->statisticDataProvider->getCountOfTransactions($dateFrom, new \DateTime());

		if ($countOfTransactions === 0) {
			$message = 'Za posledních 30 minut nebyla registrována žádná transakce!)';

			$sql = '
				SELECT *
				FROM tipli_transactions_transaction
				WHERE created_at >= "' . (new \DateTime('-30 minutes'))->format('Y-m-d H:i:s') . '"
				LIMIT 1000
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_CRITICAL,
				'#ccom',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkBadRequests(): void
	{
		if ($badRequests = $this->statisticDataProvider->getCommonBadRequests(new \DateTime('-24 hours'), new \DateTime(), 500)) {
			$message = 'Za posledních 24 hodin příliš mnoho 404 stránek:';

			/** @var BadRequest $badRequest */
			foreach (array_slice($badRequests, 0, 5) as $badRequest) {
				$message .=  "\n" . $badRequest->getUrl();
			}

			if (count($badRequests) > 5) {
				$message .= "\n +" . (count($badRequests) - 5) . ' další!';
			}

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#cbr',
				$message
			);
		}
	}

	public function checkPayouts(): void
	{
		$countOfTransactionPayouts = (int) $this->statisticDataProvider->getCountOfPayoutTransactions(new \DateTime('-12 hours'), new \DateTime());
		$countOfPayouts = (int) $this->statisticDataProvider->getCountOfCreatedPayouts(new \DateTime('-12 hours'), new \DateTime());

		$sql = '
				SELECT *
				FROM tipli_payouts_payout
				WHERE created_at >= "' . (new \DateTime('-12 hours'))->format('Y-m-d H:i:s') . '";

				SELECT *
				FROM tipli_transactions_transaction
				WHERE created_at >= "' . (new \DateTime('-12 hours'))->format('Y-m-d H:i:s') . '"
				AND type = "payout";
			';

		if ($countOfTransactionPayouts !== $countOfPayouts) {
			$message = 'Počet výplat se neshoduje s počtem payout transakcí';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cpy',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}

		if ($countOfPayouts === 0) {
			$message = 'Za posledních 12 hodin nebyla vytvořena žádná výplata';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cpz',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTooMuchUnsuccessUserLogins(): void
	{
		$countOfUnsuccessfulLogins = (int) $this->statisticDataProvider->getCountOfUserLogin(new \DateTime('-1 hour'), new \DateTime(), null, null, false);

		if ($countOfUnsuccessfulLogins > 120) {
			$message = 'Příliš mnoho neúspěšných pokusů o přihlášení (' . $countOfUnsuccessfulLogins . ') #ctmuul';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctmuul',
				$message
			);
		}
	}

	public function checkAdmitadWebhooks(): void
	{
		$countOfWebhooks = $this->statisticDataProvider->getCountOfWebhookRequests(new \DateTime('-1 day'), new \DateTime(), WebhookRequest::SOURCE_ADMITAD);

		if ($countOfWebhooks < 100) {
			$message = 'Málo webhooků z admitadu: ' . $countOfWebhooks;

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#caw',
				$message
			);
		}

		if ($this->statisticDataProvider->getCountOfWebhookRequests(new \DateTime('-1 hour'), new \DateTime(), WebhookRequest::SOURCE_ADMITAD) === 0) {
			$message = 'Za poslední hodinu žádný webhook z admitadu';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cawz',
				$message
			);
		}
	}

	public function checkTradedoublerWebhooks(): void
	{
		$countOfWebhooks = $this->statisticDataProvider->getCountOfWebhookRequests(new \DateTime('-1 day'), new \DateTime(), WebhookRequest::SOURCE_TRADEDOUBLER);

		if ($countOfWebhooks < 50) {
			$message = 'Málo webhooků z tradedoubleru: ' . $countOfWebhooks;

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctw',
				$message
			);
		}
	}

	public function checkDuplicatePayouts(): void
	{
		if ($countOfDuplicates = $this->statisticDataProvider->getDuplicatePayouts(new \DateTime('-1 hour'))) {
			$message = 'Za poslední hodinu ' . $countOfDuplicates . ' duplicitních výplat';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cdp',
				$message
			);
		}
	}

	public function checkDuplicateTransactions(): void
	{
		$duplicateTransactions = $this->statisticDataProvider->getDuplicateTransactions(new \DateTime('-1 hour'));

		if ($duplicateTransactions) {
			$countOfDuplicates = count($duplicateTransactions);

			$idsOfDuplicateTransactions = array_map(static function ($item) {
				return $item['transactionId'];
			}, $duplicateTransactions);

			$message = 'Za poslední hodinu ' . $countOfDuplicates . ' duplicitních transakcí ' . implode(', ', $idsOfDuplicateTransactions);

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cdt',
				$message
			);
		}
	}

	public function checkUsersWithNegativeBalances(): void
	{
		/** @var User $user */
		foreach ($this->statisticDataProvider->getUsersWithNegativeBalance(new \DateTime('-8 hours')) as $user) {
			$message = 'Za posledních 8 hodin uživatel s ID:  ' . $user->getId() . ' má účet v mínusu';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cuwnb' . $user->getId(),
				$message
			);
		}
	}

	public function checkDuplicatePayments(): void
	{
		$from = (new \DateTime('-1 day'))->setTime(0, 0, 0);
		$to = (new \DateTime('-1 day'))->setTime(23, 59, 59);

		$countOfTransfers = $this->statisticDataProvider->getCountOfDuplicatePayments($from, $to);

		if ($countOfTransfers > 0) {
			$message = 'včera celkem ' . $countOfTransfers . ' duplicitních plateb #cdp';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cdp',
				$message
			);
		}
	}

	public function checkVisits(): void
	{
		$countOfImportedVisits = $this->statisticDataProvider->getCountOfImportedVisits((new \DateTime('-1 day'))->setTime(0, 0));

		if ($countOfImportedVisits < 1500) {
			$message = 'Málo stažených dat z analytics';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#cvisits',
				$message
			);
		}
	}

	public function checkMandrillTags(): void
	{
		$countOfTags = $this->statisticDataProvider->getCountOfMandrillTags((new \DateTime())->setTime(0, 0));

		if ($countOfTags < 10) {
			$message = 'Málo stažených tagů z Mandrillu #cmt';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#cmt',
				$message
			);
		}
	}

	public function checkCollabimData(): void
	{
		$countOfProcessedKeywords = $this->statisticDataProvider->getCountOfProcessedCollabimKeywords((new \DateTime())->setTime(0, 0, 0), new \DateTime());

		if ((int) $countOfProcessedKeywords < 500) {
			$message = 'Málo aktualizovaných klíčových slov z collabimu';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#cpk',
				$message
			);
		}

		$countOfProcessedKeywordPositions = $this->statisticDataProvider->getCountOfProcessedCollabimKeywordPositions((new \DateTime())->setTime(0, 0, 0), new \DateTime());

		if ((int) $countOfProcessedKeywordPositions < 20) {
			$message = 'Málo zpracovaných pozic klíčových slov z collabimu';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#cpkp',
				$message
			);
		}
	}

	public function checkSentEmails(): void
	{
		$hour = date('G');
		$treshold = $hour >= 1 && $hour <= 7 ? 10 : 80;

		$from = (new \DateTime())->modify('-1 hour');
		$to = (new \DateTime())->modify('-1 hour');
		$lastHour = $from->format('G');

		$countOfEmails = $this->statisticDataProvider->getCountOfSentEmails($from->setTime($lastHour, 0, 0), $to->setTime($lastHour, 59, 59));

		if ((int) $countOfEmails < $treshold) {
			$message = 'Za poslední hodinu bylo odesláno: ' . $countOfEmails . ' / ' . $treshold . ' e-mailů.';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cse',
				$message
			);
		}
	}

	public function checkAbortedEmails(): void
	{
		$from = (new \DateTime())->modify('-1 hour');
		$to = (new \DateTime())->modify('-1 hour');
		$lastHour = $from->format('G');

		$treshold = 200;
		$countOfEmails = $this->statisticDataProvider->getCountOfAbortedEmails($from->setTime($lastHour, 0), $to->setTime($lastHour, 59, 59));

		if ((int) $countOfEmails > $treshold) {
			$message = 'Za poslední hodinu bylo zamítnuto více než ' . $treshold . ' e-mailů.';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cae',
				$message
			);
		}
	}

	public function checkPayoutsWithPayments(): void
	{
		$dateFrom = (new \DateTime('-1 day'))->setTime(0, 0, 0);
		$dateTo = (new \DateTime('-1 day'))->setTime(23, 59, 9);

		$paymentsWithoutPayout = $this->statisticDataProvider->getPaymentsWithoutPayout($dateFrom, $dateTo);
		$payoutsWithoutPayment = $this->statisticDataProvider->getPayoutsWithoutPayment($dateFrom, $dateTo);

		if ($payoutsWithoutPayment) {
			/** @var Payout $payment */
			foreach ($payoutsWithoutPayment as $payout) {
				$sql = '
					SELECT * FROM tipli_payouts_payout p
					WHERE p.id  = ' . $payout->getId() . ';
				';

				$message = sprintf(
					'Výplata s id %s bez payment',
					$payout->getId()
				);

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_MODERATE,
					'#ccopewpp' . $payout->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}

		if ($paymentsWithoutPayout) {
			/** @var Payment $payment */
			foreach ($paymentsWithoutPayout as $payment) {
				$sql = '
					SELECT * FROM tipli_accounting_payment p
					WHERE p.id  = ' . $payment->getId() . ';
				';

				$message = sprintf(
					'Platba s id %s bez payout transakce',
					$payment->getId()
				);

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_MODERATE,
					'#ccopewpp' . $payment->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkCountOfScheduledSms(): void
	{
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$from = (new \DateTime())->modify('-1 hour');
			$to = new \DateTime();

			$fromLastWeek = (clone $from)->modify('-7 days');
			$toLastWeek = (clone $to)->modify('-7 days');

			$countOfScheduledSms = $this->statisticDataProvider->getCountOfScheduledSms($localization, $from, $to);
			$countOfScheduledSmsLastWeek = $this->statisticDataProvider->getCountOfScheduledSms($localization, $fromLastWeek, $toLastWeek);

			if ($countOfScheduledSms > 10 && $countOfScheduledSms >= 5 * $countOfScheduledSmsLastWeek) {
				$message = 'Za poslední hodinu odesláno příliš mnoho SMS zpráv: ' . $countOfScheduledSms . '/' . $countOfScheduledSmsLastWeek * 5 . ', locale: ' . $localization->getLocale();

				$sql = '
					SELECT s.*
					FROM tipli_messages_sms s 
					INNER JOIN tipli_account_user u on u.id = s.user_id
					WHERE s.scheduled_at >= "' . $from->format('Y-m-d H:i') . '"
					AND s.scheduled_at <= "' . $to->format('Y-m-d H:i') . '"
					AND u.localization_id = ' . $localization->getId() . '
				';

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#ccoss',
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkSmsBranaRemainingCredit(): void
	{
		if ($this->smsSender->getRemainingCredit() >= 4000) {
			return;
		}

		$this->opsGenieProducer->scheduleAlert(
			OpsGenieClient::PRIORITY_HIGH,
			'#csbrc',
			'Docházející kredit na smsbrana.cz',
			'Kontaktovat Jirku Jiráska'
		);
	}

	public function checkSentPushNotifications(): void
	{
		$countOfSentPushNotifications = $this->statisticDataProvider->getCountOfSentPushNotifications(new \DateTime('-24 hours'), new \DateTime());

		if ($countOfSentPushNotifications > 200) {
			return;
		}

		$sql = '
				SELECT count(id)
				FROM tipli_inbox_notification
				WHERE pushed_at >= "' .  (new \DateTime('-24 hours'))->format('Y-m-d H:i') . '"
				AND pushed_at <= "' .  (new \DateTime())->format('Y-m-d H:i') . '"
			';

		$this->opsGenieProducer->scheduleAlert(
			OpsGenieClient::PRIORITY_HIGH,
			'#cspn',
			'Za posledních 24 hod. málo odeslaných push notifikací (' . $countOfSentPushNotifications . ' / 200)',
			$this->createAdminerLinkFromSql($sql)
		);
	}

	public function checkTransactionsRegisteredAt(): void
	{
		$countOfTransactionsWithGreaterCreatedAt = $this->statisticDataProvider->getCountOfTransactionsWithGreaterCreatedAt(new \DateTime('-1 day'), new \DateTime());

		if ($countOfTransactionsWithGreaterCreatedAt) {
			$message = 'U ' . $countOfTransactionsWithGreaterCreatedAt . ' transakcí za posledních 24 hodin je větší created_at než registered_at';

			$sql = '
				SELECT *
				FROM tipli_transactions_transaction
				WHERE created_at > registered_at
				AND created_at >= "' .  (new \DateTime('- 1 day'))->format('Y-m-d H:i') . '"
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctra',
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTransactionsWithoutUser(): void
	{
		/** @var Transaction $transaction */
		foreach ($this->statisticDataProvider->getTransactionsWithoutUser(new \DateTime('-1 day'), new \DateTime()) as $transaction) {
			$message = 'Transakce s id: ' . $transaction->getId() . ' nemá uživatele';

			$sql = '
				SELECT *
				FROM tipli_transactions_transaction
				WHERE id = ' . $transaction->getId() . '
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctwu' . $transaction->getId(),
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTransactionsByCountOfRedirections(): void
	{
		$shopsQuery = $this->shopFacade->createShopsQuery()
			->onlyWithCashbackAllowed();

		$dateFrom = new \DateTime('-24 hours');

		/** @var Shop $shop */
		foreach ($this->shopFacade->fetch($shopsQuery) as $shop) {
			$countOfUniqueUsersInRedirections = $this->statisticDataProvider->getCountOfUniqueUsersInRedirections($dateFrom, new \DateTime(), null, $shop);
			$countOfCommissionTransactions = $this->statisticDataProvider->getCountOfTransactions($dateFrom, new \DateTime(), null, null, $shop);

			if ($countOfUniqueUsersInRedirections >= 30 && ($countOfCommissionTransactions <= ($countOfUniqueUsersInRedirections / 100 * 5))) {
				$message = sprintf(
					'do obchodu %s (%s) id %s, se přesměrovalo %s uživatelů, má ale pouze %s %s',
					$shop->getName(),
					$shop->getLocalization()->getLocale(),
					$shop->getId(),
					$countOfUniqueUsersInRedirections,
					$countOfCommissionTransactions,
					(($countOfCommissionTransactions === 0 || $countOfCommissionTransactions > 4) ? 'transakcí' : ($countOfCommissionTransactions === 1 ? 'transakci' : 'transakce'))
				);
				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#ctbcor' . $shop->getId(),
					$message
				);
			}
		}
	}

	public function checkCountOfCreatedRefunds(): void
	{
		foreach (Refund::getTypes() as $type => $name) {
			$dateFrom = $type === Refund::TYPE_MISSING_COMMISSION ? '-2 hours' : '-1 day';
			$countOfRefunds = $this->statisticDataProvider->getCountOfCreatedRefundsByType(new \DateTime($dateFrom), new \DateTime(), $type);

			if ($countOfRefunds === 0) {
				$message = $countOfRefunds . ' reklamací typu ' . $type;

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#ccocr' . $type,
					$message
				);
			}
		}
	}

	public function checkCountOfApprovedRefunds(): void
	{
		$countOfApprovedRefunds = $this->statisticDataProvider->getCountOfApprovedRefunds(new \DateTime('-24 hours'), new \DateTime());
		$countOfApprovedRefundsByBot = $this->statisticDataProvider->getCountOfApprovedRefunds(new \DateTime('-8 hours'), new \DateTime(), true);

		if ($countOfApprovedRefunds < 10) {
			$message = 'za posledních 24 hodin bylo schváleno pouze ' . $countOfApprovedRefunds . ' reklamací';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#ccoapr',
				$message
			);
		}

		if ($countOfApprovedRefundsByBot < 3) {
			$message = 'za posledních 8 hodin bylo schváleno pouze ' . $countOfApprovedRefundsByBot . ' reklamací robotem';
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#ccoaprb',
				$message
			);
		}
	}

	public function checkSumOfRefundedAmount(): void
	{
		$refundedAmountsByCurrency = $this->statisticDataProvider->getRefundedAmountsByCurrency(new \DateTime('-1 hour'), new \DateTime());

		$totalAmountInCzk = 0;

		foreach ($refundedAmountsByCurrency as $amountInCurrency) {
			$totalAmountInCzk += $this->currencyFacade->convert($amountInCurrency['sum'], $amountInCurrency['currency'], Currency::CZK);
		}

		if ($totalAmountInCzk > 3500) {
			$message = 'za poslední hodinu byla celkem schválena v reklamacích částka ' . $totalAmountInCzk . ' Kč';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#csora',
				$message
			);
		}
	}

	public function getCountOfRefundsWithoutFreshdeskTicketId(): void
	{
		$refundsWithoutFreshdeskTicketId = $this->statisticDataProvider->getRefundsWithoutFreshdeskTicketId(new \DateTime('-8 hours'), new \DateTime());

		if (count($refundsWithoutFreshdeskTicketId) > 2) {
			$refundIds = array_map(static function (Refund $refund) {
				return $refund->getId();
			}, $refundsWithoutFreshdeskTicketId);

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#corwft',
				'za posledních 8 hodin celkem ' . implode(', ', $refundIds) . ' bez freshdeskTicketId'
			);
		}
	}

	public function checkAdmitadStats(): void
	{
		/** @var Model $admitadStats */
		$admitadStats = $this->admitadClient->getStatistics();
		$salesSum = $admitadStats->__get('sales_sum');

		$countOfAdmitadTransactions = $this->statisticDataProvider->getCountOfTransactions(
			(new \DateTime('yesterday'))->setTime(22, 0),
			new \DateTime(),
			null,
			$this->partnerSystemFacade->findByType(PartnerSystem::TYPE_ADMITAD)
		);

		if ((($salesSum - $countOfAdmitadTransactions) / $salesSum) * 100 > 20) {
			$this->opsGenieProducer->scheduleAlert(OpsGenieClient::PRIORITY_CRITICAL, '#cas', 'Málo transakcí z Admitadu');
		}
	}

	public function checkShopWithCbAllowedToPartnerSystemWithoutCommissions(): void
	{
		/** @var Shop $shop */
		foreach ($this->statisticDataProvider->findShopWithCbAllowedToPartnerSystemWithoutCommissions() as $shop) {
			$message = sprintf(
				'obchod %s (%s) id %s s aktivním cashbackem je v síti %s s id %s která má has commissions = 0',
				$shop->getName(),
				$shop->getLocalization()->getLocale(),
				$shop->getId(),
				$shop->getPartnerSystem()->getName(),
				$shop->getPartnerSystem()->getId()
			);

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cswacwpshnhc' . $shop->getId(),
				$message
			);
		}
	}

	public function checkRefundsByPartnerSystems(): void
	{
		foreach ($this->statisticDataProvider->findCountOfRefundsByPartnerSystem(new \DateTime('-3 hours'), new \DateTime()) as $data) {
			if ($data['countOfRefunds'] <= 20) {
				continue;
			}

			$sql = '
				SELECT COUNT(r.id) FROM tipli_refunds_refund r
				INNER JOIN tipli_shops_shop s ON s.id = r.shop_id
				WHERE s.partner_system_id  = ' . $data['id'] . '
				AND r.created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i') . '";

				SELECT * FROM tipli_transactions_transaction
				WHERE partner_system_id = ' . $data['id'] . '
				AND created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i') . '";
			';

			$message = sprintf(
				'V síti %s (%s) je za poslední 3 hodiny velké množství (%s) reklamací',
				$data['name'],
				$data['id'],
				$data['countOfRefunds']
			);

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#crbps' . $data['id'],
				$message,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkShopCountOfTransactionsToRefunds(): void
	{
		$shopsQuery = $this->shopFacade->createShopsQuery()
			->onlyWithCashbackAllowed();

		$dateFrom = new \DateTime('-5 hours');

		/** @var Shop $shop */
		foreach ($this->shopFacade->fetch($shopsQuery) as $shop) {
			$countOfRefunds = $this->statisticDataProvider->getCountOfRefunds($dateFrom, new \DateTime(), null, $shop);
			$countOfCommissionTransactions = $this->statisticDataProvider->getCountOfTransactions($dateFrom, new \DateTime(), null, null, $shop);

			$sql = '
				SELECT r.* FROM tipli_refunds_refund r
				INNER JOIN tipli_shops_shop s ON s.id = r.shop_id
				WHERE s.id  = ' . $shop->getId() . '
				AND r.created_at >= "' . (new \DateTime('-5 hours'))->format('Y-m-d H:i') . '";

				SELECT * FROM tipli_transactions_transaction
				WHERE shop_id = ' . $shop->getId() . '
				AND created_at >= "' . (new \DateTime('-5 hours'))->format('Y-m-d H:i') . '";
			';

			if ($countOfRefunds > 10 && $countOfRefunds > $countOfCommissionTransactions * 1.8) {
				$message = sprintf(
					'V obchodě %s (%s) s id %s je velké množství reklamací (%s) ku počtu transakcí (%s)',
					$shop->getName(),
					$shop->getLocalization()->getLocale(),
					$shop->getId(),
					$countOfRefunds,
					$countOfCommissionTransactions
				);

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cscottr' . $shop->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkShopsWithRedirectionsToRefunds(): void
	{
		$dateFrom = new \DateTime('-3 hours');

		$shops = $this->statisticDataProvider->findShopsWithActiveCashbackAndRedirections($dateFrom, new \DateTime());

		foreach ($shops as $shop) {
			$countOfRefunds = $this->statisticDataProvider->getCountOfRefunds($dateFrom, new \DateTime(), null, $shop);
			$countOfCommissionTransactions = $this->statisticDataProvider->getCountOfTransactions($dateFrom, new \DateTime(), null, null, $shop);

			if ($countOfRefunds > 5 && $countOfRefunds > $countOfCommissionTransactions / 3) {
				$sql = '
					SELECT r.* FROM tipli_refunds_refund r
					INNER JOIN tipli_shops_shop s ON s.id = r.shop_id
					WHERE s.id  = ' . $shop->getId() . '
					AND r.created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i') . '";

					SELECT * FROM tipli_transactions_transaction
					WHERE shop_id = ' . $shop->getId() . '
					AND created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i') . '";
				';

				$message = sprintf(
					'V obchodě %s (%s) s id %s je velké množství reklamací (%s) ku počtu transakcí (%s)',
					$shop->getName(),
					$shop->getLocalization()->getLocale(),
					$shop->getId(),
					$countOfRefunds,
					$countOfCommissionTransactions
				);

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cswrtr' . $shop->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkPartnerSystemsWithRedirectionsToRefunds(): void
	{
		$dateFrom = new \DateTime('-24 hours');

		$partnerSystems = $this->statisticDataProvider->findPartnerSystemsWithCommissionsAndRedirections($dateFrom, new \DateTime());

		foreach ($partnerSystems as $partnerSystem) {
			$countOfRefunds = $this->statisticDataProvider->getCountOfRefunds($dateFrom, new \DateTime(), null, null, null, null, $partnerSystem);
			$countOfCommissionTransactions = $this->statisticDataProvider->getCountOfTransactions($dateFrom, new \DateTime(), null, $partnerSystem);

			if ($countOfRefunds > 15 && $countOfRefunds > $countOfCommissionTransactions / 3) {
				$sql = '
					SELECT r.* FROM tipli_refunds_refund r
					INNER JOIN tipli_shops_shop s ON s.id = r.shop_id
					WHERE s.partner_system_id  = ' . $partnerSystem->getId() . '
					AND r.created_at >= "' . (new \DateTime('-24 hours'))->format('Y-m-d H:i') . '";

					SELECT * FROM tipli_transactions_transaction
					WHERE partner_system_id = ' . $partnerSystem->getId() . '
					AND created_at >= "' . (new \DateTime('-24 hours'))->format('Y-m-d H:i') . '";
				';

				$message = sprintf(
					'V partnerské síti %s s id %s je velké množství reklamací (%s) ku počtu transakcí (%s)',
					$partnerSystem->getName(),
					$partnerSystem->getId(),
					$countOfRefunds,
					$countOfCommissionTransactions
				);

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cpswrtr' . $partnerSystem->getId(),
					$message,
					$this->createAdminerLinkFromSql($sql)
				);
			}
		}
	}

	public function checkCompetitorCrawlers(): void
	{
		$offers = $this->statisticDataProvider->getCountOfUpdatedOffersByCompetitor(new \DateTime('-10 days'));

		foreach ($offers as $offer) {
			if ($offer->cnt > 10) {
				continue;
			}

			$message = 'Málo stažených dat konkurenčního robota ' . $offer->name;

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_LOW,
				'#ccd' . $offer->name,
				$message
			);
		}
	}

	public function checkTransactionsInTopShops(): void
	{
		foreach ([1, 298, 617, 5732, 6493, 7992] as $id) {
			$from = (new \DateTime())->modify('-30 minutes');
			$to = new \DateTime();

			/** @var Shop|null $shop */
			$shop = $this->shopFacade->find($id);
			if (!$shop) {
				continue;
			}

			$countOfTransactions = $this->statisticDataProvider->getCountOfTransactions($from, $to, null, null, $shop);

			if ($countOfTransactions === 0) {
				$message = 'Za poslední půl hodinu žádná transakce v obchodě  ' . $shop->getName() . ' - ' . $shop->getLocalization()->getLocale();

				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_MODERATE,
					'#ctc' . $shop->getId(),
					$message
				);
			}
		}
	}

	public function checkWaitingRefunds(): void
	{
		/** @var Refund $refund */
		foreach ($this->statisticDataProvider->findRefundsWaitingToProcess(new \DateTime('-3 hours')) as $refund) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#wr' . $refund->getId(),
				'Reklamace id ' . $refund->getId() . ' čeká dlouho na zpracování.'
			);
		}
	}

	public function checkPartnerSystemProcess(): void
	{
		$partnerSystemWithProcessAnomaly = $this->statisticDataProvider->findPartnerSystemProcessWithAnomaly();

		foreach ($partnerSystemWithProcessAnomaly as $partnerSystemProcess) {
			$sql = '
				SELECT * FROM tipli_partner_systems_partner_system WHERE id = ' . $partnerSystemProcess->id . ';
				SELECT * FROM tipli_partner_systems_partner_system_process
				WHERE partner_system_id = ' . $partnerSystemProcess->id . '
				AND created_at >= "' . (new \DateTime('- 4 hours'))->format('Y-m-d H:i') . '"
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cpsp' . $partnerSystemProcess->id,
				'Partnerský systém ' . $partnerSystemProcess->name . ' má za poslední 4 hodiny malý počet zpracování: ' . $partnerSystemProcess->countLast4Hours . ' oproti průměru: ' . $partnerSystemProcess->avgPerHour
			);
		}
	}

	public function checkShopActiveAndUnpublished(): void
	{
		$shops = $this->statisticDataProvider->findActiveUnpublishedShops();

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			$sql = 'SELECT * FROM tipli_shops_shop WHERE id = ' . $shop->getId();

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#csaaup' . $shop->getId(),
				'Obchod ' . $shop->getName() . ' (' . $shop->getId() . ') má nastaveno active=1, ale datum publikace je budoucí',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkShopInactiveAndPublished(): void
	{
		$shops = $this->statisticDataProvider->findInactivePublishedShops();

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			$sql = 'SELECT * FROM tipli_shops_shop WHERE id = ' . $shop->getId();

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#csiaap' . $shop->getId(),
				'Obchod ' . $shop->getName() . ' (' . $shop->getId() . ') má nastaveno active=0, ale je publikovaný (published_at <= NOW())',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkShopsWithoutPageExtensions(): void
	{
		$shops = $this->statisticDataProvider->findShopsWithoutPageExtensions();

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			$sql = '
				SELECT *
				FROM tipli_shops_shop
				WHERE id = ' . $shop->getId() . '
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cswpe' . $shop->getId(),
				'Obchod ' . $shop->getName() . ' (' . $shop->getId() . ') nemá PageExtension vazbu',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTransactionsWithoutTransactionData(): void
	{
		$transactions = $this->statisticDataProvider->findTransactionsWithoutTransactionData(new \DateTime('-24 hours'));

		/** @var Transaction $transaction */
		foreach ($transactions as $transaction) {
			$sql = '
				SELECT *
				FROM tipli_transactions_transaction
				WHERE id = ' . $transaction->getId() . ';

				SELECT *
				FROM tipli_transactions_transaction_data
				WHERE transaction_id = ' . $transaction->getId() . ';
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#csaaup' . $transaction->getId(),
				'Transakce ' . $transaction->getId() . ' nemá TransactionData vazbu',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTurnover(): void
	{
		$turnover = $this->statisticDataProvider->getTurnoverOfTransactions(new \DateTime('-12 hours'), new \DateTime());

		if ($turnover > 300000) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cth' . date('dmY'),
				'Za posledních 12 hodin je obrat větší neŽ 300 000 CZK'
			);
		}

		$minThreshold = $this->isWeekend() ? 10000 : 60000;

		if ($turnover < $minThreshold) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctl' . date('dmY'),
				'Za posledních 12 hodin je obrat nižší neŽ ' . $minThreshold . ' CZK'
			);
		}
	}

	public function checkShopsWithTurnoverMoreThen5K(): void
	{
		$shops = $this->statisticDataProvider->findShopsWithTurnover(new \DateTime('-30 days'), 5000);

		foreach ($shops as $shop) {
			if ($shop->count_of_transactions / 30 > 300 && $shop->count_of_transactions_last_8_hours === 0) {
				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_MODERATE,
					'#cswtmt8h' . $shop->id,
					'Za posledních 8 hodin je v obchodě ' . $shop->name . '(' . $shop->id . ')' . '0 transakcí'
				);
			} elseif ($shop->count_of_transactions_last_2_days === 0) {
				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_MODERATE,
					'#cswtmt2d' . $shop->id,
					'Za poslední 2 dny je v obchodě ' . $shop->name . '(' . $shop->id . ')' . '0 transakcí'
				);
			}
		}
	}

	public function checkAboveStandardTurnover(): void
	{
		$averageTurnoverForLast2Hours = round($this->statisticDataProvider->findAverageTurnoverForLast2Hours());
		$turnoverForLast2Hours = round($this->statisticDataProvider->getTurnoverOfTransactions(new \DateTime('-2 hours'), new \DateTime()));

		if ($turnoverForLast2Hours > $averageTurnoverForLast2Hours * 4) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cast' . date('dmY'),
				'Za poslední 2 hodiny nadstandardní obrat: ' . $turnoverForLast2Hours . ' CZK / threshold: ' . ($averageTurnoverForLast2Hours * 4) . ' CZK'
			);
		}
	}

	public function checkAboveStandardCountOfUsers(): void
	{
		$averageCountOfUsersForLast2Hours = $this->statisticDataProvider->findAverageCountOfUsersForLastHours();
		$usersForLast2Hours = $this->statisticDataProvider->findCountOfUsersForLast2Hours();

		if ($usersForLast2Hours > $averageCountOfUsersForLast2Hours * 3) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cast' . date('dmY'),
				'Za poslední 2 hodiny příliš mnoho registrací: ' . $usersForLast2Hours . ' / ' . ($averageCountOfUsersForLast2Hours * 3)
			);
		}
	}

	public function checkInconsistentUsers(): void
	{
		foreach ($this->statisticDataProvider->findUnconsistentUsers() as $user) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#cicu' . $user->id,
				'Uživatel s id ' . $user->id . ' nemá konzistentní řádky v tabulkách (tipli_account_user_data, tipli_account_segment_data atd.)'
			);
		}
	}

	public function checkLoginsWithAccessToken(): void
	{
		$minThreshold = 100;
		$maxThreshold = $minThreshold * 30;

		if ($this->isNight() || $this->isWeekend()) {
			$minThreshold = 20;
		}

		$countOfLogins = $this->statisticDataProvider->getCountOfUserLogin(new \DateTime('-2 hours'), new \DateTime(), UserLogin::METHOD_ACCESS_TOKEN);

		$sql = '
			SELECT COUNT(id)
			FROM tipli_account_user_login
			WHERE method = "access_token"
			AND created_at >= "' . (new \DateTime('-2 hours'))->format('Y-m-d H:i:s') . '"
		';

		if ($countOfLogins < $minThreshold) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cslwatl',
				'Za poslední 2 hodiny málo uživatelů přihlášených přes access token ' . $countOfLogins . '/' . $minThreshold . ')',
				$this->createAdminerLinkFromSql($sql)
			);
		}

		if ($countOfLogins > $maxThreshold) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cslwath',
				'Za poslední 2 hodiny příliš mnoho uživatelů přihlášených přes access token ' . $countOfLogins . '/' . $maxThreshold . ')',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkFailedLoginsWithAccessToken(): void
	{
		$countOfLogins = $this->statisticDataProvider->getCountOfUserLogin(new \DateTime('-2 hours'), new \DateTime(), UserLogin::METHOD_ACCESS_TOKEN, null, false);

		if ($countOfLogins > 0) {
			$sql = '
				SELECT COUNT(id)
				FROM tipli_account_user_login
				WHERE method = "access_token"
				AND successfully = 0
				AND created_at >= "' . (new \DateTime('-2 hours'))->format('Y-m-d H:i:s') . '"
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#cslwath',
				'Za poslední 2 hodiny ' . $countOfLogins . ' neúspěšných pokusů o přihlášení přes access token',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkAdminUserVisitsByUser(): void
	{
		$countOfUserVisitsByUser = $this->statisticDataProvider->getCountOfUserVisitsByUser(new \DateTime('-1 hour'));

		foreach ($countOfUserVisitsByUser as $item) {
			if ($item->cnt <= 300) {
				continue;
			}

			$sql = '
				SELECT *
				FROM tipli_account_user_visit
				WHERE user_id = ' . $item->user_id . '
				AND created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i:s') . '"
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_CRITICAL,
				'#cadusvbu',
				'Za poslední hodinu admin s id: ' . $item->user_id . ' má hodně pohybů v adminu (' . $item->cnt . ')',
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkAdminUserVisits(): void
	{
		$countOfVisits = $this->statisticDataProvider->getCountOfUserVisits(new \DateTime('-3 hours'));

		if ($countOfVisits > 5000) {
			$sql = '
				SELECT user_id, count(id)
				FROM tipli_account_user_visit
				WHERE created_at >= "' . (new \DateTime('-3 hours'))->format('Y-m-d H:i:s') . '"
				GROUP BY user_id
			';


			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_CRITICAL,
				'#cadusv',
				'Příliš mnoho pohybů v adminu: ' . $countOfVisits,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkExistingMetricValues(): void
	{
		/** @var Metric $metric */
		foreach ($this->metricFacade->findMetrics() as $metric) {
			if (in_array($metric->getName(), ['count_of_users', 'count_of_transactions', 'original_turnover', 'income', 'count_of_redirections'])) {
				foreach ($this->localizationFacade->findLocalizations() as $localization) {
					$metricValue = $this->metricFacade->findMetricValueByStartedAt(
						$metric,
						$localization,
						MetricValue::INTERVAL_DAY,
						(new \DateTime())->setTime(0, 0)
					);

					if ($metricValue === null || $metricValue->getValue() === 0.0 || $metricValue->getValue() === null) {
						$this->opsGenieProducer->scheduleAlert(
							OpsGenieClient::PRIORITY_HIGH,
							'#cccl',
							'Metrika [' . $metric->getName() . '][' . $localization->getLocale() . '][' . MetricValue::INTERVAL_DAY . '] neexistuje nebo je 0'
						);
					}
				}
			}
		}
	}

	public function checkExistingAggregatedMetrics(): void
	{
		foreach ($this->localizationFacade->findLocalizations() as $localization) {
			$aggregatedMetric = $this->aggregatedMetricFacade->findAggregatedMetricByStartedAt(
				$localization,
				MetricValue::INTERVAL_DAY,
				(new \DateTime())->setTime(0, 0)
			);

			if (
				$aggregatedMetric === null ||
				$aggregatedMetric->getCountOfUsers() === 0 ||
				$aggregatedMetric->getCountOfTransactions() === 0 ||
				$aggregatedMetric->getCountOfRedirections() === 0 ||
				$aggregatedMetric->getOriginalTurnover() === 0
			) {
				$this->opsGenieProducer->scheduleAlert(
					OpsGenieClient::PRIORITY_HIGH,
					'#cccl',
					'Jedna nebo více aggregated metrik [users,transactions,redirections,turnover][' . $localization->getLocale() . '][' . MetricValue::INTERVAL_DAY . '] neexistuje nebo je 0'
				);
			}
		}
	}

	public function checkUserLuckyShopDataDuplicity(): void
	{
		$userLuckyShopDataDuplicity = $this->statisticDataProvider->findUserLuckyShopDataDuplicity();

		foreach ($userLuckyShopDataDuplicity as $userLuckyShopData) {
			$sql = '
				SELECT *
				FROM tipli_lucky_shops_user_lucky_shop_data
				WHERE user_id = ' . $userLuckyShopData->user_id . '
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#culsdd' . $userLuckyShopData->id,
				'Duplicita v tabulce tipli_lucky_shops_user_lucky_shop_data pro uživatele ' . $userLuckyShopData->user_id,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	public function checkTodayLuckyShop(): void
	{
		foreach ($this->luckyShopFacade->findLuckyShopCampaigns() as $luckyShopCampaign) {
			$luckyShop = $this->luckyShopFacade->findCurrentLuckyShopByCampaign($luckyShopCampaign);

			if ($luckyShop) {
				continue;
			}

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#ctls' . $luckyShopCampaign->getId(),
				'Nebyl vybrán dnešní šťastný obchod pro kampaň ' . $luckyShopCampaign->getId()
			);
		}
	}

	public function checkUserLuckyShopChecksDuplicity(): void
	{
		$userLuckyShopChecksDuplicity = $this->statisticDataProvider->findUserLuckyShopChecksDuplicity();

		foreach ($userLuckyShopChecksDuplicity as $userLuckyShopCheck) {
			$sql = '
				SELECT *
				FROM tipli_lucky_shop_user_lucky_shop_checks
				WHERE user_id = ' . $userLuckyShopCheck->user_id . '
			';

			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_MODERATE,
				'#culscd' . $userLuckyShopCheck->id,
				'Duplicita odhalení šťastného obchodu pro uživatele: ' . $userLuckyShopCheck->user_id,
				$this->createAdminerLinkFromSql($sql)
			);
		}
	}

	private function isWeekend(): bool
	{
		return in_array(date("l"), ['Saturday', 'Sunday']);
	}

	private function isNight(): bool
	{
		return
			(date('H') >= 0 && date('H') <= 6)
			||
			(date('H') >= 22 && date('H') <= 24);
	}

	private function createAdminerLinkFromSql($sql): string
	{
		$sql = trim(preg_replace('/\t+/', '', $sql));

		return '<a href="https://adminer.tipli.cz/?server=************&username=tipli&db=tipli&sql=' . urlencode($sql) . '">adminer</a>';
	}
}
