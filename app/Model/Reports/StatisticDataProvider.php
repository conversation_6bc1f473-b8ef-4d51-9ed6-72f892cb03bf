<?php

namespace tipli\Model\Reports;

use DateTime;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\Query;
use tipli\Model\Account\UserFacade;
use tipli\Model\Doctrine\EntityManager;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Database\Context;
use tipli\Model\Account\Entities\MobileDevice;
use tipli\Model\Account\Entities\SegmentData;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserLogin;
use tipli\Model\Accounting\Entities\Payment;
use tipli\Model\Addon\Entities\ShopVisit;
use tipli\Model\Articles\Entities\Article;
use tipli\Model\Collabim\Entities\Keyword;
use tipli\Model\Collabim\Entities\Position;
use tipli\Model\Commands\Entities\WebhookRequest;
use tipli\Model\Configuration;
use tipli\Model\Currencies\Currency;
use tipli\Model\Currencies\CurrencyFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\DoctrineExtensions\ForceIndexWalker;
use tipli\Model\Freshdesk\Entities\Ticket;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Log\Entities\BadRequest;
use tipli\Model\Log\Entities\MobileRequest;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\MandrillTag;
use tipli\Model\PartnerSystems\Entities\Income;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Entities\ProcessError;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Reports\Queries\IUsersQueryFactory;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Rewards\Entities\ShareReward;
use tipli\Model\Seo\Entities\Visit;
use tipli\Model\Shops\Entities\DescriptionBlock;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\SuspectedTransaction;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\Entities\TransactionData;
use tipli\Model\Utm\Entities\Utm;
use tipli\Model\Zasilkovna\Entities\Packet;
use tipli\Tools\TimeRange;

class StatisticDataProvider
{
	/**
	 * @var EntityManager
	 */
	private $em;

	/**
	 * @var Context
	 */
	private $context;

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	/**
	 * @var CurrencyFacade
	 */
	private $currencyFacade;

	/**
	 * @var IUsersQueryFactory
	 */
	private $usersQueryFactory;

	/**
	 * @var Cache
	 */
	private $cache;

	/** @var Configuration */
	private $configuration;

	public function __construct(EntityManager $em, Context $context, LocalizationFacade $localizationFacade, private UserFacade $userFacade, CurrencyFacade $currencyFacade, IUsersQueryFactory $usersQueryFactory, Storage $storage, Configuration $configuration)
	{
		$this->em = $em;
		$this->context = $context;
		$this->localizationFacade = $localizationFacade;
		$this->currencyFacade = $currencyFacade;
		$this->usersQueryFactory = $usersQueryFactory;
		$this->cache = new Cache($storage, self::class);
		$this->configuration = $configuration;
	}

	public function getCountOfRegisteredUsers(
		$from = null,
		$to = null,
		Localization $localization = null,
		?array $utms = [],
		$isActive = null,
		$verifiedByEmail = null,
		?bool $whiteLabel = null,
		?bool $withReferral = null
	): int {
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(u.id)')
			->from(User::class, 'u');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('u.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('u.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($utms) {
			$qb->andWhere('u.utm IN (:utms)')
				->setParameter('utms', $utms);
		}

		if (!is_null($isActive)) {
			$qb->andWhere('u.active = :isActive')
				->setParameter('isActive', $isActive);
		}

		if ($verifiedByEmail) {
			$qb->andWhere('u.emailVerifiedAt IS NOT NULL');
		}

		if ($whiteLabel !== null) {
			$qb->leftJoin('u.partnerOrganization', 'p');

			if ($whiteLabel) {
				$qb->andWhere('p.isWhiteLabel = true');
			} else {
				$qb->andWhere('(p.isWhiteLabel = false OR u.partnerOrganization IS NULL)');
			}
		}

		if ($withReferral !== null) {
			if ($withReferral === false) {
				$qb->andWhere('u.parentUser IS NULL');
			} else {
				$qb->andWhere('u.parentUser IS NOT NULL');
				$qb->innerJoin('u.utm', 'utm')
					->andWhere('utm.utmSource = :doporuceni')
					->setParameter('doporuceni', 'doporuceni');
			}
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfActiveRegisteredUsers($from = null, $to = null, Localization $localization = null)
	{
		// old solution
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(s.id)')
			->from(SegmentData::class, 's')
			->innerJoin('s.user', 'u')
			->andWhere('s.firstTransactionAt >= :from')
			->andWhere('s.firstTransactionAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		if ($localization) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();

		// old solution
//        return $this->getCountOfActiveRegisteredUsersTill($to, $localization) - $this->getCountOfActiveRegisteredUsersTill($from, $localization);
	}

	public function getCountOfActiveRegisteredUsersTill(DateTime $to, Localization $localization = null)
	{
		$cacheKey = [];

		foreach (func_get_args() as $parameter) {
			if (is_array($parameter)) {
				$cacheKey[] = implode('.', $parameter);
			} elseif ($parameter instanceof DateTime) {
				$cacheKey[] = $parameter->getTimestamp();
			} elseif ($parameter instanceof Localization) {
				$cacheKey[] = $parameter->getLocale();
			} else {
				$cacheKey[] = $parameter;
			}
		}

		$cacheKey = implode('-', $cacheKey);

		if ($data = $this->cache->load($cacheKey)) {
//            return $data;
		}

		$query = $this->usersQueryFactory->create();

		if ($localization) {
			$query->withLocalization($localization);
		}

		if ($to) {
			$query->createdTo($to);
		}

		$data = $query->getCountOfActiveRegisteredUsers();

		$this->cache->save($cacheKey, $data, [
			Cache::EXPIRATION => '15 minutes',
		]);

		return $data;
	}

	public function getTurnoverOfTransactions($from = null, $to = null, Localization $localization = null, Utm $utm = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('sum(td.turnover)')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION);

		if ($localization) {
			$qb->andWhere('t.currency = :currency');
			$qb->setParameter('currency', $localization->getCurrency());
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($utm) {
			$qb->andWhere('t.utm = :utm');
			$qb->setParameter('utm', $utm);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getTotalCountOfTransactions()
	{
		return $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfTransactions($from, \DateTime $to, Localization $localization = null, PartnerSystem $partnerSystem = null, Shop $shop = null, User $user = null, ?bool $canceledOnly = false, ?bool $confirmedOnly = false, ?array $platforms = null, ?bool $fromAddon = false)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION);

		if ($localization) {
			$qb->innerJoin('t.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from);
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to);
		}

		if ($partnerSystem) {
			$qb->andWhere('t.partnerSystem = :partnerSystem');
			$qb->setParameter('partnerSystem', $partnerSystem);
		}

		if ($shop) {
			$qb->andWhere('t.shop = :shop');
			$qb->setParameter('shop', $shop);
		}

		if ($user) {
			$qb->andWhere('t.user = :user');
			$qb->setParameter('user', $user);
		}

		if ($canceledOnly) {
			$qb->andWhere('t.confirmedAt IS NOT NULL');
			$qb->andWhere('t.userCommissionAmount = 0');
		}

		if ($confirmedOnly) {
			$qb->andWhere('t.confirmedAt IS NOT NULL');
		}

		if ($platforms !== null) {
			$qb->innerJoin('t.transactionData', 'd')
				->andWhere('d.platform IN (:platforms)')
				->setParameter('platforms', $platforms);
		}

		if ($fromAddon === true) {
			$qb->innerJoin('t.utm', 'utm')
				->andWhere('utm.utmSource = :addon')
				->setParameter('addon', 'addon');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfConfirmedTransactionsByShop(DateTime $dateFrom, \DateTime $dateTo, ?Shop $shop = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.confirmedAt >= :confirmedFrom')
			->setParameter('confirmedFrom', $dateFrom)
			->andWhere('t.confirmedAt <= :confirmedTo')
			->setParameter('confirmedTo', $dateTo)
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfConfirmedCommissionTransactions(DateTime $from, \DateTime $to, Localization $localization)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.currency = :currency')
			->setParameter('currency', $localization->getCurrency());

		$qb->andWhere('t.confirmedAt IS NOT NULL');

		$qb->andWhere('t.confirmedAt >= :from');
		$qb->setParameter('from', $from);

		$qb->andWhere('t.confirmedAt <= :to');
		$qb->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfCanceledTransactionsByShop(DateTime $dateFrom, \DateTime $dateTo, ?Shop $shop = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.confirmedAt >= :canceledFrom')
			->setParameter('canceledFrom', $dateFrom)
			->andWhere('t.confirmedAt <= :canceledTo')
			->setParameter('canceledTo', $dateTo)
			->andWhere('t.userCommissionAmount = 0')
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop);


		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfTransactionActiveUsers($from, $to, $localization = null, ?bool $whiteLabel = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(DISTINCT t.user)')
			->from(Transaction::class, 't')
			->andWhere('t.type IN(:types)')
			->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND]);

		if ($localization) {
			$qb->innerJoin('t.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($whiteLabel !== null && $localization !== null) {
			$qb->leftJoin('u.partnerOrganization', 'p');

			if ($whiteLabel) {
				$qb->andWhere('p.isWhiteLabel = true');
			} else {
				$qb->andWhere('(p.isWhiteLabel = false OR u.partnerOrganization IS NULL)');
			}
		}

		$query = $qb->getQuery();

		$query->setHint(Query::HINT_CUSTOM_OUTPUT_WALKER, ForceIndexWalker::class);
		$query->setHint(ForceIndexWalker::HINT_FORCE_INDEX, 'created_at');

		return $query->getSingleScalarResult();
	}

	public function getCountOfShops($from = null, $to = null, $localization = null, $withoutLogo = false, $withoutShortDescription = false, $withoutDescription = false, $withoutCover = false)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(s.id)')
			->from(Shop::class, 's')
			->where('s.active = 1');

		if ($localization) {
			$qb->andWhere('s.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('s.publishedAt >= :publishedFrom');
			$qb->setParameter('publishedFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('s.publishedAt <= :publishedTo');
			$qb->setParameter('publishedTo', $to->format('Y-m-d H:i:s'));
		}

		if ($withoutLogo) {
			$qb->andWhere('s.logo IS NULL');
		}

		if ($withoutLogo) {
			$qb->andWhere('s.cover IS NULL');
		}

		if ($withoutShortDescription) {
			$qb->leftJoin('s.descriptionBlocks', 'shortDescriptionBlock', 'WITH', 'shortDescriptionBlock.type = :shortDescription')
				->setParameter('shortDescription', DescriptionBlock::TYPE_SHORT_DESCRIPTION);

			$qb->andWhere('shortDescriptionBlock IS NULL OR shortDescriptionBlock.description IS NULL OR LENGTH(shortDescriptionBlock.description) <= 20');
		}

		if ($withoutDescription) {
			$qb->leftJoin('s.descriptionBlocks', 'longDescriptionBlock', 'WITH', 'longDescriptionBlock.type = :longDescription')
				->setParameter('longDescription', DescriptionBlock::TYPE_LONG_DESCRIPTION);

			$qb->andWhere('longDescriptionBlock IS NULL OR longDescriptionBlock.description IS NULL OR LENGTH(longDescriptionBlock.description) <= 100');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfArticles($from, $to, $localization = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(a.id)')
			->from(Article::class, 'a');

		if ($localization) {
			$qb->andWhere('a.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('a.publishedAt >= :publishedFrom');
			$qb->setParameter('publishedFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('a.publishedAt <= :publishedTo');
			$qb->setParameter('publishedTo', $to->format('Y-m-d H:i:s'));
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	/**
	 * @param DateTime|null $from
	 * @param DateTime|null $to
	 * @param Localization|null $localization
	 * @param bool $confirmedOnly
	 * @param string|null $type
	 * @param string|null $accountNumberContains
	 * @param bool $withFailedPayouts
	 * @return int|mixed|string
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function getCountOfPayouts(
		\DateTime $from = null,
		\DateTime $to = null,
		Localization $localization = null,
		bool $confirmedOnly = false,
		string $type = null,
		string $accountNumberContains = null,
		bool $withFailedPayouts = false,
		?bool $declined = null
	) {
		$qb = $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Payout::class, 'p')
			->innerJoin('p.user', 'u');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($confirmedOnly === true || $declined === true) {
			if ($from) {
				$qb->andWhere('p.confirmedAt >= :createdFrom');
			}
			if ($to) {
				$qb->andWhere('p.confirmedAt <= :createdTo');
			}
		} else {
			if ($from) {
				$qb->andWhere('p.createdAt >= :createdFrom');
			}

			if ($to) {
				$qb->andWhere('p.createdAt <= :createdTo');
			}
		}

		if ($accountNumberContains) {
			$qb->andWhere('p.accountNumber LIKE :accountNumber')
				->setParameter('accountNumber', '%' . $accountNumberContains . '%');
		}

		if ($type) {
			$qb->andWhere('p.type = :type')
				->setParameter('type', $type);
		}

		if (!$withFailedPayouts) {
			$qb->andWhere('p.failedAt IS NULL');
		}

		if ($declined === true) {
			$qb
				->innerJoin('p.transaction', 't')
				->andWhere('t.userCommissionAmount = 0')
				->andWhere('t.bonusAmount = 0')
				->andWhere('p.confirmedAt IS NOT NULL');
		}

		$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfCreatedPayouts(DateTime $from, DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Payout::class, 'p');

		$qb->andWhere('p.createdAt >= :createdFrom');
		$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		$qb->andWhere('p.createdAt <= :createdTo');
		$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));

		return $qb->getQuery()->getSingleScalarResult();
	}

	/**
	 * @param DateTime|null $from
	 * @param DateTime|null $to
	 * @param Localization|null $localization
	 * @param bool $confirmedOnly
	 * @param User|null $user
	 * @param string|null $type
	 * @param string|null $accountNumberContains
	 * @return mixed
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function getSumOfPayouts(\DateTime $from = null, \DateTime $to = null, Localization $localization = null, bool $confirmedOnly = false, User $user = null, string $type = null, string $accountNumberContains = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Payout::class, 'p')
			->join('p.user', 'u')
			->join('p.transaction', 't')
			->select('(abs(sum(t.userCommissionAmount)) + abs(sum(t.bonusAmount))) AS amount, abs(sum(t.bonusAmount)) AS bonusAmount');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($confirmedOnly) {
			$qb->andWhere('p.confirmedAt IS NOT NULL');

			if ($from) {
				$qb->andWhere('p.confirmedAt >= :createdFrom');
			}

			if ($to) {
				$qb->andWhere('p.confirmedAt <= :createdTo');
			}
		} else {
			$qb->andWhere('p.confirmedAt IS NULL');

			if ($from) {
				$qb->andWhere('p.createdAt >= :createdFrom');
			}

			if ($to) {
				$qb->andWhere('p.createdAt <= :createdTo');
			}
		}

		if ($user) {
			$qb->andWhere('p.user = :user')
				->setParameter('user', $user);
		}

		if ($accountNumberContains) {
			$qb->andWhere('p.accountNumber LIKE :accountNumber')
				->setParameter('accountNumber', '%' . $accountNumberContains . '%');
		}

		if ($type) {
			$qb->andWhere('p.type = :type')
				->setParameter('type', $type);
		}

		if ($from) {
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}
		if ($to) {
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		return (object)$qb->getQuery()->getOneOrNullResult();
	}

	public function getSuspectedTransactions(\DateTime $from = null, \DateTime $to = null, $ignoreChecked = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(SuspectedTransaction::class, 'st')
			->innerJoin('st.transaction', 't')
			->innerJoin('t.user', 'user')
			->innerJoin('t.shop', 'shop')
			->select('st');

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		return $qb;
	}

	public function getCountOfRedirections(\DateTime $from = null, \DateTime $to = null, Localization $localization = null, $groupByShops = false, ?Shop $shop = null, ?PartnerSystem $partnerSystem = null, $platform = null, ?bool $host = false, ?bool $fromAddon = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Redirection::class, 'r', 'r.id')
			->leftJoin('r.shop', 's');

		if ($from) {
			$qb->andWhere('r.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('r.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($shop) {
			$qb->andWhere('r.shop = :shop')
				->setParameter('shop', $shop);
		}

		if ($partnerSystem) {
			$qb->andWhere('s.partnerSystem = :partnerSystem')
				->setParameter('partnerSystem', $partnerSystem);
		}

		if ($host === true && $localization !== null) {
			$qb->andWhere('r.user = :user')
				->setParameter('user', $this->configuration->getUnLoggedRedirectionUser($localization));
		}

		if ($platform !== null) {
			if (is_array($platform)) {
				$qb->andWhere('r.platform IN (:platforms)')
					->setParameter('platforms', $platform);
			} else {
				$qb->andWhere('r.platform = :platform')
					->setParameter('platform', $platform);
			}
		}

		if ($fromAddon === true) {
			$qb->innerJoin('r.utm', 'utm')
				->andWhere('utm.utmSource = :addon')
				->setParameter('addon', 'addon');
		}

		if ($groupByShops) {
			$qb->select('s.id AS shopId, count(r.id) AS countOfRedirections');
			$qb->groupBy('s');

			return $qb->getQuery()->getArrayResult();
		} else {
			$qb->select('count(r.id) AS countOfRedirections');

			return $qb->getQuery()->getSingleScalarResult();
		}
	}

	public function getShopRedirectionsCounts(\DateTime $from = null, \DateTime $to = null, Localization $localization = null)
	{
		return $this->getCountOfRedirections($from, $to, $localization, true);
	}

	public function getShopTransactionsCounts(\DateTime $from = null, \DateTime $to = null, Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't', 't.id')
			->leftJoin('t.shop', 's')
			->select('s.id AS shopId, count(t.id) AS countOfTransactions');

		if ($from) {
			$qb->andWhere('t.registeredAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.registeredAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		$qb->groupBy('s');

		return $qb->getQuery()->getArrayResult();
	}

	public function getDuplicateTransactions(?\DateTime $from = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->leftJoin('t.shop', 's')
			->select('s.id AS shopId, s.name AS shopName, t.transactionId, count(t.id) AS countOfTransactions');

		$qb->andWhere('t.partnerSystem IS NOT NULL');
		$qb->andWhere('t.transactionId IS NOT NULL');

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdAt')
				->setParameter('createdAt', $from);
		}

		$qb->groupBy('t.transactionId, t.partnerSystem');

		$qb->andHaving('countOfTransactions > 1');

		return $qb->getQuery()->getResult();
	}

	public function getCountOfLeafletsByShops(\DateTime $fromDate = null, \DateTime $toDate = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Shop::class, 's')
			->select('s.id AS id, s AS shop')
			->addSelect('(SELECT count(l.id) FROM ' . Leaflet::class . ' l WHERE l.shop = s.id AND l.validSince <= :now AND l.validTill >= :now) AS countOfLeaflets')
			->addSelect('(SELECT count(lf.id) FROM ' . Leaflet::class . ' lf WHERE lf.shop = s.id AND lf.validSince > :now) AS countOfFutureLeaflets')
			->addSelect('(SELECT count(la.id) FROM ' . Leaflet::class . ' la WHERE la.shop = s.id) AS countOfAllLeaflets')
			->setParameter('now', new \DateTime());

		if ($fromDate) {
			$qb->addSelect('(SELECT count(le.id) FROM ' . Leaflet::class . ' le WHERE le.shop = s.id AND le.createdAt <= :toDate AND le.createdAt >= :fromDate) AS countOfCreatedLeaflets')
				->setParameter('fromDate', $fromDate)
				->setParameter('toDate', $toDate);
		}

		$qb->orderBy('s.priorityLeaflets', 'DESC');
		$qb->groupBy('s.id');

		return $qb;
	}

	public function getAboveAverageTransactions($minAmountMultiplier = 1)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->leftJoin('t.shop', 's')
			->leftJoin('t.user', 'u')
			->select('t');

		$qb->andWhere('(t.currency = :czk AND t.commissionAmount >= :czkMin) OR (t.currency = :eur AND t.commissionAmount >= :eurMin) OR (t.currency = :pln AND t.commissionAmount >= :plnMin) OR (t.currency = :ron AND t.commissionAmount >= :ronMin) OR (t.currency = :huf AND t.commissionAmount >= :hufMin)')
			->setParameter('czk', Currency::CZK)
			->setParameter('czkMin', 1000 * $minAmountMultiplier)
			->setParameter('eur', Currency::EUR)
			->setParameter('eurMin', 38 * $minAmountMultiplier)
			->setParameter('pln', Currency::PLN)
			->setParameter('plnMin', 166 * $minAmountMultiplier)
			->setParameter('ron', Currency::RON)
			->setParameter('ronMin', 200 * $minAmountMultiplier)
			->setParameter('huf', Currency::HUF)
			->setParameter('hufMin', 15000 * $minAmountMultiplier)
		;

		$qb->addOrderBy('t.createdAt', 'DESC');

		return $qb;
	}

	public function getCountOfUsers(\DateTime $from, \DateTime $to, $active = false, Localization $localization = null, $excludeWhiteLabelUsers = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(User::class, 'u')
			->join('u.utm', 'm')
			->select('m.utmSource, count(u.id) AS countOfUsers');

		$qb->andWhere('u.createdAt >= :createdFrom');
		$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));

		$qb->andWhere('u.createdAt <= :createdTo');
		$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));

		if ($active) {
			$qb->andWhere('u.segment = :active')
				->setParameter('active', 'active');
		}

		if ($localization) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($excludeWhiteLabelUsers) {
		}

		$qb->groupBy('m.utmSource');

		return $qb->getQuery()->getResult();
	}

	public function getPreConfirmedTransactions(\DateTime $from, \DateTime $to, $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('t')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->andWhere('td.confirmedByScoring = true');

		if ($localization) {
			$qb->innerJoin('t.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		return $qb->getQuery()->getResult();
	}

	public function getTotalSumOfAllUserCommissions($currency)
	{
		$totalSum = 0;

		/** @var Localization $localization */
		foreach ($this->localizationFacade->findLocalizations() as $localization) {
			$qb = $this->em->createQueryBuilder()
				->from(Transaction::class, 't')
				->select('sum(t.userCommissionAmount)');

			$qb->andWhere('t.currency = :currency')
				->setParameter('currency', $localization->getCurrency());

			$sum = $qb->getQuery()->getSingleScalarResult();

			$totalSum += $this->currencyFacade->convert($sum, $localization->getCurrency(), $currency);
		}

		return $totalSum;
	}

	public function getTotalIncome($currency)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('sum(t.commissionAmount)');

		$qb->andWhere('t.currency = :currency')
			->setParameter('currency', $currency);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getUserShopsStatistics(User $user)
	{
		$qb = $this->em->createQueryBuilder()
			->select('s.name, count(t.id) AS countOfTransactions, sum(t.userCommissionAmount) as sumOfUserCommissions, max(t.registeredAt) AS lastTransactionAt')
			->addSelect('(SELECT sum(t2.userCommissionAmount) FROM ' . Transaction::class . ' t2 WHERE t2.confirmedAt IS NOT NULL AND t2.shop = t.shop AND t2.user = t.user GROUP BY t2.shop) AS sumOfConfirmedUserCommissions')
			->from(Transaction::class, 't')
			->leftJoin(TransactionData::class, 'd', 'WITH', 'd.transaction = t.id')
			->leftJoin(Shop::class, 's', 'WITH', 't.shop = s.id')
			->andWhere('t.shop IS NOT NULL');

		$qb->addGroupBy('t.shop');

		$qb->andWhere('t.user = :user')
			->setParameter('user', $user);

		return $qb->getQuery()->getResult();
	}

	public function getSumOfUserTransactions(User $user, $type, $confirmed = null, $expired = false, bool $cancelled = false, bool $billable = true)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount)) as amount, count(t.id) AS countOfTransactions')
			->andWhere('t.user = :user')
			->andWhere('t.type LIKE :bonus')
			->setParameter('user', $user)
			->setParameter('bonus', $type . '%');

		if ($expired === true) {
			$qb->andWhere('td.expiredAt IS NOT NULL');
		} else {
			$qb->andWhere('td.expiredAt IS NULL');
		}

		$qb->andWhere('t.billable = :billable')
			->setParameter('billable', $billable);

		if ($confirmed === true) {
			$qb->andWhere('t.confirmedAt IS NOT NULL');
		} elseif ($confirmed === false) {
			$qb->andWhere('t.confirmedAt IS NULL');
		}

		if ($cancelled === true) {
			$qb->andWhere('t.userCommissionAmount = 0');
		}

		return (object)$qb->getQuery()->getOneOrNullResult();
	}

	public function getTotalTurnoverOfConfirmedCommissionTransactions()
	{
		$qb = $this->em->createQueryBuilder()
			->select('SUM(p.confirmedTurnover)')
			->from(PartnerSystem::class, 'p');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getUserIncome(User $user, $accountant = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't');

		if (!$accountant) {
			$qb->select('IFNULL((sum(t.commissionAmount) - sum(t.userCommissionAmount)), 0) as amount, (100-(sum(t.userCommissionAmount)/sum(t.commissionAmount))*100) AS percentage');
			$qb->andWhere('t.type = :commission')
				->setParameter('commission', Transaction::TYPE_COMMISSION);
		} else {
			$qb->select('IFNULL((sum(t.commissionAmount) - (sum(t.userCommissionAmount) + sum(t.bonusAmount))), 0) as amount');
			$qb->andWhere('t.type != :payout')
				->setParameter('payout', Transaction::TYPE_PAYOUT);
		}

		$qb->andWhere('t.user = :user')
			->setParameter('user', $user);

		$qb->andWhere('t.confirmedAt IS NOT NULL');

		$qb->addGroupBy('t.user');

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function getPartnerSystemsAccounting($automated = null, $active = null, ?DateTime $fromDate = null, ?DateTime $toDate = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(PartnerSystem::class, 'p')
			->select('p.id, p.name, p.type, p.accountingNote as note, p.confirmedTurnover AS confirmedAmount, p.lastTransactionAt')
			->leftJoin(Income::class, 'i', 'WITH', 'i.partnerSystem = p')
			->addSelect('(SELECT sum(i2.amount) FROM ' . Income::class . ' i2 WHERE i2.partnerSystem = p AND i2.receivedAt IS NULL) AS requestedIncomeAmount')
			->addSelect('(SELECT sum(i3.amount) FROM ' . Income::class . ' i3 WHERE i3.partnerSystem = p AND i3.receivedAt IS NOT NULL) AS receivedIncomeAmount')
			->addSelect('(SELECT max(i4.requestedAt) FROM ' . Income::class . ' i4 WHERE i4.partnerSystem = p AND i4.requestedAt IS NOT NULL ORDER BY i4.requestedAt DESC ) AS lastRequestedAt')
			->addSelect('(SELECT max(i5.receivedAt) FROM ' . Income::class . ' i5 WHERE i5.partnerSystem = p AND i5.receivedAt IS NOT NULL ORDER BY i5.receivedAt DESC ) AS lastReceivedAt')
			->addOrderBy('confirmedAmount', 'desc')
			->addGroupBy('p.id')
			->andWhere('p.lastTransactionAt IS NOT NULL')
		;

		if ($fromDate !== null) {
			$qb->andWhere('i.requestedAt >= :fromDate')
				->setParameter('fromDate', $fromDate);
		}

		if ($toDate !== null) {
			$qb->andWhere('i.requestedAt <= :toDate')
				->setParameter('toDate', $toDate);
		}

		if (isset($automated)) {
			if ($automated) {
				$qb->andWhere('p.accountingNote LIKE \'%#automatizovano%\'');
			} else {
				$qb->andWhere('p.accountingNote NOT LIKE \'%#automatizovano%\'');
			}
		}

		if (isset($active)) {
			if ($active) {
				$qb->andWhere('p.accountingNote NOT LIKE \'%#neaktualni%\'');
			} else {
				$qb->andWhere('p.accountingNote LIKE \'%#neaktualni%\'');
			}
		}

		return $qb->getQuery()->getResult();
	}

	public function getPartnerSystemsAccountingSums(\DateTime $fromDate, \DateTime $toDate)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Income::class, 'i')
			->select('COUNT(i.id) AS requestedCount, IFNULL(SUM(i.amount),0) requestedAmount')
			->addSelect('(SELECT COUNT(i2.id) FROM ' . Income::class . ' i2 WHERE i2.receivedAt IS NOT NULL AND i2.receivedAt >= :fromDate AND i2.receivedAt <= :toDate) AS receivedCount')
			->addSelect('(SELECT IFNULL(SUM(i3.amount),0) FROM ' . Income::class . ' i3 WHERE i3.receivedAt IS NOT NULL AND i3.receivedAt >= :fromDate AND i3.receivedAt <= :toDate) AS receivedAmount')
			->andWhere('i.requestedAt >= :fromDate')
			->andWhere('i.requestedAt <= :toDate')
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function getPartnerSystemIncomeSum(\DateTime $fromDate, \DateTime $toDate, bool $received = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Income::class, 'i')
			->select('IFNULL(SUM(i.amount),0) AS s');

		if ($received === true) {
			$qb->andWhere('i.receivedAt >= :fromDate')
				->andWhere('i.receivedAt <= :toDate');
		} else {
			$qb->andWhere('i.requestedAt >= :fromDate')
				->andWhere('i.requestedAt <= :toDate');
		}

		$qb
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUnsentEmails()
	{
		$qb = $this->em->createQueryBuilder()
			->from(Email::class, 'e')
			->select('count(e.id)')
			->andWhere('e.sentAt IS NULL')
			->andWhere('e.abortedAt IS NULL');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfSentEmails(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Email::class, 'e')
			->select('count(e.id)')
			->andWhere('e.sentAt IS NOT NULL')
			->andWhere('e.sentAt >= :from')
			->andWhere('e.sentAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfAbortedEmails(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Email::class, 'e')
			->select('count(e.id)')
			->andWhere('e.abortedAt IS NOT NULL')
			->andWhere('e.abortedAt >= :from')
			->andWhere('e.abortedAt <= :to')
			->andWhere('e.campaign != :campaign')
			->setParameter('campaign', Email::LUCKY_SHOP_CAMPAIGN_NAME)
			->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUserLogin(\DateTime $from, \DateTime $to, $method = UserLogin::METHOD_PASSWORD, $platform = null, $successfully = true)
	{
		$qb = $this->em->createQueryBuilder()
			->from(UserLogin::class, 'l')
			->select('count(l.id)')
			->andWhere('l.successfully = :successfully')->setParameter('successfully', $successfully)
			->andWhere('l.createdAt >= :from')
			->andWhere('l.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		if ($method) {
			$qb->andWhere('l.method = :method')->setParameter('method', $method);
		}

		if ($platform) {
			$qb->andWhere('l.platform = :platform')
				->setParameter('platform', $platform);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfRefundTransactions($from = null, $to = null, Localization $localization = null, $withShop = false, User $user = null, ?Shop $shop = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :refund')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND);

		$qb->innerJoin('t.user', 'u');

		if ($localization) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($withShop) {
			$qb->andWhere('t.shop IS NOT NULL');
		}

		if ($user) {
			$qb->andWhere('t.user = :user')
				->setParameter('user', $user);
		}

		if ($shop) {
			$qb->andWhere('t.shop = :shop')
				->setParameter('shop', $shop);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUniqueUsersWithRefunds($from, $to, Localization $localization = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('count(distinct u.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :refund')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND);

		$qb->innerJoin('t.user', 'u');
		$qb->andWhere('u.localization = :localization');
		$qb->setParameter('localization', $localization);

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getSumOfRefunds($from, $to, Localization $localization = null, $withShop = false)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('sum(t.bonusAmount)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :refund')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND);

		$qb->innerJoin('t.user', 'u');
		$qb->andWhere('u.localization = :localization');
		$qb->setParameter('localization', $localization);

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		if ($withShop) {
			$qb->andWhere('t.shop IS NOT NULL');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getRefundsByShops($from = null, $to = null, Localization $localization = null)
	{
		if (is_string($from)) {
			[$from, $to] = TimeRange::fromPhrase($from);
		}

		$qb = $this->em->createQueryBuilder()
			->select('t.id, l.name AS localizationName, s.name AS shopName, count(t.id) AS countOfRefunds, sum(t.bonusAmount) AS sumOfRefunds, l.currency')
			->from(Transaction::class, 't')
			->innerJoin('t.shop', 's')
			->innerJoin('s.localization', 'l')
			->andWhere('t.type = :refund')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND);

		$qb->andWhere('t.shop IS NOT NULL');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		$qb->groupBy('t.shop')
			->addOrderBy('countOfRefunds', 'desc');

		return $qb->getQuery()->getResult();
	}

	public function getRefundsByAdmins(\DateTime $from = null, \DateTime $to = null, ?Localization $localization = null, ?string $adminEmail = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('t.id, u.email AS adminEmail, count(t.id) AS countOfRefunds, sum(t.bonusAmount) AS sumOfRefunds, t.currency, l.name as localizationName, l.id as localizationId, u.id as userId')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('td.registeredByUser', 'u')
			->innerJoin('u.localization', 'l')
			->innerJoin('t.shop', 's')
			->andWhere('t.type = :refund')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND);

		$qb->andWhere('td.registeredByUser IS NOT NULL');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($adminEmail) {
			$qb->andWhere('u.email = :email')
				->setParameter('email', $adminEmail);
		}

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to->format('Y-m-d H:i:s'));
		}

		$qb->groupBy('td.registeredByUser, t.currency')
			->addOrderBy('countOfRefunds', 'desc');

		return $qb->getQuery()->getResult();
	}

	public function getCountOfResolvedFreshdeskTicketsByUser(Localization $localization, User $user, \DateTime $from = null, \DateTime $to = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id) as countOfTickets')
			->from(Ticket::class, 't')
			->andWhere('t.resolvedBy = :user')
			->setParameter('user', $user)
			->andWhere('t.localization = :localization')
			->setParameter('localization', $localization);

		if ($from) {
			$qb->andWhere('t.resolvedAt >= :resolvedFrom');
			$qb->setParameter('resolvedFrom', $from);
		}

		if ($to) {
			$qb->andWhere('t.resolvedAt <= :resolvedTo');
			$qb->setParameter('resolvedTo', $to);
		}

		return $qb
			->getQuery()
			->getSingleScalarResult();
	}

	/**
	 * @param DateTime|null $from
	 * @param DateTime|null $to
	 * @param Localization|null $localization
	 * @param string|null $type
	 * @return int|mixed|string
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function getCountOfScheduledPayouts(\DateTime $from = null, \DateTime $to = null, Localization $localization = null, string $type = null, $onlyUnConfirmed = true)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Payout::class, 'p')
			->innerJoin('p.user', 'u')
			->andWhere('p.failedAt IS NULL')
			->andWhere('p.scheduledAt IS NOT NULL');

		if ($onlyUnConfirmed) {
			$qb->andWhere('p.confirmedAt IS NULL');
		}

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('p.scheduledAt >= :from')
				->setParameter('from', $from);
		}

		if ($to) {
			$qb->andWhere('p.scheduledAt <= :to')
				->setParameter('to', $to);
		}

		if ($type) {
			$qb->andWhere('p.type = :type')
				->setParameter('type', $type);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	/**
	 * @param DateTime|null $from
	 * @param DateTime|null $to
	 * @param Localization|null $localization
	 * @param string|null $type
	 * @return int|mixed|string
	 * @throws \Doctrine\ORM\NoResultException
	 * @throws \Doctrine\ORM\NonUniqueResultException
	 */
	public function getSumOfScheduledPayouts(\DateTime $from = null, \DateTime $to = null, Localization $localization = null, string $type = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount))')
			->from(Payout::class, 'p')
			->innerJoin('p.user', 'u')
			->innerJoin('p.transaction', 't')
			->andWhere('p.confirmedAt IS NULL')
			->andWhere('p.failedAt IS NULL')
			->andWhere('p.scheduledAt IS NOT NULL');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($type) {
			$qb->andWhere('p.type = :type')
				->setParameter('type', $type);
		}

		if ($from) {
			$qb->andWhere('p.scheduledAt >= :from')
				->setParameter('from', $from);
		}

		if ($to) {
			$qb->andWhere('p.scheduledAt <= :to')
				->setParameter('to', $to);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getAverageShareCoefficient(\DateTime $from, \DateTime $to, $currency)
	{
		$qb = $this->em->createQueryBuilder()
			->select('IFNULL(avg(d.shareCoefficient), 0)')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'd')
			->andWhere('t.createdAt >= :from')
			->andWhere('t.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to)
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.currency = :currency')
			->andWhere('d.shareCoefficient > 0')
			->setParameter('currency', $currency);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getIncome(\DateTime $from, \DateTime $to, $currency)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->andWhere('t.createdAt >= :from')
			->andWhere('t.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to)
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.currency = :currency')
			->setParameter('currency', $currency);

//        if (!$inPercentage) {
//            $qb->select('IFNULL(sum(t.commissionAmount)-sum(t.userCommissionAmount), 0)');
//        } else {
//            $qb->select('IFNULL((sum(t.commissionAmount)-sum(t.userCommissionAmount))/sum(t.commissionAmount), 0)');
//        }

		$qb->addSelect('IFNULL(sum(t.commissionAmount)-sum(t.userCommissionAmount), 0) AS income');
		$qb->addSelect('IFNULL((sum(t.commissionAmount)-sum(t.userCommissionAmount))/sum(t.commissionAmount), 0) AS incomeInPercentage');

		return $qb->getQuery()->getOneOrNullResult();
//        return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfMoneyRewards(\DateTime $from, \DateTime $to, Localization $localization, $onlyUsed = false)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(m.id)')
			->from(MoneyReward::class, 'm')
			->leftJoin('m.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->setParameter('from', $from)
			->setParameter('to', $to);

		if ($onlyUsed) {
			$qb->andWhere('m.usedAt IS NOT NULL')
				->andWhere('m.usedAt >= :from')
				->andWhere('m.usedAt <= :to');
		} else {
			$qb->andWhere('m.createdAt >= :from')
				->andWhere('m.createdAt <= :to');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getSumOfMoneyRewards(\DateTime $from, \DateTime $to, Localization $localization, $onlyUsed = false)
	{
		$qb = $this->em->createQueryBuilder()
			->select('sum(m.amount)')
			->from(MoneyReward::class, 'm')
			->leftJoin('m.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('m.createdAt >= :from')
			->andWhere('m.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		if ($onlyUsed) {
			$qb->andWhere('m.usedAt IS NOT NULL')
				->andWhere('m.usedAt >= :from')
				->andWhere('m.usedAt <= :to');
		} else {
			$qb->andWhere('m.createdAt >= :from')
				->andWhere('m.createdAt <= :to');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getAverageShareRewardCoefficient(\DateTime $from, \DateTime $to, Localization $localization)
	{
		$qb = $this->em->createQueryBuilder()
			->select('avg(s.shareCoefficient)')
			->from(ShareReward::class, 's')
			->innerJoin('s.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('s.createdAt >= :from')
			->andWhere('s.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfShareRewardCoefficient(\DateTime $from, \DateTime $to, Localization $localization)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(s.shareCoefficient)')
			->from(ShareReward::class, 's')
			->innerJoin('s.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('s.createdAt >= :from')
			->andWhere('s.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getActiveUsersWithParentUser(User $parentUser)
	{
		return $this->context->query(
			'SELECT u.id, u.email, u.created_at, sum(t.user_commission_amount) as turnover, sum(t.bonus_amount) as bonus,
min(t.confirmed_at) as confirmed_at
FROM tipli_transactions_transaction t
LEFT JOIN tipli_account_user u ON t.user_id = u.id AND (t.user_id = u.id)
WHERE (u.parent_id = ? AND DATEDIFF(confirmed_at, u.created_at) < 365 AND u.created_at > ?)
GROUP BY u.id
HAVING confirmed_at IS NOT NULL AND turnover > 0',
			$parentUser->getId(),
			new \DateTime('2018-10-01')
		);
	}

	public function getActiveUsersWithParentUserOldMetric(User $parentUser)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('u.id, u.email, u.createdAt, sum(t.userCommissionAmount) as turnover, sum(t.bonusAmount) as bonus, min(t.confirmedAt) as confirmedAt')
			->leftJoin('t.user', 'u', 'WITH', 't.user = u.id')
			->andWhere('u.parentUser = :parentUser')
			->andWhere('u.createdAt < :endDate')
			->setParameter('parentUser', $parentUser)
			->setParameter('endDate', new \DateTime('2018-10-01'))
			->groupBy('u.id')
			->andHaving('confirmedAt IS NOT NULL')
			->andHaving('turnover > 0');

		return $qb->getQuery()->getResult();
	}

	public function getSumOfUsersTransactions(User $parentUser = null, array $utms = [])
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('sum(t.userCommissionAmount) as turnover, sum(t.bonusAmount) as bonus, min(t.confirmedAt) as confirmedAt')
			->leftJoin('t.user', 'u', 'WITH', 't.user = u.id')
			->andHaving('confirmedAt IS NOT NULL')
			->andHaving('turnover > 0');

		if ($parentUser) {
			$qb->andWhere('u.parentUser = :parentUser')
				->setParameter('parentUser', $parentUser);
		}

		if (!empty($utms)) {
			$qb->andWhere('u.utm IN (:utms)')
				->setParameter('utms', $utms);
		}

		return $qb->getQuery()->getResult();
	}

	public function getCountOfActiveUsersByParentUserWithTransactionType(User $parentUser, $transactionType): int
	{
		$qb = $this->em->createQueryBuilder()
			->select('u.id')
			->from(User::class, 'u')
			->innerJoin(Transaction::class, 't', 'WITH', 't.user = u.id')
			->andWhere('u.parentUser = :user')
			->setParameter('user', $parentUser)
			->andWhere('u.segment = :segment')
			->andWhere('u.id IN (SELECT DISTINCT u2.id FROM ' . Transaction::class . ' t2 INNER JOIN ' . User::class . ' u2 WITH (u2.id = t2.user) WHERE t2.type = :type AND t.user = u.id)')
			->setParameter('type', $transactionType)
			->setParameter('segment', User::SEGMENT_ACTIVE)
			->andHaving('sum(t.userCommissionAmount) > 0')
			->andHaving('min(t.confirmedAt) IS NOT NULL')
			->groupBy('u');

		return count($qb->getQuery()->getResult());
	}

	public function getUsersBalancesByUtms(array $utms)
	{
		$utmIds = [];
		foreach ($utms as $utm) {
			$utmIds[] = $utm->getId();
		}

		return $this->context->query(
			'SELECT u.id, u.email, u.created_at, sum(t.user_commission_amount) as turnover, sum(t.bonus_amount) as bonus,
min(t.confirmed_at) as confirmed_at
FROM tipli_transactions_transaction t
LEFT JOIN tipli_account_user u ON t.user_id = u.id AND (t.user_id = u.id)
WHERE u.utm_id IN (?) AND DATEDIFF(confirmed_at, u.created_at) < 365 AND u.created_at > ? AND t.type != \'payout\'
GROUP BY u.id
HAVING confirmed_at IS NOT NULL AND turnover > 0',
			$utmIds,
			new \DateTime('2018-10-01')
		);
	}

	public function getUsersBalancesByUtmsOldMetric(array $utms)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('u.id, u.email, u.createdAt, sum(t.userCommissionAmount) as turnover, sum(t.bonusAmount) as bonus, min(t.confirmedAt) as confirmedAt')
			->leftJoin('t.user', 'u', 'WITH', 't.user = u.id')
			->andWhere('u.utm IN (:utms)')
			->andWhere('u.createdAt < :endDate')
			->setParameter('endDate', new \DateTime('2018-10-01'))
			->setParameter('utms', $utms)
			->groupBy('u.id')
			->andHaving('confirmedAt IS NOT NULL')
			->andHaving('turnover > 0');

		return $qb->getQuery()->getResult();
	}

	public function getShopsReport(\DateTime $fromDate, \DateTime $toDate, Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('t, l.id as localization, s.id AS id, p.id as partnerSystemId, p.name as partnerSystemName, s.name as shop, count(t.shop) as countOfTransactions, sum(td.turnover) as sumOfTurnover, sum(td.income) as sumOfIncome, s.averageRegistrationPeriod AS averageRegistrationPeriod, s.averageConfirmationPeriod AS averageConfirmationPeriod')
			->addSelect('s, sd, td, hct, rt, ps')
			->innerJoin('t.shop', 's')
			->innerJoin('s.shopData', 'sd')
			->innerJoin('s.partnerSystem', 'p')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('s.localization', 'l')
			->leftJoin('t.homeCreditTransaction', 'hct')
			->leftJoin('t.rondoTransaction', 'rt')
			->leftJoin('t.partnerSystem', 'ps')
			->andWhere('t.createdAt BETWEEN :fromDate AND :toDate')
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate)
			->groupBy('s.id')
			->orderBy('sumOfTurnover', 'DESC');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getResult();
	}

	public function getPartnerSystemsReport(\DateTime $fromDate, \DateTime $toDate)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('ps.id AS id, ps.name as system, count(t.shop) as countOfTransactions, count(DISTINCT t.user) as countOfUsers, sum(td.turnover) as sumOfTurnover, sum(td.income) as sumOfIncome')
			->innerJoin('t.partnerSystem', 'ps')
			->innerJoin('t.transactionData', 'td')
			->andWhere('t.createdAt BETWEEN :fromDate AND :toDate')
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate)
			->groupBy('ps.id')
			->orderBy('sumOfTurnover', 'DESC');

		return $qb->getQuery()->getResult();
	}

	public function getTurnOverByPartnerSystem(PartnerSystem $partnerSystem, DateTime $fromDate, DateTime $toDate): float
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->select('IFNULL(SUM(td.turnover),0)')
			->innerJoin('t.transactionData', 'td')
			->andWhere('t.confirmedAt BETWEEN :fromDate AND :toDate')
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate)
			->andWhere('t.partnerSystem = :partnerSystem')
			->setParameter('partnerSystem', $partnerSystem)
			->andWhere('t.type = :commission AND t.confirmedAt IS NOT NULL')
			->setParameter('commission', Transaction::TYPE_COMMISSION);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getPausedCashbackShops(?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('s, notes, sd, partnerSystem, l')
			->from(Shop::class, 's')
			->leftJoin('s.shopNotes', 'notes')
			->innerJoin('s.shopData', 'sd')
			->innerJoin('s.partnerSystem', 'partnerSystem')
			->innerJoin('s.localization', 'l')
			->andWhere('s.cashbackAllowed = true')
			->andWhere('sd.pausedAt < CURRENT_TIMESTAMP()');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb;
	}

	public function getCountOfActivationsByFirstTransaction($from = null, $to = null, Localization $localization = null, array $utms = [], ?int $maxDaysAfterRegistration = null, ?bool $withReferral = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(u.id)')
			->from(User::class, 'u')
			->innerJoin(SegmentData::class, 'd', 'WITH', 'd.user = u.id');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('d.firstTransactionAt >= :from');
			$qb->setParameter('from', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('d.firstTransactionAt <= :to');
			$qb->setParameter('to', $to->format('Y-m-d H:i:s'));
		}

		if ($utms) {
			$qb->andWhere('u.utm IN (:utms)')
				->setParameter('utms', $utms);
		}

		if ($maxDaysAfterRegistration !== null) {
			$qb->andWhere('DATE_DIFF(u.createdAt, d.firstTransactionAt) <= :maxDays') // @todo
			->setParameter('maxDays', $maxDaysAfterRegistration);
		}

		if ($withReferral !== null) {
			if ($withReferral === false) {
				$qb->andWhere('u.parentUser IS NULL');
			} else {
				$qb->andWhere('u.parentUser IS NOT NULL');
				$qb->innerJoin('u.utm', 'utm')
					->andWhere('utm.utmSource = :doporuceni')
					->setParameter('doporuceni', 'doporuceni');
			}
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUniqueParentReferralUsers($from = null, $to = null, Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(DISTINCT u.parentUser)')
			->andWhere('u.parentUser IS NOT NULL')
			->from(User::class, 'u');

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($from) {
			$qb->andWhere('u.createdAt >= :from');
			$qb->setParameter('from', $from->format('Y-m-d H:i:s'));
		}

		if ($to) {
			$qb->andWhere('u.createdAt <= :to');
			$qb->setParameter('to', $to->format('Y-m-d H:i:s'));
		}

		$qb->innerJoin('u.utm', 'utm')
			->andWhere('utm.utmSource = :doporuceni')
			->setParameter('doporuceni', 'doporuceni');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getSumOfTurnoverUsersActivatedByFirstTransaction(\DateTime $from, \DateTime $to, Localization $localization = null, $confirmed = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->innerJoin('t.user', 'u')
			->innerJoin(SegmentData::class, 'sd', 'WITH', 'sd.user = u.id')
			->innerJoin('t.transactionData', 'd');

		$qb->addSelect('IFNULL(sum(d.turnover), 0) AS turnover');

		$qb->andWhere('t.type != :payout')
			->setParameter('payout', Transaction::TYPE_PAYOUT);

		if ($localization) {
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		if ($confirmed) {
			$qb->andWhere('t.confirmedAt IS NOT NULL');
		} else {
			$qb->andWhere('t.confirmedAt IS NULL');
		}

		$qb->andWhere('sd.firstTransactionAt >= :from');
		$qb->setParameter('from', $from->format('Y-m-d H:i:s'));

		$qb->andWhere('sd.firstTransactionAt <= :to');
		$qb->setParameter('to', $to->format('Y-m-d H:i:s'));

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getSumOfConfirmedBonus(\DateTime $from, \DateTime $to, Localization $localization = null): float
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't');

		$qb->addSelect('IFNULL(sum(t.bonusAmount), 0) AS bonus');

		$qb->andWhere('t.type != :payout')
			->andWhere('t.confirmedAt IS NOT NULL')
			->andWhere('t.confirmedAt >= :fromDate')
			->andWhere('t.confirmedAt <= :toDate')
			->setParameter('fromDate', $from)
			->setParameter('toDate', $to)
			->setParameter('payout', Transaction::TYPE_PAYOUT);

		if ($localization) {
			$qb
				->innerJoin('t.user', 'u')
				->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getSumOfCommissionAmount(\DateTime $from, \DateTime $to, Localization $localization = null, ?bool $confirmed = null): float
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't');

		$qb->addSelect('IFNULL(sum(t.commissionAmount), 0) AS commissionAmount');

		$qb->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION);

		if ($localization) {
			$qb
				->innerJoin('t.user', 'u')
				->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($confirmed === true) {
			$qb
				->andWhere('t.confirmedAt IS NOT NULL')
				->andWhere('t.confirmedAt >= :fromDate')
				->andWhere('t.confirmedAt <= :toDate');
		} else {
			$qb
				->andWhere('t.createdAt >= :fromDate')
				->andWhere('t.createdAt <= :toDate');
		}

		$qb
			->setParameter('fromDate', $from)
			->setParameter('toDate', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getTransactionsAmounts($currency, \DateTime $fromDate, \DateTime $toDate)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'd');

		$qb->addSelect('IFNULL(sum(t.commissionAmount), 0) AS sumOfCommissionAmount')
			->addSelect('IFNULL(sum(t.userCommissionAmount), 0) AS sumOfUserCommissionAmount')
			->addSelect('IFNULL(sum(t.bonusAmount), 0) AS sumOfBonusAmount')
//            ->addSelect('IFNULL(sum(d.turnover), 0) AS sumOfTurnover')
			->addSelect('IFNULL(sum(d.originalTurnover), 0) AS sumOfOriginalTurnover')
			->addSelect('IFNULL(sum(d.income), 0) AS sumOfIncome')
			->addSelect('avg(d.shareCoefficient) AS shareCoefficient')
			->addSelect('IFNULL(count(t.id), 0) AS countOfTransactions')//            ->addSelect('IFNULL(count(distinct t.user), 0) AS countOfActiveUsers')
		;

		$qb->andWhere('t.currency = :currency')
			->andWhere('t.createdAt >= :fromDate')
			->andWhere('t.createdAt <= :toDate')
			->setParameter('currency', $currency)
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate);

		$qb->andWhere('t.type != :payout')
			->setParameter('payout', Transaction::TYPE_PAYOUT);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function getConfirmedTurnover($currency, \DateTime $fromDate, \DateTime $toDate)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'd');

		$qb->addSelect('IFNULL(sum(d.turnover), 0) AS sumOfConfirmedTurnover');

		$qb->andWhere('t.currency = :currency')
			->andWhere('t.createdAt >= :fromDate')
			->andWhere('t.createdAt <= :toDate')
			->andWhere('t.confirmedAt IS NOT NULL')
			->setParameter('currency', $currency)
			->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate);

		$qb->andWhere('t.type != :payout')
			->setParameter('payout', Transaction::TYPE_PAYOUT);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfDeals(\DateTime $from, \DateTime $to, Localization $localization)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(d.id)')
			->from(Deal::class, 'd')
			->andWhere('d.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('d.createdAt >= :from')
			->andWhere('d.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfReactivatedUsers(\DateTime $from, \DateTime $to, ?Localization $localization = null): int
	{
		$activeTo = clone $from;
		$activeTo->modify('- 6 months')->modify('- 1 second');

		$nonTransactionsFrom = clone $from;
		$nonTransactionsFrom->modify('- 6 months');

		$nonTransactionsTo = clone $from;
		$nonTransactionsTo->modify('-1 second');

		return $this->context->query('SELECT count(DISTINCT t.user_id) AS c FROM tipli_transactions_transaction t
  INNER JOIN tipli_account_user u ON (t.user_id = u.id)
  WHERE u.localization_id = ?
  AND t.created_at >= ? AND t.created_at <= ? AND t.type="commission" AND t.user_id IS NOT NULL
  AND t.user_id IN (SELECT t2.user_id FROM tipli_transactions_transaction t2 WHERE t2.created_at <= ? AND t2.type="commission" AND t2.user_id IS NOT NULL)
  AND t.user_id NOT IN (SELECT t3.user_id FROM tipli_transactions_transaction t3 WHERE t3.created_at >= ? AND t3.created_at <= ? AND t3.type="commission" AND t3.user_id IS NOT NULL);
        ', $localization->getId(), $from, $to, $activeTo, $nonTransactionsFrom, $nonTransactionsTo)->fetch()->c;
	}

	public function getAverageTransactionConfirmationTime(\DateTime $from, \DateTime $to, Localization $localization): int
	{
		return $this->context->query('
            SELECT IFNULL(avg(TIMESTAMPDIFF(SECOND, t.created_at, t.confirmed_at)), 0) as r
            FROM tipli_transactions_transaction t
            INNER JOIN tipli_account_user u ON (t.user_id = u.id)
            WHERE t.confirmed_at >= ? AND t.confirmed_at <= ? AND t.type = \'commission\' AND t.confirmed_at IS NOT NULL
            AND u.localization_id = ?
        ', $from, $to, $localization->getId())->fetch()->r;
	}

	public function getAverageTransactionRegistrationTime(\DateTime $from, \DateTime $to, Localization $localization): int
	{
		return $this->context->query('
            SELECT IFNULL(avg(TIMESTAMPDIFF(SECOND, t.registered_at, t.created_at)), 0) as r
            FROM tipli_transactions_transaction t
            INNER JOIN tipli_account_user u ON (t.user_id = u.id)
            WHERE t.registered_at >= ? AND t.registered_at <= ?
            AND u.localization_id = ? AND t.type = \'commission\'
        ', $from, $to, $localization->getId())->fetch()->r;
	}

	public function getCountOfUsersByPlatform(string $platform, \DateTime $from, \DateTime $to, Localization $localization)
	{
		return $this->em->createQueryBuilder()
			->select('count(sd.id)')
			->from(SegmentData::class, 'sd')
			->innerJoin('sd.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('sd.registrationPlatform = :platform')
			->setParameter('platform', $platform)
			->andWhere('u.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('u.createdAt <= :to')
			->setParameter('to', $to)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfUsersUsingMobileApp(\DateTime $from, \DateTime $to, Localization $localization)
	{
		return $this->em->createQueryBuilder()
			->select('count(DISTINCT mr.user)')
			->from(MobileRequest::class, 'mr')
			->andWhere('mr.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('mr.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('mr.createdAt <= :to')
			->setParameter('to', $to)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfMobileAppInteractions(\DateTime $from, \DateTime $to, Localization $localization)
	{
		return $this->em->createQueryBuilder()
			->select('count(mr.user)')
			->from(MobileRequest::class, 'mr')
			->andWhere('mr.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('mr.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('mr.createdAt <= :to')
			->setParameter('to', $to)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfActiveUsersMobileApp(\DateTime $from, \DateTime $to, Localization $localization)
	{
		return $this->em->createQueryBuilder()
			->select('count(DISTINCT u.id)')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('t.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('td.platform IN (:platforms)')
			->setParameter('platforms', [MobileDevice::PLATFORM_ANDROID, MobileDevice::PLATFORM_IOS])
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getHostTurnover(\DateTime $from, \DateTime $to, Localization $localization)
	{
		return $this->em->createQueryBuilder()
			->select('COALESCE(SUM(td.turnover), 0)')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('t.user', 'u')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->andWhere('t.user = :user')
			->setParameter('user', $this->configuration->getUnLoggedRedirectionUser($localization))
			->getQuery()
			->getSingleScalarResult();
	}

	public function getShopsHostTurnovers(Localization $localization): array
	{
		return $this->em->createQueryBuilder()
			->select('s.id, COALESCE(SUM(td.turnover), 0) as turnover')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('t.shop', 's')
			->innerJoin('t.user', 'u')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->andWhere('u = :user')
			->setParameter('user', $this->configuration->getUnLoggedRedirectionUser($localization))
			->groupBy('s.id')
			->getQuery()
			->getResult();
	}

	public function getTurnover(DateTime $from, DateTime $to, ?Localization $localization, bool $confirmed = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COALESCE(SUM(td.turnover), 0)')
			->from(Transaction::class, 't')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('t.user', 'u')
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
		;

		if ($confirmed === true) {
			$qb->andWhere('t.confirmedAt IS NOT NULL');
		} elseif ($confirmed === false) {
			$qb->andWhere('t.confirmedAt IS NULL');
		}

		if ($localization !== null) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization)
			;
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfPayments($currency, \DateTime $paidFrom, \DateTime $paidTo, $withoutPayout = false)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Payment::class, 'p')
			->select('count(p.id)')
			->andWhere('p.paidAt >= :paidFrom')
			->andWhere('p.paidAt <= :paidTo')
			->setParameters(['paidFrom' => $paidFrom, 'paidTo' => $paidTo]);

		if ($currency) {
			$qb->andWhere('p.currency = :currency')
				->setParameter('currency', $currency);
		}

		if ($withoutPayout) {
			$qb->andWhere('p.payout IS NULL');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getPaymentsWithoutPayout(DateTime $paidFrom, \DateTime $paidTo)
	{
		$qb = $this->em->createQueryBuilder()
			->select('p')
			->from(Payment::class, 'p')
			->andWhere('p.paidAt >= :paidFrom')
			->andWhere('p.paidAt <= :paidTo')
			->setParameters(['paidFrom' => $paidFrom, 'paidTo' => $paidTo])
			->andWhere('p.payout IS NULL');

		return $qb->getQuery()
			->getResult();
	}

	public function getPayoutsWithoutPayment(DateTime $dateFrom, \DateTime $dateTo)
	{
		$qb = $this->em->createQueryBuilder()
			->select('p')
			->from(Payout::class, 'p')
			->leftJoin('p.payments', 'payments')
			->innerJoin('p.user', 'u')
			->andWhere('p.failedAt IS NULL')
			->andWhere('p.scheduledAt IS NOT NULL')
			->andWhere('p.scheduledAt >= :from')
			->setParameter('from', $dateFrom)
			->andWhere('p.scheduledAt <= :to')
			->setParameter('to', $dateTo)
			->andWhere('payments IS NULL');

		return $qb->getQuery()
			->getResult();
	}

	public function getSumOfPayments($currency, \DateTime $paidFrom, \DateTime $paidTo)
	{
		$qb = $this->em->createQueryBuilder()
			->from(Payment::class, 'p')
			->select('sum(p.amount)')
			->andWhere('p.paidAt >= :paidFrom')
			->andWhere('p.paidAt <= :paidTo')
			->andWhere('p.currency = :currency')
			->setParameters(['currency' => $currency, 'paidFrom' => $paidFrom, 'paidTo' => $paidTo]);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findMultiAccountActiveUsersEmails()
	{
		$qb = $this->em->createQueryBuilder()
			->select('u.email')
			->from(User::class, 'u')
			->andWhere('u.active = 1')
			->andWhere('u.emailVerifiedAt IS NOT NULL')
			->groupBy('u.email')
			->andHaving('count(u.email) > 1');

		return $qb->getQuery()->getResult();
	}

	public function getCountOfTicketsInSoutezDoporuc(User $user)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(u.id)')
			->from(User::class, 'u')
			->andWhere('u.parentUser = :parentUser')
			->andWhere('u.createdAt >= :fromDate')
			->andWhere('u.createdAt <= :toDate')
			->andWhere('u.segment = :active')
			->setParameter('parentUser', $user)
			->setParameter('fromDate', new \DateTime('2019-10-15 00:00:00'))
			->setParameter('toDate', new \DateTime('2019-11-31 00:00:00'))
			->setParameter('active', User::SEGMENT_ACTIVE);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfReactivatedUsersByUtmIds(array $utms, \DateTime $dateFrom, \DateTime $dateTo)
	{
		$utmIds = array_keys($utms);
		$activeTo = clone $dateFrom;
		$activeTo->modify('- 6 months')->modify('- 1 second');

		$nonTransactionsFrom = clone $dateFrom;
		$nonTransactionsFrom->modify('- 6 months');

		$nonTransactionsTo = clone $dateFrom;
		$nonTransactionsTo->modify('-1 second');

		return $this->context->query('
            SELECT count(DISTINCT t.user_id) AS c
            FROM tipli_transactions_transaction t
            INNER JOIN tipli_account_user u ON (t.user_id = u.id)
            WHERE t.utm_id IN (?) AND DATE(t.created_at) >= ? AND DATE(t.created_at) <= ? AND t.type="commission" AND t.user_id IS NOT NULL
             AND EXISTS (
                SELECT t.user_id
                FROM tipli_transactions_transaction t2
                WHERE DATE(t2.created_at) <= ? AND t2.type="commission"
                AND t.user_id = t2.user_id
                GROUP BY t.user_id
            ) AND NOT EXISTS (
                SELECT t.user_id
                FROM tipli_transactions_transaction t3
                WHERE DATE(t3.created_at) >= ? AND DATE(t3.created_at) <= ?
                AND t3.type="commission" AND t3.user_id = t.user_id
                GROUP BY t.user_id
        )', $utmIds, $dateFrom, $dateTo, $activeTo, $nonTransactionsFrom, $nonTransactionsTo)->fetch()->c;

		/*
		 * old solution
		return $this->context->query('SELECT count(DISTINCT t.user_id) AS c FROM tipli_transactions_transaction t
		  INNER JOIN tipli_account_user u ON (t.user_id = u.id)
		  WHERE t.utm_id IN (?) AND DATE(t.created_at) >= ? AND DATE(t.created_at) <= ? AND t.type="commission" AND t.user_id IS NOT NULL
		  AND t.user_id IN (SELECT t2.user_id FROM tipli_transactions_transaction t2 WHERE DATE(t2.created_at) <= ? AND t2.type="commission" AND t2.user_id IS NOT NULL)
		  AND t.user_id NOT IN (SELECT t3.user_id FROM tipli_transactions_transaction t3 WHERE DATE(t3.created_at) >= ? AND DATE(t3.created_at) <= ? AND t3.type="commission" AND t3.user_id IS NOT NULL);
		', $utmIds, $dateFrom, $dateTo, $activeTo, $nonTransactionsFrom, $nonTransactionsTo)->fetch()->c;
	}
		*/
	}

	public function getCountOfPurchases($currency, \DateTime $startedAt, \DateTime $endedAt)
	{
		$countOfPurchases = $this->context->query('SELECT COUNT(dateCreated) as c
			FROM (SELECT DATE(t.created_at) AS dateCreated, t.user_id AS t_user_id
			FROM tipli_transactions_transaction t WHERE t.shop_id IS NOT NULL AND t.type = "commission" AND t.currency = ? AND t.created_at >= ? AND t.created_at <= ?
 			GROUP BY t.shop_id, dateCreated, t_user_id
			) c', $currency, $startedAt, $endedAt)->fetch();

		return $countOfPurchases['c'];
	}

	public function getCountOfActivatedUsersByUtmIds(array $utms, \DateTime $from, \DateTime $to): int
	{
		$qb = $this->em->createQueryBuilder()
			->select('t')
			->from(Transaction::class, 't')
			->leftJoin(Transaction::class, 't2', 'WITH', 't2.user = t.user')
			->andWhere('t.utm IN (:utms)')->setParameter('utms', $utms)
			->andWhere('DATE(t.createdAt) >= :dateFrom')->setParameter('dateFrom', $from->format('Y-m-d'))
			->andWhere('DATE(t.createdAt) <= :dateTo')->setParameter('dateTo', $to->format('Y-m-d'))
			->andWhere('t.type IN (:types)')->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND])
			->addGroupBy('t.id')
			->having('count(t2.id) = 1');

		return count($qb->getQuery()->getScalarResult());
	}

	/**
	 * @param DateTime $from
	 * @param DateTime $to
	 * @param array $utms
	 * @param Localization $localization
	 * @return int
	 */
	public function getCountOfAddonInstalls(\DateTime $from, \DateTime $to, array $utms = [], Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(t.id) as s')
			->from(Transaction::class, 't')
			->andWhere('DATE(t.createdAt) >= :dateFrom')->setParameter('dateFrom', $from->format('Y-m-d'))
			->andWhere('DATE(t.createdAt) <= :dateTo')->setParameter('dateTo', $to->format('Y-m-d'))
			->andWhere('t.type = :type')->setParameter('type', Transaction::TYPE_BONUS_ADDON);

		if (!empty($utms)) {
			$qb->andWhere('t.utm IN (:utms)')->setParameter('utms', $utms);
		}

		if ($localization) {
			$qb->innerJoin('t.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUsersBySendingPolicy()
	{
		return $this->em->createQueryBuilder()
			->select('count(DISTINCT s.user) as count, s.messageType, s.contentType')
			->from(SendingPolicy::class, 's')
			->leftJoin('s.user', 'u')
			->where('u.active = true')
			->groupBy('s.messageType')
			->addGroupBy('s.contentType')
			->getQuery()->getResult();
	}

	public function getCountOfUnsubscribedUsers()
	{
		return $this->em->createQueryBuilder()
			->select('COUNT(u) as c')
			->from(User::class, 'u')
			->where('u.emailsUnsubscribedAt IS NOT NULL')
			->getQuery()->getSingleScalarResult();
	}

	/**
	 * @param DateTime $fromDate
	 * @param DateTime $toDate
	 * @param string $metric
	 * @param string $platform
	 * @param Localization $localization
	 * @return int
	 */
	public function getMobileAppMetrics(\DateTime $fromDate, \DateTime $toDate, string $metric, string $platform = null, Localization $localization = null)
	{
		if ($metric === 'logins') {
			return $this->getMobileAppCountOfLogins($fromDate, $toDate, $platform, $localization);
		} elseif ($metric === 'active') {
			return $this->getMobileAppCountOfMobileUsers($fromDate, $toDate, $platform, $localization);
		}

		$qb = $this->em->createQueryBuilder()
			->select('COUNT(m.id) as c')
			->from(MobileDevice::class, 'm');

		if ($platform) {
			$qb->andWhere('m.platform = :platform')
				->setParameter('platform', $platform);
		} else {
			$qb->andWhere('m.platform IN (:platforms)')
				->setParameter('platforms', [MobileDevice::PLATFORM_ANDROID, MobileDevice::PLATFORM_IOS]);
		}

		if ($metric === 'installs') {
			$qb->andWhere('m.installedAt IS NOT NULL AND m.installedAt >= :fromDate AND m.installedAt <= :toDate');
		} elseif ($metric === 'logouts') {
			$qb->andWhere('m.lastLogoutAt IS NOT NULL AND m.lastLogoutAt >= :fromDate AND m.lastLogoutAt <= :toDate');
		}

		if ($localization) {
			$qb->innerJoin('m.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		$qb->setParameter('fromDate', $fromDate)
			->setParameter('toDate', $toDate);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getMobileAppCountOfLogins(\DateTime $fromDate, \DateTime $toDate, string $platform, ?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(ul.id)')
			->from(UserLogin::class, 'ul')
			->andWhere('ul.createdAt >= :fromDate')
			->setParameter('fromDate', $fromDate)
			->andWhere('ul.createdAt <= :toDate')
			->setParameter('toDate', $toDate);

		if ($platform) {
			$qb->andWhere('ul.platform = :platform')
				->setParameter('platform', $platform);
		} else {
			$qb->andWhere('ul.platform IN (:platforms)')
				->setParameter('platforms', [MobileDevice::PLATFORM_ANDROID, MobileDevice::PLATFORM_IOS]);
		}

		if ($localization) {
			$qb->innerJoin('ul.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getMobileAppCountOfMobileUsers(\DateTime $fromDate, \DateTime $toDate, ?string $platform = null, ?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(DISTINCT mr.user)')
			->from(MobileRequest::class, 'mr')
			->andWhere('mr.createdAt >= :fromDate')
			->setParameter('fromDate', $fromDate)
			->andWhere('mr.createdAt <= :toDate')
			->setParameter('toDate', $toDate);

		if ($platform) {
			$qb->andWhere('mr.platform = :platform')
				->setParameter('platform', $platform);
		} else {
			$qb->andWhere('mr.platform IN (:platforms)')
				->setParameter('platforms', [MobileDevice::PLATFORM_ANDROID, MobileDevice::PLATFORM_IOS]);
		}

		if ($localization) {
			$qb->innerJoin('mr.user', 'u');
			$qb->andWhere('u.localization = :localization');
			$qb->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUsersUsingAddon(\DateTime $fromDate, \DateTime $toDate, ?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(DISTINCT sv.user)')
			->from(ShopVisit::class, 'sv')
			->innerJoin('sv.user', 'u')
			->andWhere('sv.createdAt >= :fromDate')
			->setParameter('fromDate', $fromDate)
			->andWhere('sv.createdAt <= :toDate')
			->setParameter('toDate', $toDate);

		if ($localization) {
			$qb->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCommonBadRequests(\DateTime $fromDate, \DateTime $toDate, $count = 500)
	{
		$qb = $this->em->createQueryBuilder()
			->select('b')
			->from(BadRequest::class, 'b')
			->addGroupBy('b.url')
			->andHaving('COUNT(b.id) > :count')->setParameter('count', $count)
			->addOrderBy('COUNT(b.id)', 'DESC');

		if ($fromDate) {
			$qb->andWhere('b.createdAt >= :fromDate')->setParameter('fromDate', $fromDate);
		}

		if ($toDate) {
			$qb->andWhere('b.createdAt <= :toDate')->setParameter('toDate', $toDate);
		}

		return $qb->getQuery()->getResult();
	}

	public function getCountOfPayoutTransactions(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_PAYOUT);

		if ($from) {
			$qb->andWhere('t.createdAt >= :createdFrom');
			$qb->setParameter('createdFrom', $from);
		}

		if ($to) {
			$qb->andWhere('t.createdAt <= :createdTo');
			$qb->setParameter('createdTo', $to);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function countOfZasilkovnaPackets(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Packet::class, 'p')
			->andWhere('p.createdAt >= :createdFrom')->setParameter('createdFrom', $from)
			->andWhere('p.createdAt <= :createdTo')->setParameter('createdTo', $to);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getDuplicatePayouts(\DateTime $from): int
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id) as c')
			->from(Transaction::class, 't')
			->where('t.type = :type')
			->setParameter('type', Transaction::TYPE_PAYOUT)
			->andWhere('t.createdAt >= :createdAt')
			->setParameter('createdAt', $from)
			->addGroupBy('t.user')
			->addGroupBy('t.userCommissionAmount')
			->having('c > 1')
			->getQuery();

		return count($qb->getScalarResult());
	}

	public function getUsersWithNegativeBalance(DateTime $from)
	{
		$payoutUsers = $this->em->createQueryBuilder()
			->select('u')
			->from(User::class, 'u')
			->leftJoin(Payout::class, 'p', 'WITH', 'p.user = u.id')
			->andWhere('p.createdAt >= :createdAt')
			->setParameter('createdAt', $from)
			->getQuery()
			->getResult();

		if ($payoutUsers) {
			return $this->em->createQueryBuilder()
				->select('u')
				->from(User::class, 'u')
				->leftJoin('u.transactions', 't')
				->andWhere('u IN (:users)')
				->setParameter('users', $payoutUsers)
				->groupBy('u.id')
				->andHaving('sum(t.userCommissionAmount) < 0')
				->getQuery()
				->getResult();
		}

		return null;
	}

	public function getCountOfDuplicatePayments(DateTime $from, DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(p.id) as c')
			->from(Payment::class, 'p')
			->andWhere('p.paidAt >= :from')
			->setParameter('from', $from)
			->andWhere('p.paidAt <= :to')
			->setParameter('to', $to)
			->addGroupBy('p.payout')
			->andHaving('count(p.id) > 1')
			->getQuery();

		return (int)$qb->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
	}

	public function getCountOfWebhookRequests(\DateTime $from, \DateTime $to, $name)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(w.id) as c')
			->from(WebhookRequest::class, 'w')
			->where('w.name = :name')->setParameter('name', $name)
			->andWhere('w.createdAt >= :createdFrom')->setParameter('createdFrom', $from)
			->andWhere('w.createdAt <= :createdTo')->setParameter('createdTo', $to);

		return (int)$qb->getQuery()->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
	}

	public function getCountOfImportedVisits(\DateTime $date)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(v.id) as c')
			->from(Visit::class, 'v')
			->andWhere('v.date >= :date')->setParameter('date', $date)
			->getQuery();

		return (int)$qb->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
	}

	public function getCountOfMandrillTags(\DateTime $createdAt)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(mt.id) as c')
			->from(MandrillTag::class, 'mt')
			->andWhere('mt.createdAt >= :createdAt')->setParameter('createdAt', $createdAt)
			->getQuery();

		return (int)$qb->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
	}

	public function getCountOfTransactionsByPartnerSystem($partnerSystem, ?\DateTime $createdAt = null, ?\DateTime $confirmedAt = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(t.id) as countOfTransactions')
			->from(PartnerSystem::class, 'p', 'p.id')
			->leftJoin(Transaction::class, 't', 'WITH', 't.partnerSystem = p')
			->andWhere('t.partnerSystem = :partnerSystem')->setParameter('partnerSystem', $partnerSystem)
			->addGroupBy('t.partnerSystem');

		if ($createdAt) {
			$qb->andWhere('t.createdAt >= :createdAt')->setParameter('createdAt', $createdAt);
		}

		if ($confirmedAt) {
			$qb->andWhere('t.confirmedAt >= :confirmedAt')->setParameter('confirmedAt', $confirmedAt);
		}

		return (int)$qb->getQuery()->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR) ?? 0;
	}

	public function getCountOfProcessedCollabimKeywords(\DateTime $dateFrom, \DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('count(k.id)')
			->from(Keyword::class, 'k')
			->andWhere('k.processedAt >= :dateFrom')->setParameter('dateFrom', $dateFrom)
			->andWhere('k.processedAt <= :dateTo')->setParameter('dateTo', $dateTo)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfProcessedCollabimKeywordPositions(\DateTime $dateFrom, \DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Position::class, 'p')
			->andWhere('p.createdAt >= :dateFrom')->setParameter('dateFrom', $dateFrom)
			->andWhere('p.createdAt <= :dateTo')->setParameter('dateTo', $dateTo)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfUpdatedOffersByCompetitor(\DateTime $dateFrom)
	{
		return $this->context->query('
            SELECT COUNT(o.id) as cnt, c.name FROM tipli_competitors_company c
            LEFT JOIN tipli_competitors_offer o on o.company_id = c.id AND o.updated_at > ?
            WHERE c.name NOT LIKE \'tipli%\'
            AND (c.track_rewards = 1 OR c.track_deals = 1 OR c.track_coupons = 1 OR c.track_leaflets = 1)
            GROUP BY c.id
        ', $dateFrom)->fetchAll();
	}

	public function getCountOfConfirmedPayouts(DateTime $confirmedFrom, DateTime $confirmedTo)
	{
		return $this->em->createQueryBuilder()
			->select('count(p.id)')
			->from(Payout::class, 'p')
			->join('p.user', 'u')
			->andWhere('p.scheduledAt IS NOT NULL')
			->andWhere('p.confirmedAt >= :confirmedFrom')
			->andWhere('p.confirmedAt <= :confirmedTo')
			->setParameter('confirmedFrom', $confirmedFrom)
			->setParameter('createdTo', $confirmedTo)
			->getQuery()
			->getResult();
	}

	public function getCountOfSentPushNotifications(DateTime $pushedFrom, DateTime $pushedTo)
	{
		return $this->em->createQueryBuilder()
			->select('count(n.id)')
			->from(Notification::class, 'n')
			->andWhere('n.pushedAt >= :pushedFrom')
			->andWhere('n.pushedAt <= :pushedTo')
			->setParameter('pushedFrom', $pushedFrom)
			->setParameter('pushedTo', $pushedTo)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfSentCampaignPushNotificationsByLocale(DateTime $pushedFrom, DateTime $pushedTo, string $locale)
	{
		$localization = $this->localizationFacade->findOneByLocale($locale);

		$users = array_column($this->userFacade->getUsers($localization)->select('u.id')->getQuery()->getResult(), 'id');

		$query = $this->em->createQueryBuilder()
			->select('COUNT(n.id) AS count, SUM(CASE WHEN n.mobileClickedAt IS NOT NULL THEN 1 ELSE 0 END) AS clickedCount')
			->from(Notification::class, 'n')
			->andWhere('n.notificationCampaign IS NOT NULL')
			->andWhere('n.pushedAt BETWEEN :pushedFrom AND :pushedTo')
			->andWhere('n.user IN (:users)')
			->setParameter('users', $users)
			->setParameter('pushedFrom', $pushedFrom)
			->setParameter('pushedTo', $pushedTo)
			->getQuery()
			->getScalarResult();

		return $query[0];
	}

	public function getCountOfTransactionsWithGreaterCreatedAt(\DateTime $dateFrom, \DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('count(t.id)')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.createdAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('t.createdAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('t.createdAt < t.registeredAt')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getTransactionsWithoutUser(\DateTime $dateFrom, \DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('t')
			->from(Transaction::class, 't')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.createdAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('t.createdAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('t.user IS NULL')
			->getQuery()
			->getResult();
	}

	public function getRegisteredTransactionsByTime(\DateTime $createdFrom, DateTime $createdTo, Localization $localization, int $minMinutesDiff, ?int $maxMinutesDiff = null): int
	{
		return $this->context->query('
            SELECT count(t.id) as c
            FROM tipli_transactions_transaction t
            WHERE t.currency = ?
			AND t.created_at >= ?
            AND t.created_at <= ?
            AND t.registered_at <= t.created_at
            AND t.type = ?
            AND TIMESTAMPDIFF(MINUTE, t.registered_at, t.created_at) > ?
            ' . ($maxMinutesDiff ? 'AND TIMESTAMPDIFF(MINUTE, t.registered_at, t.created_at) <= ' . $maxMinutesDiff : '') . '
        ', $localization->getCurrency(), $createdFrom, $createdTo, Transaction::TYPE_COMMISSION, $minMinutesDiff)->fetch()->c;
	}

	public function getCountOfTransactionsByRedirections(\DateTime $dateFrom, DateTime $dateTo)
	{
		return $this->context->query('
        SELECT r.shop_id, s.name, l.locale, count(r.id) as count_of_redirections,
        (
           SELECT count(id)
           FROM tipli_transactions_transaction t
           WHERE t.shop_id = r.shop_id
           AND t.created_at >= ? AND t.created_at <= ?
           AND t.type = \'commission\'
        ) AS count_of_transactions
        FROM tipli_shops_redirection r
        INNER JOIN tipli_shops_shop s on s.id = r.shop_id
        INNER JOIN tipli_localization_localization l on l.id = s.localization_id
        INNER JOIN tipli_shops_shop_data sd on sd.shop_id = s.id
        WHERE r.created_at >= ? AND r.created_at <= ?
        AND s.cashback_allowed = 1 AND (sd.paused_at > ? OR sd.paused_at IS NULL)
        GROUP BY r.shop_id
        ', $dateFrom, $dateTo, $dateFrom, $dateTo, new DateTime())->fetchAll();
	}

	public function getCountOfApprovedRefunds(DateTime $dateFrom, DateTime $dateTo, $byBot = false)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(r.id)')
			->from(Refund::class, 'r')
			->andWhere('r.resolvedAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('r.resolvedAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('r.state = :state')
			->setParameter('state', Refund::STATE_APPROVED);

		if ($byBot) {
			$qb->andWhere('r.resolvedByUser IS NULL');
		}

		return $qb->getQuery()
			->getSingleScalarResult();
	}

	public function getRefundedAmountsByCurrency(DateTime $dateFrom, DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('sum(t.bonusAmount) as sum, t.currency')
			->from(Refund::class, 'r')
			->leftJoin('r.refundTransaction', 't')
			->andWhere('r.resolvedAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('r.resolvedAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('r.state = :state')
			->setParameter('state', Refund::STATE_APPROVED)
			->addGroupBy('t.currency')
			->andWhere('t.currency IS NOT NULL')
			->getQuery()
			->getScalarResult();
	}

	public function getRefundsWithoutFreshdeskTicketId(DateTime $dateFrom, DateTime $dateTo)
	{
		return $this->em->createQueryBuilder()
			->select('r')
			->from(Refund::class, 'r')
			->andWhere('r.createdAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('r.createdAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('r.freshdeskTicket IS NULL')
			->getQuery()
			->getResult();
	}

	public function getCountOfCreatedRefundsByType(DateTime $dateFrom, DateTime $dateTo, string $type)
	{
		return $this->em->createQueryBuilder()
			->select('count(r.id)')
			->from(Refund::class, 'r')
			->andWhere('r.createdAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->andWhere('r.createdAt <= :dateTo')
			->setParameter('dateTo', $dateTo)
			->andWhere('r.type = :type')
			->setParameter('type', $type)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCountOfCommissionsAndRefundsByShop(DateTime $dateFrom, DateTime $dateTo)
	{
		return $this->context->query('
        SELECT shop_id,
           (SELECT count(id)
            FROM tipli_transactions_transaction AS t2
            WHERE t.shop_id = t2.shop_id AND t2.type="commission" AND t2.created_at > ? AND t2.created_at < ?) AS commissions,
           (SELECT count(id)
            FROM tipli_transactions_transaction AS t3
            WHERE t.shop_id = t3.shop_id AND t3.type="bonus_refund" AND t3.created_at > ? AND t3.created_at < ?) AS refunds
        FROM tipli_transactions_transaction t
        WHERE t.shop_id IS NOT NULL
        GROUP BY t.shop_id
        HAVING refunds > commissions AND refunds > 5
       ', $dateFrom, $dateTo, $dateFrom, $dateTo)->fetchAll();
	}

	public function getAverageShopTransactionRegistrationTime(\DateTime $from, \DateTime $to, Shop $shop): ?int
	{
		return $this->context->query('
            SELECT avg(TIMESTAMPDIFF(SECOND, t.registered_at, t.created_at)) as r
            FROM tipli_transactions_transaction t
            WHERE t.created_at >= ? AND t.created_at <= ?
            AND t.shop_id = ? AND t.type = \'commission\'
        ', $from, $to, $shop->getId())->fetch()->r;
	}

	public function getPercentileTransactionRegistrationTime(\DateTime $from, \DateTime $to, Localization $localization, float $percentile = 0.99): ?int
	{
		return $this->context->query(
			'
            SELECT a FROM (
			SELECT IFNULL(ABS(TIMESTAMPDIFF(SECOND, t.registered_at, t.created_at)), 0) as a
			FROM tipli_transactions_transaction t
			WHERE t.currency = ?
			AND t.created_at >= ?
			AND t.created_at <= ?
			AND t.type="commission"
			ORDER BY a) AS d
			LIMIT 1
			OFFSET ?
        ',
			$localization->getCurrency(),
			$from,
			$to,
			floor($this->getCountOfTransactions($from, $to, $localization) * $percentile)
		)->fetch()->a;
	}

	public function getPercentileTransactionConfirmationTime(\DateTime $from, \DateTime $to, Localization $localization, float $percentile = 0.99): ?int
	{
		return $this->context->query(
			'
            SELECT a AS a FROM (
			SELECT IFNULL(ABS(TIMESTAMPDIFF(SECOND, t.confirmed_at, t.created_at)), 0) as a
			FROM tipli_transactions_transaction t
			WHERE t.currency = ?
			AND t.confirmed_at >= ?
			AND t.confirmed_at <= ?
			AND t.type="commission"
			ORDER BY a) AS d
			LIMIT 1
			OFFSET ?
        ',
			$localization->getCurrency(),
			$from,
			$to,
			floor($this->getCountOfConfirmedCommissionTransactions($from, $to, $localization) * $percentile)
		)->fetch()->a;
	}

	public function getPercentilePayoutSolutionTime(\DateTime $from, \DateTime $to, Localization $localization, float $percentile = 0.99): ?int
	{
		return $this->context->query(
			'
			SELECT a FROM (
			SELECT IFNULL(ABS(TIMESTAMPDIFF(SECOND, p.created_at, p.confirmed_at)), 0) as a
			FROM tipli_payouts_payout p
			INNER JOIN tipli_account_user u ON (u.id=p.user_id)
			WHERE p.confirmed_at IS NOT NULL
			AND p.confirmed_at >= ?
			AND p.confirmed_at <= ?
			AND u.localization_id = ?
			ORDER BY a) AS d
			LIMIT 1
			OFFSET ?
        ',
			$from,
			$to,
			$localization->getId(),
			floor($this->getCountOfPayouts($from, $to, $localization, true) * $percentile)
		)->fetch()->a;
	}

	public function getAverageShopTransactionConfirmationTime(\DateTime $from, \DateTime $to, Shop $shop): ?int
	{
		return $this->context->query('
            SELECT avg(TIMESTAMPDIFF(SECOND, t.registered_at, t.confirmed_at)) as r
            FROM tipli_transactions_transaction t
            WHERE t.confirmed_at  >= ? AND t.confirmed_at  <= ?
            AND t.shop_id = ? AND t.type = \'commission\'
        ', $from, $to, $shop->getId())->fetch()->r;
	}

	public function getCountOfRefunds(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null, ?Shop $shop = null, ?string $type = null, ?string $state = null, ?PartnerSystem $partnerSystem = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('count(r.id)')
			->from(Refund::class, 'r');

		if ($state === null) {
			$qb
				->andWhere('r.createdAt >= :dateFrom')
				->andWhere('r.createdAt <= :dateTo')
				->setParameter('dateFrom', $dateFrom)
				->setParameter('dateTo', $dateTo);
		} elseif ($state === Refund::STATE_DECLINED || $state === Refund::STATE_APPROVED) {
			$qb
				->andWhere('r.resolvedAt >= :dateFrom')
				->andWhere('r.resolvedAt <= :dateTo')
				->andWhere('r.state = :state')
				->setParameter('dateFrom', $dateFrom)
				->setParameter('dateTo', $dateTo)
				->setParameter('state', $state);
		}

		if ($localization !== null) {
			$qb
				->andWhere('r.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($shop !== null) {
			$qb
				->andWhere('r.shop = :shop')
				->setParameter('shop', $shop);
		}

		if ($partnerSystem !== null) {
			$qb
				->innerJoin('r.shop', 's')
				->innerJoin('s.partnerSystem', 'ps')
				->andWhere('ps = :partnerSystem')
				->setParameter('partnerSystem', $partnerSystem);
		}

		if ($type !== null) {
			$qb
				->andWhere('r.type = :type')
				->setParameter('type', $type);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUniqueUsersInRefunds(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(DISTINCT u.id) AS c')
			->from(Refund::class, 'r')
			->innerJoin('r.user', 'u');

		$qb
			->andWhere('r.createdAt >= :dateFrom')
			->andWhere('r.createdAt <= :dateTo')
			->setParameter('dateFrom', $dateFrom)
			->setParameter('dateTo', $dateTo);

		if ($localization !== null) {
			$qb
				->andWhere('r.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUniqueUsersInPayouts(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(DISTINCT u.id) AS c')
			->from(Payout::class, 'p')
			->innerJoin('p.user', 'u');

		$qb
			->andWhere('p.createdAt >= :dateFrom')
			->andWhere('p.createdAt <= :dateTo')
			->setParameter('dateFrom', $dateFrom)
			->setParameter('dateTo', $dateTo);

		if ($localization !== null) {
			$qb
				->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getCountOfUniqueUsersInRedirections(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null, ?Shop $shop = null)
	{
		$qb = $this->em->createQueryBuilder()
			->select('COUNT(DISTINCT u.id) AS c')
			->from(Redirection::class, 'r')
			->innerJoin('r.user', 'u');

		$qb
			->andWhere('r.createdAt >= :dateFrom')
			->andWhere('r.createdAt <= :dateTo')
			->setParameter('dateFrom', $dateFrom)
			->setParameter('dateTo', $dateTo);

		if ($localization !== null) {
			$qb
				->andWhere('u.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($shop !== null) {
			$qb
				->andWhere('r.shop = :shop')
				->setParameter('shop', $localization);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getAverageRefundSolutionTime(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null): int
	{
		return $this->context->query('
			SELECT IFNULL(SUM(TIMESTAMPDIFF(SECOND,r.created_at, r.resolved_at))/COUNT(r.id), 0) AS c
			FROM tipli_refunds_refund r
			WHERE r.resolved_at IS NOT NULL
			AND r.resolved_at >= ?
			AND r.resolved_at <= ?
			AND r.localization_id = ?
			AND r.resolved_by_user_id IS NOT NULL
        ', $dateFrom, $dateTo, $localization->getId())->fetch()->c;
	}

	public function getAveragePayoutSolutionTime(\DateTime $dateFrom, \DateTime $dateTo, ?Localization $localization = null): int
	{
		return $this->context->query('
			SELECT IFNULL(SUM(TIMESTAMPDIFF(SECOND,p.created_at, p.confirmed_at))/COUNT(p.id), 0) AS c
			FROM tipli_payouts_payout p
			INNER JOIN tipli_localization_localization l
			WHERE p.confirmed_at IS NOT NULL
			AND p.confirmed_at >= ?
			AND p.confirmed_at <= ?
			AND l.id = ?
        ', $dateFrom, $dateTo, $localization->getId())->fetch()->c;
	}

	public function getCountOfUserLoginByPlatformAndMethod(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->from(UserLogin::class, 'l')
			->select('count(l.id) as countOfLogins, l.platform, l.method')
			->andWhere('l.createdAt >= :from')
			->andWhere('l.createdAt <= :to')
			->setParameter('from', $from)
			->setParameter('to', $to)
			->addGroupBy('l.platform')
			->addGroupBy('l.method');

		return $qb->getQuery()->getResult();
	}

	public function getCountOfUserLoginByLocalizationPlatformAndMethod(Localization $localization, \DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->from(UserLogin::class, 'l')
			->innerJoin('l.user', 'u')
			->select('count(l.id) as countOfLogins, l.platform, l.method')
			->andWhere('l.createdAt >= :from')
			->andWhere('l.createdAt <= :to')
			->andWhere('u.localization = :localization')
			->setParameter('localization', $localization)
			->setParameter('from', $from)
			->setParameter('to', $to)
			->addGroupBy('l.platform')
			->addGroupBy('l.method');

		return $qb->getQuery()->getResult();
	}

	public function getPartnerSystemProcessErrors(\DateTime $from, \DateTime $to)
	{
		$qb = $this->em->createQueryBuilder()
			->select('p.name, p.id, count(e.id) as countOfErrors')
			->from(ProcessError::class, 'e')
			->leftJoin('e.partnerSystem', 'p')
			->andWhere('e.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('e.createdAt <= :to')
			->setParameter('to', $to)
			->addOrderBy('count(e.id)', 'DESC')
			->addGroupBy('p.id');

		return $qb->getQuery()->getResult();
	}

	public function findShopWithCbAllowedToPartnerSystemWithoutCommissions()
	{
		return $this->em->createQueryBuilder()
			->select('s')
			->from(Shop::class, 's')
			->innerJoin('s.partnerSystem', 'ps')
			->andWhere('s.cashbackAllowed = true')
			->andWhere('s.publishedAt <= :published')
			->setParameter('published', new DateTime())
			->andWhere('ps.hasCommissions = false')
			->getQuery()
			->getResult();
	}

	public function findCountOfRefundsByPartnerSystem(\DateTime $from, \DateTime $to)
	{
		return $this->em->createQueryBuilder()
			->select('ps.id, ps.name, count(distinct(r.user_id) as countOfRefunds')
			->from(Refund::class, 'r')
			->innerJoin('r.shop', 's')
			->innerJoin('s.partnerSystem', 'ps')
			->andWhere('r.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('r.createdAt <= :to')
			->setParameter('to', $to)
			->addGroupBy('ps.id')
			->getQuery()
			->getArrayResult();
	}

	public function findShopsWithActiveCashbackAndRedirections(\DateTime $from, \DateTime $to)
	{
		return $this->em->createQueryBuilder()
			->select('s')
			->from(Shop::class, 's')
			->leftJoin('s.redirections', 'r')
			->andWhere('r.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('r.createdAt <= :to')
			->setParameter('to', $to)
			->having('count(r.id) >= 50')
			->addGroupBy('s.id')
			->getQuery()
			->getResult();
	}

	public function findPartnerSystemsWithCommissionsAndRedirections(\DateTime $from, \DateTime $to)
	{
		return $this->em->createQueryBuilder()
			->select('ps')
			->from(PartnerSystem::class, 'ps')
			->innerJoin('ps.shops', 's')
			->leftJoin('s.redirections', 'r')
			->andWhere('r.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('r.createdAt <= :to')
			->setParameter('to', $to)
			->having('count(r.id) >= 1')
			->addGroupBy('ps.id')
			->getQuery()
			->getResult();
	}

	public function findRefundsWaitingToProcess(\DateTime $from)
	{
		return $this->em->createQueryBuilder()
			->select('r')
			->from(Refund::class, 'r')
			->andWhere('r.createdAt <= :from')
			->setParameter('from', $from)
			->andWhere('r.state = :state')
			->setParameter('state', Refund::STATE_WAITING_FOR_PROCESS)
			->getQuery()
			->getResult();
	}

	public function findActiveUnpublishedShops()
	{
		return $this->em->createQueryBuilder()
			->select('s')
			->from(Shop::class, 's')
			->where('s.active = 1')
			->andWhere('s.publishedAt > :now')
			->setParameter('now', new DateTime())
			->getQuery()
			->getResult();
	}

	public function findInactivePublishedShops()
	{
		return $this->em->createQueryBuilder()
			->select('s')
			->from(Shop::class, 's')
			->where('s.active = 0')
			->andWhere('s.publishedAt <= :now')
			->setParameter('now', new DateTime())
			->getQuery()
			->getResult();
	}

	public function findShopsWithoutPageExtensions()
	{
		return $this->em->createQueryBuilder()
			->select('s')
			->from(Shop::class, 's')
			->andWhere('s.pageExtension IS NULL')
			->getQuery()
			->getResult();
	}

	public function findTransactionsWithoutTransactionData(\DateTime $from)
	{
		return $this->em->createQueryBuilder()
			->select('t')
			->from(Transaction::class, 't')
			->leftJoin('t.transactionData', 'td')
			->andWhere('td.id IS NULL')
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->getQuery()
			->getResult();
	}

	public function findShopsWithTurnover(DateTime $from, int $turnover)
	{
		return $this->context->query('
			SELECT s.id, s.name,
			(SELECT SUM(td.turnover) from tipli_transactions_transaction t INNER JOIN tipli_transactions_transaction_data td on td.transaction_id = t.id WHERE t.shop_id = s.id AND t.created_at >= ? AND t.type = \'commission\') as turnover,
			(SELECT COUNT(id) from tipli_transactions_transaction WHERE shop_id = s.id AND created_at >= ? AND type = \'commission\') as count_of_transactions,
			(SELECT COUNT(id) from tipli_transactions_transaction WHERE shop_id = s.id AND created_at >= ? AND type = \'commission\') as count_of_transactions_last_8_hours,
			(SELECT COUNT(id) from tipli_transactions_transaction WHERE shop_id = s.id AND created_at >= ? AND type = \'commission\') as count_of_transactions_last_2_days
			FROM tipli_shops_shop s
			GROUP BY s.id
			HAVING(turnover) > ?
		', $from, $from, $from->modify('- 8 hours'), $from->modify('- 2 days'), $turnover)->fetchAll();
	}

	public function findAverageTurnoverForLast2Hours()
	{
		return $this->context->query('
			SELECT SUM(td.turnover) / 36 as turnover FROM tipli_transactions_transaction t
			INNER JOIN tipli_transactions_transaction_data td ON td.transaction_id = t.id
			WHERE t.type = \'commission\'
			AND t.created_at >= ?
		', new DateTime('- 72 hours'))->fetchField();
	}

	public function findAverageCountOfUsersForLastHours(?Localization $localization = null)
	{
		$sql = '
			SELECT count(u.id) / 36 as cnt
			FROM tipli_account_user u
			WHERE created_at >= ?
		';

		$params = [new DateTime('-72 hours')];

		if ($localization !== null) {
			$sql .= ' AND u.localization_id = ?';
			$params[] = $localization->getId();
		}

		return $this->context->query($sql, ...$params)->fetchField();
	}

	public function findCountOfUsersForLast2Hours(?Localization $localization = null)
	{
		$sql = '
			SELECT count(u.id)
			FROM tipli_account_user u
			WHERE created_at >= ?
		';

		$params = [new DateTime('-2 hours')];

		if ($localization !== null) {
			$sql .= ' AND u.localization_id = ?';
			$params[] = $localization->getId();
		}

		return $this->context->query($sql, ...$params)->fetchField();
	}

	public function findPartnerSystemProcessWithAnomaly()
	{
		return $this->context->query('
			SELECT ps.id, ps.name, COUNT(psp.id) / 72 as avgPerHour,
			(SELECT count(id) * 1.2 from tipli_partner_systems_partner_system_process WHERE partner_system_id = ps.id AND created_at >= ?) as countLast4Hours
			FROM tipli_partner_systems_partner_system ps
			LEFT JOIN tipli_partner_systems_partner_system_process psp ON psp.partner_system_id = ps.id
			WHERE psp.created_at >= ?
			GROUP BY ps.id
			HAVING(avgPerHour * 4) > countLast4Hours
		', new DateTime('- 4 hours'), new DateTime('-72 hours'))->fetchAll();
	}

	public function findUnconsistentUsers()
	{
		return $this->context->query('
			SELECT u.id FROM tipli_account_user u
		    LEFT JOIN tipli_account_user_data ud ON ud.user_id = u.id
		    LEFT JOIN tipli_account_segment_data sd ON sd.user_id = u.id
		    LEFT JOIN tipli_account_user_security us ON us.user_id = u.id
			WHERE  (ud.id IS NULL OR sd.id IS NULL OR us.id IS NULL OR ac.id IS NULL)
		')->fetchAll();
	}

	public function getCountOfUserVisitsByUser(DateTime $fromDate): array
	{
		return $this->context->query('
			SELECT COUNT(id) as cnt, user_id
			FROM tipli_account_user_visit uv
			WHERE uv.created_at >= ?
			AND is_ajax = 0
			GROUP BY user_id
		', $fromDate)->fetchAll();
	}

	public function getCountOfUserVisits(DateTime $fromDate): ?int
	{
		return $this->context->query('
			SELECT COUNT(id) as cnt
			FROM tipli_account_user_visit uv
			WHERE uv.created_at >= ?
			AND is_ajax = 0
		', $fromDate)->fetch()->cnt;
	}

	public function findShopsWithRefunds(Localization $localization, DateTime $fromDate, DateTime $toDate): array
	{
		return $this->context->query(
			'
    SELECT
        s.id,
        s.localization_id,
        s.name,
        COUNT(r.id) AS countOfRefunds,
        (
            SELECT COUNT(id)
            FROM tipli_transactions_transaction
            WHERE type = "commission"
                AND shop_id = s.id
                AND created_at >= ?
                AND created_at <= ?
        ) AS countOfCommissions,
        (
            SELECT MAX(created_at)
            FROM tipli_transactions_transaction
            WHERE type = "commission"
                AND shop_id = s.id
        ) AS lastCommission
    FROM
        tipli_shops_shop s
    INNER JOIN
        tipli_refunds_refund r ON r.shop_id = s.id
    INNER JOIN
        tipli_shops_shop_data sd ON sd.shop_id = s.id
    WHERE
        s.active = 1
        AND sd.paused_at IS NULL
        AND s.localization_id = ?
        AND r.created_at >= ?
        AND r.created_at <= ?
        AND r.refund_transaction_id IS NOT NULL
    GROUP BY
        s.id
    HAVING
        COUNT(r.id) >= 5  -- Minimální počet reklamací
        AND (COUNT(r.id) + (
            SELECT COUNT(id)
            FROM tipli_transactions_transaction
            WHERE type = "commission"
                AND shop_id = s.id
                AND created_at >= ?
                AND created_at <= ?
        )) > 10  -- Minimální celkový počet transakcí
        AND (COUNT(r.id) / NULLIF(COUNT(r.id) + (
            SELECT COUNT(id)
            FROM tipli_transactions_transaction
            WHERE type = "commission"
                AND shop_id = s.id
                AND created_at >= ?
                AND created_at <= ?
        ), 0) * 100 > 30)  -- Poměr reklamací k transakcím
    ORDER BY
        s.localization_id;
',
			$fromDate,
			$toDate,
			$localization->getId(),
			$fromDate,
			$toDate,
			$fromDate,
			$toDate,
			$fromDate,
			$toDate
		)->fetchAll();
	}

	public function getCountOfScheduledSms(Localization $localization, DateTime $fromDate, DateTime $toDate)
	{
		return $this->context->query('
			SELECT COUNT(s.id) as cnt
			FROM tipli_messages_sms s
			INNER JOIN tipli_account_user u ON u.id = s.user_id
			WHERE s.scheduled_at >= ?
			AND s.scheduled_at <= ?
			AND u.localization_id = ?
		', $fromDate, $toDate, $localization->getId())->fetch()->cnt;
	}

	public function findUserLuckyShopDataDuplicity()
	{
		return $this->context->query('
			SELECT user_id, COUNT(*) AS count
			FROM tipli_account_user_lucky_shop_data
			GROUP BY user_id
			HAVING COUNT(*) > 1
			ORDER BY count DESC;
		')->fetchAll();
	}

	public function findUserLuckyShopChecksDuplicity(DateTime $fromDate, DateTime $toDate): array
	{
		return $this->context->query('
			SELECT user_id, count(*) as cnt
			FROM `tipli_lucky_shop_user_lucky_shop_checks`
			AND `created_at` >= ?
			AND `created_at` <= ?
			GROUP BY user_id
			HAVING count(*) > 1
		')->fetchAll();
	}
}
