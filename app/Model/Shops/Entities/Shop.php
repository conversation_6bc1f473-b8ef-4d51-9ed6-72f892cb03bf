<?php

namespace tipli\Model\Shops\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Articles\Entities\Article;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Reports\Entities\ReportCheck;
use tipli\Model\Rewards\Entities\MoneyRewardCampaign;
use tipli\Model\Rewards\Entities\ShareReward;
use tipli\Model\Rewards\Entities\ShareRewardCampaign;
use tipli\Model\Risk\Entities\RiskPolicy;
use tipli\Model\Seo\Entities\PageExtension;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Transactions\ShareCoefficientResolver;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Shops\Repositories\ShopRepository")
 * @ORM\Table(name="tipli_shops_shop", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="slug_unique", columns={"localization_id", "slug"})
 * }, indexes= {
 *      @ORM\Index(name="published_at_idx", columns={"published_at"}),
 *      @ORM\Index(name="active_idx", columns={"active"}),
 *      @ORM\Index(name="type_idx", columns={"type"})
 * }
 * )
 * @ORM\EntityListeners({"tipli\Model\Shops\Listeners\ShopListener"})
 */
class Shop
{
	public const SAZKA_ID = 5283;
	public const ALIEXPRESS_ID = [
		'cs' => 1,
		'sk' => 298,
		'pl' => 617,
		'ro' => 5732,
		'hu' => 6493,
		'bg' => 11642,
		'si' => 11643,
		'hr' => 11644,
	];

	public const BOOKING_ID = [
		2,
		299,
		616,
		5731,
		6501,
		11619,
		11620,
		11621,
	];

	public const TEMU_IDS = [
		11488,
		12067,
		12068,
		12069,
		12070,
		12071,
		12072,
		12073,
	];

	public const NON_PROFIT_SHOP_IDS = [
		972, 1277, 1802, 13451,
	];

	public const WITHOUT_DEALS_SHOP_IDS = [
		50, 5816,
	];

	public const LESS_DETAILED_SHOP_IDS = [
		50,
	];

	public const LUCKY_SHOP_IDS = [
		1 => [5056,321,35,186,2209,4309,12432,1719,2000,2255,3547,1898,58,66,155,522,188,4375,357,177,11598,4529,2056,457,168,189,3066,3758,8650,4627,6977,972,9113,356,38,3491,5085,10711,1648,4398,12576,102,396,11367,4609,11,2028,2223,2094,6295,11145,1975,8097,1761,2288,453,9077,5319,10919,125,11659,3239,348,3987,3078,13153,1693,838,349,11366,14206,10388,60,4126,12107,3954,306,77,1744,11197,903,77,1998,464,11197,6329,5275,11197,12067,1998,3832,6329,3832,364,2226,1998,1834,11197,12067,1998,6948,4472,6329,1744,364,312,77,1,5275,476,1,4121,8776,8037,1926,128],
		2 => [3316,12732,298,12865,1379,1341,13904,221,1340,4558,460,3315,3298,11599,1491,1509,269,12068,3258,361,11539,1493,1169,10231,229,3085,285,1503,3080,1147,3098,3285,3082,6908,398,13952,288,1531,11557,12060,1271,3312,10296,1074,10239,992,264,11556,827,1188,7762,259,1246,250,6671,9705,5288,13325,6117,1142,1253,12999,6296,8040,1348,987,904,9282,5253,4880,397,14339,1426,1084,4169,389,1224,4508,14509,277,1357,10369,10115,7013,1469,4882,5615,3288,9135,5300,4399,10446,395,1533,11905,253,11225,3848,11149,12674,4376,241,273,11303,1001,11026,1028,8686,245,359,468,278,267,14386,4466,329,9941,6647,324,12027],
		3 => [3115,617,11488,4914,757,976,973,3624,790,858,15102,3404,4931,3671,3433,11429,13077,678,8188,668,7615,8937,876,3372,792,13739,975,3732,3050,6129,4936,5674,683,3387,7367,3331,854,7917,6073,3109,3434,5026,6168,923,8148,9602,3440,12513,737,3421,3663,3426,3792,3347,3717,7543,811,10911,3813,7426,6924,10632,10573,3418,4174,4209,4395,6751,850,5984,3400,12720,4934,857,6165,3405,14948,5680,3976,871,4906,853,13486,691,10542,7948,10413,6009,730,12453,3427,6489,13721,4136,887,4176,8941,14088,3394,4913],
		4 => [12069,8159,5732,7137,5973,5816,5929,8470,6290,5961,9646,8772,6291,9264,7100,8079,9022,5942,10185,8052,10191,6249,6247,5970,6284,7101,5746,5967,9428,8160,14738,6286,13516,6335,6663,8256,5944,5777,9808,6228,9670,5968,5802,5956,9434,8771,11211,10537,11533,8071,8228,6362,6289,11388,10183,8850,9843,9640,14309,12952,11334,6756,6281,5794,10055,10038,4800,13083,13481,9644,9377,9899,5868,5834,6334,9433,7307,6754,12283,9191,9419,9253,9354,9064,6364,9880,5754,11530,6460,5823,13142,6403,7249,11085,5738,5791,9710,5846,6422,13594],
		6 => [12070,7021,8354,6493,7254,8069,6496,7255,7746,6568,9156,7817,8298,6556,7966,14368,11572,7785,8119,12436,6588,14906,6510,11194,9636,6909,7625,8922,11552,8299,7257,10348,8747,12419,6610,6579,13451,8067,7992,7308,9469,8600,7768,7973,6598,7483,14167,8732,7268,7290,6584,14685,13389,11596,7883,7848,9305,13348,9363,7284,10242,6526,6505,13020,7832,6506,12490,6959,6617,8361,12792,14544,7644,12964,8468,7810,9087,8333,8271,8754,11551,7753,11083,8334,14734,6595,14885,7943,6523,6508,7346,13096,8235,8251,7074,7652,8471,12942,6950,7303],
		8 => [12071,11856,11677,11665,11669,11644,11731,12305,11861,12215,12158,12614,12717,11931,11869,11727,11668,15064,12517,13277,11741,11680,11737,12421,11782,15149,14207,11715,12281,12003,11786,11857,12425,11810,11724,13437,11720,15042,13654,11771,13034,11656,13295,13117,11711,11709,11730,14621,13497,12553,15005,14499,12849,11867,13664,12172,11862,12593,12925,13745,11840,12941,13161,12834,14745,11684,13027,11670,12451,11818,11734,11864,13648,14482,13488,14586,12354,11710,12615,14950,11693,11690,11875,11860,11744,11738,11872,11767,11876,14947,11653,12748,11785,11863,13491,11764,11721,12006,11748,13762],
	];

	public const PROFILE_BUSINESS = 'business';
	public const PROFILE_LEAFLET = 'leaflet';
	public const PROFILE_BUSINESS_LEAFLET = 'business_leaflet';

	public const MONETIZATION_NONE = 'none';
	public const MONETIZATION_WITH_AFFILIATE = 'affiliate';
	public const MONETIZATION_WITH_CASHBACK = 'cashback';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Shops\Entities\ShopData", mappedBy="shop", cascade={"persist"})
	 * @var ShopData
	 */
	private $shopData;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\Shops\Entities\ShopSettings", mappedBy="shop", cascade={"persist"})
	 */
	private $shopSettings;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\Reports\Entities\ReportCheck", mappedBy="shop", cascade={"persist"})
	 */
	private $reportCheck;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\Shops\Entities\ShopProductData", mappedBy="shop")
	 */
	private $shopProductData;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\Shops\Entities\ShopProductFeed", mappedBy="shop")
	 */
	private $shopProductFeed;

	/**
	 * @ORM\OneToOne(targetEntity="tipli\Model\Seo\Entities\PageExtension", inversedBy="shop")
	 * @ORM\JoinColumn(name="page_extension_id", referencedColumnName="id")
	 * @var PageExtension|null
	 */
	private $pageExtension;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 * @var Localization
	 */
	private $localization;

	/**
	 * @ORM\Column(type="string")
	 * @var string
	 */
	private $name;

	/**
	 * @ORM\Column(type="string")
	 * @var string
	 */
	private $slug;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EXTRA_LAZY")
	 * @ORM\JoinColumn(name="logo_id", referencedColumnName="id", nullable=true)
	 * @var Image|null
	 */
	private $logo;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EXTRA_LAZY")
	 * @ORM\JoinColumn(name="svg_logo_id", referencedColumnName="id", nullable=true)
	 * @var Image|null
	 */
	private ?Image $svgLogo = null;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EXTRA_LAZY")
	 * @ORM\JoinColumn(name="cover_id", referencedColumnName="id", nullable=true)
	 * @var Image|null
	 */
	private $cover;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image", fetch="EXTRA_LAZY")
	 * @ORM\JoinColumn(name="mobile_cover_id", referencedColumnName="id", nullable=true)
	 * @var Image|null
	 */
	private $mobileCover;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image")
	 * @ORM\JoinColumn(name="screenshot_id", referencedColumnName="id", nullable=true)
	 * @var Image|null
	 */
	private $screenshot;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string
	 */
	private $domain;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string
	 */
	private $conditions;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ShopTag", mappedBy="shop", cascade={"persist", "remove"}, orphanRemoval=true)
	 * @var ShopTag[]|ArrayCollection
	 */
	private $shopTags;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ShopLeafletTag", mappedBy="shop", cascade={"persist", "remove"}, orphanRemoval=true)
	 * @var ShopLeafletTag[]|ArrayCollection
	 */
	private $shopLeafletTags;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Leaflets\Entities\Leaflet", mappedBy="shop")
	 * @var Leaflet[]|ArrayCollection
	 */
	private $leaflets;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\Offer", mappedBy="shop")
	 * @ORM\OrderBy({"priority" = "DESC", "maximalReward" = "DESC"})
	 * @var Offer[]|ArrayCollection
	 */
	private $offers;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\PartnerSystem", mappedBy="shop")
	 * @var Offer[]|ArrayCollection
	 */
	private $partnerSystems;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Articles\Entities\Article", mappedBy="shops")
	 * @var Article|ArrayCollection
	 */
	private $articles;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Account\Entities\User", mappedBy="favouriteShops")
	 * @var User[]|ArrayCollection
	 */
	private $favouriteByUsers;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ShopReport", mappedBy="shop")
	 */
	private $reports;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\RelatedShop", mappedBy="relatedShop")
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var RelatedShop[]|ArrayCollection
	 */
	private $relatedShops;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\RelatedShop", mappedBy="parentShop")
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var RelatedShop[]|ArrayCollection
	 */
	private $parentShops;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ForeignShop", mappedBy="foreignParentShop")
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var ForeignShop[]|ArrayCollection
	 */
	private $foreignShops;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ForeignShop", mappedBy="foreignShop")
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var ForeignShop[]|ArrayCollection
	 */
	private $foreignParentShops;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Rewards\Entities\MoneyRewardCampaign", mappedBy="shops")
	 * @var MoneyRewardCampaign[]|ArrayCollection
	 */
	private $moneyRewardCampaigns;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Rewards\Entities\ShareRewardCampaign", mappedBy="shops")
	 * @var ShareRewardCampaign[]|null
	 */
	private $shareRewardCampaigns;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Rewards\Entities\ShareReward", mappedBy="shops")
	 * @var ShareReward[]|null
	 */
	private $shareRewards;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\DescriptionBlock", mappedBy="shop", cascade={"persist"})
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var DescriptionBlock[]|ArrayCollection
	 */
	private $descriptionBlocks;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Deals\Entities\Deal", mappedBy="shop", cascade={"persist"})
	 * @ORM\OrderBy({"priority" = "DESC"})
	 * @var Deal[]|ArrayCollection
	 */
	private $deals;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $cashbackAllowed = false;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\PartnerSystems\Entities\PartnerSystem", inversedBy="shops")
	 * @ORM\JoinColumn(name="partner_system_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\PartnerSystems\Entities\PartnerSystem|null
	 */
	private $partnerSystem;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $partnerSystemRedirectUrl;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private ?string $defaultDeepUrl = null;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $deepUrlAllowed = true;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $countOfTransactions = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $countOfRedirections = 0;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float|null
	 */
	private $averageRegistrationPeriod;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float|null
	 */
	private $averageConfirmationPeriod;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $trackingReliability = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $priorityLeaflets = 0;

	/**
	 * @ORM\Column(type="string", length=16)
	 * @var string
	 */
	private $type = self::PROFILE_BUSINESS;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $visible = true;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $boostCoupons = false;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $description;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string
	 */
	private $shortDescription;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $publishedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $updatedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $updateIndexAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime
	 */
	private $sitemapUpdatedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	/**
	 * @ORM\OneToOne(targetEntity="tipli\Model\Shops\Entities\Contact")
	 * @ORM\JoinColumn(name="contact_id", referencedColumnName="id")
	 * @var \tipli\Model\Shops\Entities\Contact|null
	 */
	private $contact;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Transactions\Entities\Transaction", mappedBy="shop")
	 * @var \tipli\Model\Transactions\Entities\Transaction[]|ArrayCollection
	 */
	private $transactions;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Reviews\Entities\Review", mappedBy="shop")
	 * @var \tipli\Model\Reviews\Entities\Review[]|ArrayCollection
	 */
	private $reviews;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\Redirection", mappedBy="shop")
	 * @var \tipli\Model\Shops\Entities\Redirection[]|null
	 */
	private $redirections;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Products\Entities\Category", mappedBy="shops")
	 */
	private $productCategories;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Products\Entities\Product", mappedBy="shop")
	 */
	private $products;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ShopNote", mappedBy="shop", cascade={"persist"})
	 * @ORM\OrderBy({"createdAt" = "DESC"})
	 * @var \tipli\Model\Shops\Entities\ShopNote[]|ArrayCollection
	 */
	private $shopNotes;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\ShopQuestion", mappedBy="shop", cascade={"persist"})
	 * @var \tipli\Model\Shops\Entities\ShopQuestion[]|ArrayCollection
	 */
	private $shopQuestions;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Risk\Entities\RiskPolicy", mappedBy="shop")
	 */
	private Collection $riskPolicy;

	/** @var  ShareCoefficientResolver */
	private $shareCoefficientResolver;

	public function __construct(Localization $localization, $name, $slug, $priority, \DateTime $publishedAt, $active, $author = null, $visible = true)
	{
		$this->localization = $localization;
		$this->name = $name;
		$this->slug = $slug;
		$this->priority = $priority;
		$this->publishedAt = $publishedAt;
		$this->active = $active;
		$this->visible = $visible;

		$this->shopData = new ShopData($this);

		$this->shopTags = new ArrayCollection();
		$this->shopLeafletTags = new ArrayCollection();
		$this->offers = new ArrayCollection();
		$this->partnerSystems = new ArrayCollection();
		$this->articles = new ArrayCollection();
		$this->favouriteByUsers = new ArrayCollection();
		$this->relatedShops = new ArrayCollection();
		$this->parentShops = new ArrayCollection();
		$this->foreignShops = new ArrayCollection();
		$this->foreignParentShops = new ArrayCollection();
		$this->descriptionBlocks = new ArrayCollection();
		$this->deals = new ArrayCollection();
		$this->shopProductData = new ArrayCollection();
		$this->shopNotes = new ArrayCollection();
		$this->shopQuestions = new ArrayCollection();
		$this->moneyRewardCampaigns = new ArrayCollection();
		$this->riskPolicy = new ArrayCollection();

		$this->shopSettings = new ArrayCollection();
		$this->shopSettings->add(new ShopSettings($this));

		$this->reportCheck = new ArrayCollection();
		$this->reportCheck->add(new ReportCheck($this));

//		$this->publishedAt = new DateTime();
		$this->updatedAt = new DateTime();
		$this->updateIndexAt = new DateTime();
		$this->sitemapUpdatedAt = new DateTime();
		$this->createdAt = new DateTime();

		$this->shopData->setAuthor($author);
	}

	public function __toString()
	{
		return $this->slug . '-' . $this->id;
	}

	public function update()
	{
		$this->updatedAt = new DateTime();
	}

	public function updateIndex()
	{
		$this->updateIndexAt = new DateTime();
	}

	public function indexUpdated()
	{
		$this->updateIndexAt = null;
	}

	public function isPausedByShopChecker()
	{
		return $this->shopData->isPausedByShopChecker();
	}

	public function pauseByShopChecker()
	{
		$this->setPausedAt(new DateTime());
		$this->setPauseReason(ShopData::PAUSE_REASON_PAUSED_BY_SHOP_CHECKER);
		$this->shopData->setPausedByShopChecker(true);
	}

	public function unPauseByShopChecker()
	{
		$this->setPausedAt(null);
		$this->setPauseReason(null);
		$this->shopData->setPausedByShopChecker(false);
	}

	/**
	 * @return ShopData
	 */
	public function getShopData()
	{
		return $this->shopData;
	}

	/**
	 * @param ShopData $shopData
	 */
	public function setShopData(ShopData $shopData)
	{
		$this->shopData = $shopData;
	}

	/**
	 * @param Tag[] $tags
	 */
	public function setTags(array $tags)
	{
		$idsOfCurrentTags = $this->getIdsOfTags();
		$idsOfTags = array_map(static function ($tag) {
			return $tag->getId();
		}, $tags);

		foreach ($this->getShopTags() as $shopTag) {
			if (!in_array($shopTag->getTag()->getId(), $idsOfTags)) {
				$this->removeShopTag($shopTag);
			}
		}

		foreach ($tags as $tag) {
			if (!in_array($tag->getId(), $idsOfCurrentTags)) {
				$this->addTag($tag, 0);
			}
		}
	}

	public function setLeafletTags(array $tags)
	{
		$idsOfCurrentTags = $this->getIdsOfLeafletTags();
		$idsOfTags = array_map(static function ($tag) {
			return $tag->getId();
		}, $tags);

		foreach ($this->getShopLeafletTags() as $shopLeafletTag) {
			if (!in_array($shopLeafletTag->getTag()->getId(), $idsOfTags)) {
				$this->removeShopLeafletTag($shopLeafletTag);
			}
		}

		foreach ($tags as $tag) {
			if (!in_array($tag->getId(), $idsOfCurrentTags)) {
				$this->addLeafletTag($tag, 0);
			}
		}
	}

	/**
	 * @return Tag[]
	 */
	public function getTags()
	{
		$tags = [];
		foreach ($this->shopTags as $shopTag) {
			if (!$shopTag->getTag()->isRemoved()) {
				$tags[] = $shopTag->getTag();
			}
		}

		return $tags;
	}

	public function getVisibleTagsIdNamePairs()
	{
		$tags = [];
		foreach ($this->shopTags as $shopTag) {
			if ($shopTag->getTag()->isVisible()) {
				$tags[$shopTag->getTag()->getId()] = $shopTag->getTag()->getName();
			}
		}

		return $tags;
	}

	/**
	 * @return null|\tipli\Model\PartnerSystems\Entities\PartnerSystem
	 */
	public function getPartnerSystem()
	{
		return $this->partnerSystem;
	}

	public function getAverageConfirmationPeriod(): ?float
	{
		return $this->averageConfirmationPeriod;
	}

	/**
	 * @return array
	 */
	public function getIdsOfTags()
	{
		$ids = [];
		foreach ($this->shopTags as $shopTag) {
			$ids[] = $shopTag->getTag()->getId();
		}

		return $ids;
	}

	public function getIdsOfLeafletTags()
	{
		$ids = [];
		foreach ($this->shopLeafletTags as $shopLeafletTag) {
			$ids[] = $shopLeafletTag->getTag()->getId();
		}

		return $ids;
	}

	public function hasTag(Tag $tag): bool
	{
		return in_array($tag->getId(), $this->getIdsOfTags());
	}

	/**
	 * @param ShopTag[] $shopTags
	 */
	public function setShopTags(array $shopTags)
	{
		//        $this->clearShopTags();
//        $this->addShopTags($shopTags);
	}

	public function addTag(Tag $tag, $priority)
	{
		$this->addShopTag(
			new ShopTag($this, $tag, $priority)
		);
	}

	public function addLeafletTag(Tag $tag, $priority)
	{
		$this->addShopLeafletTag(
			new ShopLeafletTag($this, $tag, $priority)
		);
	}

	/**
	 * @param ShopLeafletTag $shopLeafletTag
	 */
	public function addShopLeafletTag(ShopLeafletTag $shopLeafletTag)
	{
		if (!$this->shopLeafletTags->contains($shopLeafletTag)) {
			$this->shopLeafletTags->add($shopLeafletTag);
		}
	}

	/**
	 * @param ShopLeafletTag $shopLeafletTag
	 */
	public function removeShopLeafletTag(ShopLeafletTag $shopLeafletTag)
	{
		if ($this->shopLeafletTags->contains($shopLeafletTag)) {
			$this->shopLeafletTags->removeElement($shopLeafletTag);
		}
	}

	/**
	 * @param ShopTag $shopTag
	 */
	public function addShopTag(ShopTag $shopTag)
	{
		if (!$this->shopTags->contains($shopTag)) {
			$this->shopTags->add($shopTag);
		}
	}

	/**
	 * @param ShopTag $shopTag
	 */
	public function removeShopTag(ShopTag $shopTag)
	{
		if ($this->shopTags->contains($shopTag)) {
			$this->shopTags->removeElement($shopTag);
		}
	}

	public function clearShopTags()
	{
		foreach ($this->shopTags as $shopTag) {
			$this->removeShopTag($shopTag);
			$shopTag->getTag()->removeShopTag($shopTag);
		}
	}

	/**
	 * @param ShopTag[] $shopTags
	 */
	public function addShopTags(array $shopTags)
	{
		foreach ($shopTags as $shopTag) {
			$this->addShopTag($shopTag);
			$shopTag->getTag()->addShopTag($shopTag);
		}
	}

	/**
	 * @return ArrayCollection|ShopLeafletTag[]
	 */
	public function getShopLeafletTags()
	{
		return $this->shopLeafletTags;
	}

	/**
	 * @return ShopLeafletTag|null
	 */
	public function getFirstShopLeafletTag()
	{
		if (!$this->shopLeafletTags->isEmpty()) {
			return $this->shopLeafletTags->first();
		} else {
			return null;
		}
	}


	/**
	 * @return ArrayCollection|Offer[]
	 *
	 * public function getOffers()
	 * {
	 * return $this->offers;
	 * }
	 */

	/**
	 * @return ArrayCollection|RelatedShop[]
	 */
	public function getRelatedShops()
	{
		return $this->relatedShops;
	}

	/**
	 * @return string
	 */
	public function getSlug()
	{
		return $this->slug;
	}

	/**
	 * @return null|Image
	 */
	public function getLogo()
	{
		return $this->logo;
	}

	public function getCurrentLogo(): ?Image
	{
//		if (isset($_COOKIE['3541d5b8ce93838816db6c6d4ad5ff39']) && $_COOKIE['3541d5b8ce93838816db6c6d4ad5ff39'] === '3541d5b8ce93838816db6c6d4ad5ff39') {
//			return $this->getSvgLogo() ?? $this->getLogo();
//		} else {
//			return $this->getLogo();
//		}
		return $this->getSvgLogo() ?? $this->getLogo();
	}

	public function getSvgLogo(): ?Image
	{
		return $this->svgLogo;
	}

	public function getDomain()
	{
		return $this->domain;
	}

	public function getAlternativeNames(): ?string
	{
		return $this->shopData->getAlternativeNames();
	}

	public function setAlternativeNames(?string $alternativeNames): void
	{
		$this->shopData->setAlternativeNames($alternativeNames);
	}

	public function getAlternativeNamesArray(): array
	{
		if (strstr($this->getAlternativeNames(), "\n")) {
			return array_unique(explode("\n", $this->getAlternativeNames()));
		}
		return [$this->getAlternativeNames()];
	}

	/**
	 * @return string|null
	 */
	public function getShortDescription()
	{
		/** @var DescriptionBlock $block */
		foreach ($this->getDescriptionBlocks('short_description') as $block) {
			return $block->getDescription();
		}

		return null;
	}

	/**
	 * @return DescriptionBlock|null
	 */
	public function getLongDescription(): ?DescriptionBlock
	{
		/** @var DescriptionBlock $block */
		foreach ($this->getDescriptionBlocks('long_description') as $block) {
			return $block;
		}

		return null;
	}

	/**
	 * @return Localization
	 */
	public function getLocalization()
	{
		return $this->localization;
	}

	/**
	 * @return string|null
	 */
	public function getDescription()
	{
		/** @var DescriptionBlock $block */
		foreach ($this->getDescriptionBlocks('description') as $block) {
			return $block->getDescription();
		}

		return null;
	}

	/**
	 * @return string
	 */
	public function getConditions()
	{
		return $this->conditions;
	}

	/**
	 * @param ShareCoefficientResolver $shareCoefficientResolver
	 */
	public function setShareCoefficientResolver($shareCoefficientResolver)
	{
		$this->shareCoefficientResolver = $shareCoefficientResolver;
	}

	public function getShareCoefficient(User $user = null)
	{
		return $this->shareCoefficientResolver->resolveShareCoefficient($user, $this, null, true);
	}


//	public function getTopOffer()
//	{
//		return $this->offers->first();
//	}
//
//
//	public function getLowestOffer()
//	{
//		$lowestOffer = $this->getTopOffer();
//
//		foreach ($this->getOffers() as $offer) {
//			if ($offer->getMinimalReward() < $lowestOffer->getMinimalReward()) {
//				$lowestOffer = $offer;
//			}
//		}
//
//		return $lowestOffer;
//	}

	public function hasAbsoluteOffer(): bool
	{
		/** @var Offer $offer */
		foreach ($this->offers as $offer) {
			if ($offer->isAbsolute()) {
				return true;
			}
		}

		return false;
	}

	public function isCurrencyAllowed(string $currency): bool
	{
		// Kinguin
		if ($this->getId() === 1955) {
			return in_array($currency, [Currency::CZK, Currency::EUR]);
		}

		return $this->getLocalization()->getCurrency() === $currency;
	}

	/**
	 * @return bool
	 */
	public function isCashbackAllowed()
	{
		return $this->cashbackAllowed;
	}

	/**
	 * @return bool
	 */
	public function getCashbackAllowed()
	{
		return $this->cashbackAllowed;
	}

	/**
	 * @return bool
	 */
	public function isPublished()
	{
		return $this->publishedAt <= new DateTime() && $this->active;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	/**
	 * @return null|Contact
	 */
	public function getContact()
	{
		return $this->contact;
	}

	/**
	 * @param Contact $contact
	 */
	public function setContact($contact)
	{
		$this->contact = $contact;
	}

	/**
	 * @return string
	 */
	public function getNote()
	{
		return $this->shopData->getNote();
	}

	public function setNote($note)
	{
		$this->shopData->setNote($note);
	}

	public function isCashbackWithCouponAllowed()
	{
		return $this->shopData->isCashbackWithCouponAllowed();
	}

	public function setCashbackWithCouponAllowed($cashbackWithCouponAllowed)
	{
		$this->shopData->setCashbackWithCouponAllowed($cashbackWithCouponAllowed);
	}

	public function isAddonAllowed(): bool
	{
		return !$this->getShopSettings()->isAddonFeedDisabled();
	}

	public function setAddonAllowed($allowedAddon)
	{
		$allowedAddon === true ? $this->getShopSettings()->enableAddonFeed() : $this->getShopSettings()->disableAddonFeed();
	}

	public function isInAppRedirectionAllowed(): bool
	{
		return !$this->getShopSettings()->isInAppRedirectionDisabled();
	}

	/**
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return String
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * @param string $name
	 */
	public function setName(string $name)
	{
		$this->name = $name;
	}

	/**
	 * @return int
	 */
	public function getPriority()
	{
		return $this->priority;
	}

	/**
	 * @param int $priority
	 */
	public function setPriority(int $priority)
	{
		$this->priority = $priority;
	}

	/**
	 * @return int
	 */
	public function getPriorityLeaflets()
	{
		return $this->priorityLeaflets;
	}

	/**
	 * @param int $priorityLeaflets
	 */
	public function setPriorityLeaflets(int $priorityLeaflets)
	{
		$this->priorityLeaflets = $priorityLeaflets;
	}

	/**
	 * @return DateTime
	 */
	public function getPublishedAt()
	{
		return $this->publishedAt;
	}

	/**
	 * @param DateTime $publishedAt
	 */
	public function setPublishedAt(DateTime $publishedAt)
	{
		$this->publishedAt = $publishedAt;
	}

	/**
	 * @deprecated
	 */
	public function getActive()
	{
		return $this->active;
	}

	/**
	 * @param bool $active
	 */
	public function setActive(bool $active)
	{
		$this->active = $active;
	}

	/**
	 * @return string
	 */
	public function getPartnerSystemRedirectUrl()
	{
		return $this->partnerSystemRedirectUrl;
	}

	/**
	 * @return bool
	 */
	public function isDeepUrlAllowed(): bool
	{
		return $this->deepUrlAllowed;
	}

	/**
	 * @return bool
	 */
	public function getDeepUrlAllowed(): bool
	{
		return $this->deepUrlAllowed;
	}

	/**
	 * @return int
	 */
	public function getCountOfTransactions()
	{
		return $this->countOfTransactions;
	}

	public function getIdsOfRelatedShops()
	{
		$ids = [];
		foreach ($this->getRelatedShops() as $relatedShop) {
			$ids[] = $relatedShop->getRelatedShop()->getId();
		}

		return $ids;
	}

	public function hasRelatedShop(Shop $shop): bool
	{
		return in_array($shop->getId(), $this->getIdsOfRelatedShops());
	}

	public function getSeoVerifiedAt()
	{
		return $this->shopData->getSeoVerifiedAt();
	}

	public function verifySeo()
	{
		$this->shopData->setSeoVerifiedAt(new \DateTime());
	}

	/** @return ShopTag|null */
	public function getShopTag(Tag $tag)
	{
		$criteria = Criteria::create()->where(Criteria::expr()->eq('tag', $tag))->setMaxResults(1);

		$shopTag = $this->getShopTags()->matching($criteria)->toArray();

		return $shopTag[0] ?? null;
	}

	/** @return ShopLeafletTag|null */
	public function getShopLeafletTag(Tag $tag)
	{
		$criteria = Criteria::create()->where(Criteria::expr()->eq('tag', $tag))->setMaxResults(1);

		$shopTag = $this->getShopLeafletTags()->matching($criteria)->toArray();

		return $shopTag[0] ?? null;
	}

	/**
	 * @return ArrayCollection|ShopTag[]
	 */
	public function getShopTags()
	{
		return $this->shopTags;
	}

	public function setInfoMessage($infoMessage)
	{
		$this->shopData->setInfoMessage($infoMessage);
	}

	public function getInfoMessage()
	{
		return $this->shopData->getInfoMessage();
	}

	public function setAddonMessage($addonMessage): void
	{
		$this->shopData->setAddonMessage($addonMessage);
	}

	public function getAddonMessage(): ?string
	{
		return $this->shopData->getAddonMessage();
	}

	public function setPausedAt(\DateTime $pausedAt = null)
	{
		$this->shopData->setPausedAt($pausedAt);
	}

	public function getPausedAt()
	{
		return $this->shopData->getPausedAt();
	}

	public function isPaused()
	{
		return ($this->shopData->getPausedAt() && $this->shopData->getPausedAt() <= new \DateTime());
	}

	public function isAffiliateActive()
	{
		return $this->isActive() && !$this->isPaused() && $this->partnerSystem && $this->partnerSystemRedirectUrl;
	}

	public function isCashbackActive(): bool
	{
		return $this->cashbackAllowed && $this->isAffiliateActive();
	}

	/**
	 * @return DateTime
	 */
	public function getUpdatedAt()
	{
		return $this->updatedAt;
	}

	public function setDomain($domain)
	{
		$this->domain = $domain;
	}

	public function setConditions($conditions)
	{
		$this->conditions = $conditions;
	}

	/**
	 * @param null|\tipli\Model\PartnerSystems\Entities\PartnerSystem $partnerSystem
	 */
	public function setPartnerSystem($partnerSystem)
	{
		$this->partnerSystem = $partnerSystem;
	}

	/**
	 * @param mixed $partnerSystemRedirectUrl
	 */
	public function setPartnerSystemRedirectUrl($partnerSystemRedirectUrl)
	{
		$this->partnerSystemRedirectUrl = $partnerSystemRedirectUrl;
	}

	public function setDeepUrlAllowed(bool $deepUrlAllowed)
	{
		$this->deepUrlAllowed = $deepUrlAllowed;
	}

	/**
	 * @param bool $cashbackAllowed
	 */
	public function setCashbackAllowed(bool $cashbackAllowed)
	{
		$this->cashbackAllowed = $cashbackAllowed;
	}

	public function setSlug($slug)
	{
		$this->slug = $slug;
	}

	public function setLogo(?Image $logo)
	{
		$this->logo = $logo;
	}

	public function setSvgLogo(?Image $svgLogo): void
	{
		$this->svgLogo = $svgLogo;
	}

	public function setScreenshot(Image $screenshot)
	{
		$this->screenshot = $screenshot;
	}

	/**
	 * @return null|PageExtension
	 */
	public function getPageExtension()
	{
		return $this->pageExtension;
	}

	/**
	 * @param null|PageExtension $pageExtension
	 */
	public function setPageExtension($pageExtension)
	{
		$this->pageExtension = $pageExtension;
	}

	/**
	 * @return DateTime
	 */
	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

	public function getLastLeafletCreatedAt()
	{
		return $this->shopData->getLastLeafletCreatedAt();
	}

	public function getCountOfInternalLinks()
	{
		return $this->shopData->getCountOfInternalLinks();
	}

	/**
	 * @param int $countOfInternalLinks
	 */
	public function setCountOfInternalLinks(int $countOfInternalLinks)
	{
		$this->shopData->setCountOfInternalLinks($countOfInternalLinks);
	}

	public function getCountOfExternalLinks()
	{
		return $this->shopData->getCountOfExternalLinks();
	}

	/**
	 * @param int $countOfExternalLinks
	 */
	public function setCountOfExternalLinks(int $countOfExternalLinks)
	{
		$this->shopData->setCountOfExternalLinks($countOfExternalLinks);
	}

	public function getCountOfSimilarShops()
	{
		return $this->shopData->getCountOfSimilarShops();
	}

	/**
	 * @param int $countOfSimilarShops
	 */
	public function setCountOfSimilarShops(int $countOfSimilarShops)
	{
		$this->shopData->setCountOfSimilarShops($countOfSimilarShops);
	}

	public function getIsInternationalLinked()
	{
		return $this->shopData->getIsInternationalLinked();
	}

	/**
	 * @param bool $isInternationalLinked
	 */
	public function setIsInternationalLinked(bool $isInternationalLinked)
	{
		$this->shopData->setIsInternationalLinked($isInternationalLinked);
	}

	public function getPauseReason()
	{
		return $this->shopData->getPauseReason();
	}

	/**
	 * @param string $pauseReason
	 */
	public function setPauseReason(string $pauseReason = null)
	{
		$this->shopData->setPauseReason($pauseReason);
	}

	public function getAuthor()
	{
		return $this->shopData->getAuthor();
	}

	/**
	 * @param User $user
	 */
	public function setAuthor(User $user)
	{
		$this->shopData->setAuthor($user);
	}

	public function getSearchKeywords()
	{
		return $this->shopData->getSearchKeywords();
	}

	public function getShopNameInCyrillic(): string
	{
		return ContentFilter::convertLatinToCyrillic($this->getName());
	}

	public function setSearchKeywords($searchKeywords)
	{
		$this->shopData->setSearchKeywords($searchKeywords);
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function setType(string $type)
	{
		$this->type = $type;
	}

	/**
	 * @return bool
	 */
	public function isVisible(): bool
	{
		return $this->visible;
	}

	/**
	 * @param bool $visible
	 */
	public function setVisible(bool $visible)
	{
		$this->visible = $visible;
	}

	public function toDocument()
	{
		$pageExtension = $this->getPageExtension();

		$data = [
			'name' => $this->getName(),
			'slug' => $this->getSlug(),
			'conditions' => $this->getConditions(),
			'domain' => $this->getDomain(),
			'partnerSystemRedirectUrl' => $this->getPartnerSystemRedirectUrl(),
			'deepUrlAllowed' => $this->getDeepUrlAllowed(),
			'cashbackAllowed' => $this->getCashbackAllowed(),
			'cashbackWithCouponAllowed' => $this->getShopData()->isCashbackWithCouponAllowed(),
			'isPaused' => $this->isPaused(),
			'isActive' => $this->isActive(),
			'addonAllowed' => $this->isAddonAllowed(),
			'pauseReason' => $this->getPauseReason(),
			'publishedAt' => $this->getPublishedAt()->format('d.m.Y H:i:s'),
			'author' => $this->getAuthor() ? ($this->getAuthor()->getEmail() . ' (' . $this->getAuthor()->getId() . ')') : '',
		];

		if ($pageExtension) {
			$data['metaTitle'] = $pageExtension->getMetaTitle();
			$data['metaKeywords'] = $pageExtension->getMetaKeywords();
			$data['getMetaDescription'] = $pageExtension->getMetaDescription();
		}

		if ($partnerSystem = $this->getPartnerSystem()) {
			$data['partnerSystem'] = $partnerSystem->getName();
		}

		$partnerSystems = '';
		/** @var PartnerSystem $partnerSystem */
		foreach ($this->getPartnerSystems() as $shopPartnerSystem) {
			$partnerSystems .= $shopPartnerSystem->getPartnerSystem()->getName() . ' (' . $shopPartnerSystem->getPartnerSystemKey() . ')' . "\n";
		}

		$data['partnerSystems'] = rtrim($partnerSystems, ', ');

		$descriptionBlocks = '';
		/** @var DescriptionBlock $descriptionBlock */
		foreach ($this->descriptionBlocks as $descriptionBlock) {
			$descriptionBlocks .= $descriptionBlock->getTitle() . ' ' . $descriptionBlock->getDescription();
		}

		$offers = '';
		/** @var Offer $offer */
		foreach ($this->offers as $offer) {
			$offers .= sprintf(
				'
id: %s,
název: %s,
minimalReward: %s,
minimalRewardShare: %s,
maximalReward: %s,
maximalRewardShare: %s,
validSince: %s,
validTill: %s,
createdAt: %s
		    ',
				$offer->getId(),
				$offer->getName(),
				$offer->getMinimalReward(),
				$offer->getMinimalRewardShare(),
				$offer->getMaximalReward(),
				$offer->getMaximalRewardShare(),
				$offer->getValidSince() ? $offer->getValidSince()->format('d.m.Y') : 'není časově omezeno',
				$offer->getValidTill() ? $offer->getValidTill()->format('d.m.Y') : 'není časově omezeno',
				$offer->getCreatedAt()->format('d.m.Y H:i')
			);
		}

		$data['descriptionBlocks'] = $descriptionBlocks;
		$data['offers'] = $offers;

		$tags = '';
		/** @var Tag $tag */
		foreach ($this->getTags() as $tag) {
			$tags .= $tag->getName() . ', ';
		}

		$data['tags'] = rtrim($tags, ',');

		$document = [];
		foreach ($data as $key => $value) {
			$document[] = $key . ":\n" . $value;
		}

		$document = implode("\n\n***\n\n", $document);

		return $document;
	}

	/**
	 * @return mixed
	 */
	public function getCountOfDeals()
	{
		return $this->shopData->getCountOfDeals();
	}

	public function getCountOfLeaflets(): int
	{
		return $this->shopData->getCountOfLeaflets();
	}

	/**
	 * @return mixed
	 */
	public function getCountOfCouponDeals()
	{
		return $this->shopData->getCountOfCouponDeals();
	}

	public function hasSomeDeals(): bool
	{
		return $this->getCountOfDeals() > 0 || $this->getCountOfCouponDeals() > 0;
	}

	/**
	 * @return mixed
	 */
	public function getTotalCommissionAmountInLast30Days()
	{
		return $this->shopData->getTotalCommissionAmountInLast30Days();
	}

	/**
	 * @return null|Image
	 */
	public function getCover()
	{
		return $this->cover;
	}

	/**
	 * @param null|Image $cover
	 */
	public function setCover(?Image $cover)
	{
		$this->cover = $cover;
	}

	/**
	 * @return null|Image
	 */
	public function getMobileCover()
	{
		return $this->mobileCover;
	}

	public function setMobileCover(?Image $mobileCover)
	{
		$this->mobileCover = $mobileCover;
	}

	/**
	 * @return string|null
	 */
	public function getMobileMessageBeforeRedirect(): ?string
	{
		return $this->shopData->getMobileMessageBeforeRedirect();
	}

	/**
	 * @param string|null $mobileMessageBeforeRedirect
	 */
	public function setMobileMessageBeforeRedirect(?string $mobileMessageBeforeRedirect)
	{
		$this->shopData->setMobileMessageBeforeRedirect($mobileMessageBeforeRedirect);
	}

	/**
	 * @return DateTime|null
	 */
	public function getSitemapUpdatedAt(): ?DateTime
	{
		return $this->sitemapUpdatedAt;
	}

	public function updateSitemap()
	{
		$this->sitemapUpdatedAt = new DateTime();
	}

	/*
	   * @return string|null
	   */
	public function getDealsTitle(): ?string
	{
		return $this->shopData->getDealsTitle();
	}

	/**
	 * @param string|null $dealsTitle
	 */
	public function setDealsTitle(?string $dealsTitle): void
	{
		$this->shopData->setDealsTitle($dealsTitle);
	}

	/**
	 * @return string|null
	 */
	public function getCouponsDescription(): ?string
	{
		return $this->shopData->getCouponsDescription();
	}

	/**
	 * @param string|null $couponsDescription
	 */
	public function setCouponsDescription(?string $couponsDescription): void
	{
		$this->shopData->setCouponsDescription($couponsDescription);
	}

	/**
	 * @return string|null
	 */
	public function getShippingDescription(): ?string
	{
		return $this->shopData->getShippingDescription();
	}

	/**
	 * @param string|null $shippingDescription
	 */
	public function setShippingDescription(?string $shippingDescription): void
	{
		$this->shopData->setShippingDescription($shippingDescription);
	}

	public function getPartnerSystems()
	{
		return $this->partnerSystems;
	}

	public function getShopPartnerSystem(\tipli\Model\PartnerSystems\Entities\PartnerSystem $partnerSystem)
	{
		foreach ($this->getPartnerSystems() as $shopPartnerSystem) {
			if ($shopPartnerSystem->getPartnerSystem() === $partnerSystem) {
				return $shopPartnerSystem;
			}
		}
	}

	public function getDescriptionBlocks(?string $type = null)
	{
		if (!$type) {
			return $this->descriptionBlocks;
		}

		$blocks = [];
		/** @var DescriptionBlock $block */
		foreach ($this->descriptionBlocks as $block) {
			if (
				$block->getType() === $type &&
				$block->isArchived() === false &&
				($block->getType() === DescriptionBlock::TYPE_DEAL || $block->getDescription())
			) {
				$blocks[] = $block;
			}
		}

		return $blocks;
	}

	public function getDescriptionBlock(string $type): ?DescriptionBlock
	{
		/** @var DescriptionBlock $block */
		foreach ($this->descriptionBlocks as $block) {
			if ($block->getType() === $type && $block->isArchived() === false) {
				return $block;
			}
		}

		return null;
	}

	/**
	 * @return float|null
	 */
	public function getConfirmationRate(): ?float
	{
		return $this->getShopData()->getConfirmationRate();
	}

	public function scheduleShopChecker(DateTime $scheduleAt): void
	{
		$this->shopData->processByShopChecker($scheduleAt);
	}

	public function setLeafletCover(?Image $leafletCover)
	{
		$this->shopData->setLeafletCover($leafletCover);
	}

	/**
	 * @return Image|null
	 */
	public function getLeafletCover(): ?Image
	{
		return $this->shopData->getLeafletCover();
	}

	public function getTransactionCheckerScheduledAt(): ?\DateTime
	{
		return $this->shopData->getTransactionCheckerScheduledAt();
	}

	public function scheduleTransactionChecker(?\DateTime $scheduleAt = null)
	{
		$this->shopData->scheduleTransactionChecker($scheduleAt);
	}

	/**
	 * @return string|null
	 */
	public function getLeafletCoverAlt(): ?string
	{
		return $this->shopData->getLeafletCoverAlt();
	}

	/**
	 * @param string|null $leafletCoverAlt
	 */
	public function setLeafletCoverAlt(?string $leafletCoverAlt)
	{
		$this->shopData->setLeafletCoverAlt($leafletCoverAlt);
	}

	public function getAverageTransactionRegistrationPeriod()
	{
		return $this->shopData->getAverageTransactionRegistrationPeriod();
	}

	/**
	 * @return int|null
	 */
	public function getCountOfRequiredDeals(): ?int
	{
		return $this->shopData->getCountOfRequiredDeals();
	}

	/**
	 * @param int|null $minimalCountOfRequiredDeals
	 */
	public function setCountOfRequiredDeals(?int $minimalCountOfRequiredDeals): void
	{
		$this->shopData->setCountOfRequiredDeals($minimalCountOfRequiredDeals);
	}

	/**
	 * @return ShopProductData
	 */
	public function getShopProductData()
	{
		return $this->shopProductData->first();
	}

	public function getShopProductFeed(): PersistentCollection
	{
		return $this->shopProductFeed;
	}

	/**
	 * @param bool $onlyLeafletShop
	 */
	public function setOnlyLeafletShop(bool $onlyLeafletShop)
	{
		$this->shopData->setOnlyLeafletShop($onlyLeafletShop);
	}

	/**
	 * @return bool
	 */
	public function isOnlyLeafletShop()
	{
		return $this->shopData->isOnlyLeafletShop();
	}

	/**
	 * @return int|null
	 */
	public function getAverageTransactionRegistrationTime()
	{
		return $this->shopData->getAverageTransactionRegistrationTime();
	}

	/**
	 * @param mixed $description
	 */
	public function setDescription($description)
	{
		$this->description = $description;
	}

	public function setShortDescription($shortDescription)
	{
		$this->shortDescription = $shortDescription;
	}

	public function isAliexpress(): bool
	{
		return $this->getSlug() === 'aliexpress';
	}

	public function isTesco(): bool
	{
		return $this->getSlug() === 'tesco';
	}

	public function isTemu(): bool
	{
		return $this->getSlug() === 'temu';
	}

	public function isShein()
	{
		return $this->getSlug() === 'shein';
	}

	public function isBooking(): bool
	{
		return in_array($this->getId(), self::BOOKING_ID);
	}

	public function isAllegro(): bool
	{
		return $this->getSlug() === 'allegro';
	}

	/**
	 * @param bool $couponConfirmationRequired
	 * @return void
	 */
	public function setCouponConfirmationRequired(bool $couponConfirmationRequired)
	{
		$this->shopData->setCouponConfirmationRequired($couponConfirmationRequired);
	}

	/**
	 * @return bool
	 */
	public function isCouponConfirmationRequired(): bool
	{
		return $this->shopData->isCouponConfirmationRequired();
	}

	public function getParentShops()
	{
		return $this->parentShops;
	}

	public function getShopSettings(): ShopSettings
	{
		return $this->shopSettings->first();
	}

	public function getWarningMessage()
	{
		return $this->shopData->getWarningMessage();
	}

	public function setWarningMessage(?string $message)
	{
		$this->shopData->setWarningMessage($message);
	}

	public function getMobileWarningMessage(): ?string
	{
		return $this->shopData->getMobileWarningMessage();
	}

	public function setMobileWarningMessage(?string $message)
	{
		$this->shopData->setMobileWarningMessage($message);
	}

	public function getWarningMessageHash(): ?string
	{
		return $this->shopData->getWarningMessageHash();
	}

	public function getMobileWarningMessageHash(): ?string
	{
		return $this->shopData->getMobileWarningMessageHash();
	}

	public function getReports()
	{
		return $this->reports;
	}

	public function review(User $user)
	{
		$this->shopData->review($user);
	}

	public function getReviewedAt()
	{
		return $this->shopData->getReviewedAt();
	}

	public function getReportCheck(): ReportCheck
	{
		return $this->reportCheck->first();
	}

	public function isRequestSentInAffiliate(): bool
	{
		return $this->shopData->isRequestSentInAffiliate();
	}

	public function sendRequestToAffiliate(): void
	{
		$this->shopData->sendRequestToAffiliate();
	}

	public function clearRequestToAffiliate(): void
	{
		$this->shopData->clearRequestToAffiliate();
	}

	public function getRewardReviewAt(): ?\DateTime
	{
		return $this->shopData->getRewardReviewAt();
	}

	public function setRewardReviewAt(?\DateTime $rewardReviewAt): void
	{
		$this->shopData->setRewardReviewAt($rewardReviewAt);
	}

	public function getCouponsReviewAt(): ?\DateTime
	{
		return $this->shopData->getCouponsReviewAt();
	}

	public function setCouponsReviewAt(?\DateTime $couponsReviewAt): void
	{
		$this->shopData->setCouponsReviewAt($couponsReviewAt);
	}

	public function getDealsReviewAt(): ?\DateTime
	{
		return $this->shopData->getDealsReviewAt();
	}

	public function setDealsReviewAt(?\DateTime $dealsReviewAt): void
	{
		$this->shopData->setDealsReviewAt($dealsReviewAt);
	}

	public function isMarkedWithoutCashback(): bool
	{
		return $this->shopData->isMarkedWithoutCashback();
	}

	public function markWithoutCashback()
	{
		$this->shopData->markWithoutCashback();
	}

	public function unmarkWithoutCashback()
	{
		$this->shopData->unmarkWithoutCashback();
	}

	public function isBoostCoupons(): bool
	{
		return $this->boostCoupons;
	}

	public function setBoostCoupons(bool $boostCoupons): void
	{
		$this->boostCoupons = $boostCoupons;
	}

	public function getCompetitorReviewAtByMetric(string $metric): ?DateTime
	{
		switch ($metric) {
			case 'reward':
				return $this->getRewardReviewAt();
			case 'countOfDeals':
				return $this->getDealsReviewAt();
			case 'countOfCoupons':
				return $this->getCouponsReviewAt();
		}

		throw new InvalidArgumentException('unknown metric');
	}

	public function getRecentDealExpireAt(): ?DateTime
	{
		return $this->shopData->getRecentDealExpireAt();
	}

	public function getRecentCouponExpireAt(): ?DateTime
	{
		return $this->shopData->getRecentCouponExpireAt();
	}

	public function isNonProfitShop(): bool
	{
		return in_array($this->getId(), self::NON_PROFIT_SHOP_IDS, true);
	}

	/**
	 * @return ArrayCollection|ShopNote[]
	 */
	public function getShopNotes()
	{
		return $this->shopNotes;
	}

	/**
	 * @return ArrayCollection|ShopQuestion[]
	 */
	public function getShopQuestions()
	{
		return $this->shopQuestions;
	}

	/**
	 * @return ArrayCollection|ForeignShop[]
	 */
	public function getForeignShops()
	{
		return $this->foreignShops;
	}

	/**
	 * @param ArrayCollection|ForeignShop[] $foreignShops
	 */
	public function setForeignShops($foreignShops): void
	{
		$this->foreignShops = $foreignShops;
	}

	/**
	 * @return ArrayCollection|ForeignShop[]
	 */
	public function getForeignParentShops()
	{
		return $this->foreignParentShops;
	}

	/**
	 * @param ArrayCollection|ForeignShop[] $foreignParentShops
	 */
	public function setForeignParentShops($foreignParentShops): void
	{
		$this->foreignParentShops = $foreignParentShops;
	}

	public function isShowDeals(): bool
	{
		return $this->shopData->isShowDeals();
	}

	public function setShowDeals(bool $showDeals): void
	{
		$this->shopData->setShowDeals($showDeals);
	}

	public function isShowLeaflets(): bool
	{
		return $this->shopData->isShowLeaflets();
	}

	public function setShowLeaflets(bool $showLeaflets): void
	{
		$this->shopData->setShowLeaflets($showLeaflets);
	}

	public function isShowProducts(): bool
	{
		return $this->shopData->isShowProducts();
	}

	public function setShowProducts(bool $showProducts): void
	{
		$this->shopData->setShowProducts($showProducts);
	}

	public function showedQuestionsAfterDeals(): bool
	{
		return $this->shopData->showedQuestionsAfterDeals();
	}

	public function setShowQuestionsAfterDeals(bool $showQuestionAfterDeals): void
	{
		$this->shopData->setShowQuestionsAfterDeals($showQuestionAfterDeals);
	}

	public function getFirstDescriptionAt(): ?\DateTime
	{
		return $this->shopData->getFirstDescriptionAt();
	}

	public function setFirstDescriptionAt(?\DateTime $firstDescriptionAt): void
	{
		$this->shopData->setFirstDescriptionAt($firstDescriptionAt);
	}

	public function getTransactionsConfirmationNote(): ?string
	{
		return $this->shopData->getTransactionsConfirmationNote();
	}

	public function setTransactionsConfirmationNote(?string $transactionsConfirmationNote): void
	{
		$this->shopData->setTransactionsConfirmationNote($transactionsConfirmationNote);
	}

	public function isGamble(): bool
	{
		foreach ($this->getTags() as $tag) {
			if ($tag->isGamble()) {
				return true;
			}
		}

		return false;
	}

	public function hasDisabledDeals(): bool
	{
		return $this->shopData->isShowDeals() === false || in_array($this->getId(), self::WITHOUT_DEALS_SHOP_IDS);
	}

	public function isLessDetailed(): bool
	{
		return in_array($this->getId(), self::LESS_DETAILED_SHOP_IDS);
	}

	public function getMonetization(): string
	{
		if ($this->getPartnerSystem() === null) {
			return self::MONETIZATION_NONE;
		} else {
			return $this->isCashbackAllowed() ? self::MONETIZATION_WITH_CASHBACK : self::MONETIZATION_WITH_AFFILIATE;
		}
	}

	public function getAverageCashback(): ?float
	{
		return $this->shopData->getAverageCashback();
	}

	public function getAverageCommissionAmount(): ?float
	{
		return $this->shopData->getAverageCommissionAmount();
	}

	public function getRiskPolicy(): ?RiskPolicy
	{
		if ($this->riskPolicy->isEmpty()) {
			return null;
		}

		return $this->riskPolicy->first();
	}

	public function getRefundHoursLimit(): int
	{
		return in_array($this->getId(), self::TEMU_IDS) ? 72 : 48;
	}

	public function isHidePromoAfterFirstTransaction(): bool
	{
		return $this->getShopSettings()->isHidePromoAfterFirstTransaction();
	}

	public function setHidePromoAfterFirstTransaction(bool $hidePromoAfterFirstTransaction)
	{
		$this->getShopSettings()->setHidePromoAfterFirstTransaction($hidePromoAfterFirstTransaction);
	}

	public function getRewardLabel(): ?string
	{
		return $this->shopData->getRewardLabel();
	}

	public function getDefaultDeepUrl(): ?string
	{
		return $this->defaultDeepUrl;
	}

	public function setDefaultDeepUrl(?string $defaultDeepUrl): void
	{
		$this->defaultDeepUrl = $defaultDeepUrl;
	}
}
