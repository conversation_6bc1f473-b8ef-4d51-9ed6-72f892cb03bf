<?php

namespace tipli\Model\Sitemap;

use Nette\Localization\Translator;
use Nette\Application\LinkGenerator;
use Nette\SmartObject;
use Nette\Utils\Strings;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use tipli\Model\Articles\ArticleFacade;
use tipli\Model\Articles\Entities\Article;
use tipli\Model\Configuration;
use tipli\Model\Deals\DealFacade;
use tipli\Model\LandingPages\Entities\LandingPage;
use tipli\Model\LandingPages\LandingPageFacade;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Sitemap\Events\SitemapFeedGeneratedEvent;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;

class NewSitemapGenerator
{
	use SmartObject;

	private const FREQUENCY_WEEKLY = 'weekly';
	private const FREQUENCY_DAILY = 'daily';

	/** @var array  */
	public $onGenerateFeed = [];

	/** @var LinkGenerator */
	private $linkGenerator;

	/** @var Translator */
	private $translator;

	/** @var ArticleFacade */
	private $articleFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var TagFacade */
	private $tagFacade;

	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var DealFacade */
	private $dealFacade;

	/** @var EventDispatcherInterface */
	private $eventDispatcher;

	/** @var LandingPageFacade */
	private $landingPageFacade;

	/** @var Configuration */
	private $configuration;

	public function __construct(LinkGenerator $linkGenerator, Translator $translator, ArticleFacade $articleFacade, ShopFacade $shopFacade, TagFacade $tagFacade, LeafletFacade $leafletFacade, DealFacade $dealFacade, EventDispatcherInterface $eventDispatcher, LandingPageFacade $landingPageFacade, Configuration $configuration)
	{
		$this->linkGenerator = $linkGenerator;
		$this->translator = $translator;
		$this->articleFacade = $articleFacade;
		$this->shopFacade = $shopFacade;
		$this->tagFacade = $tagFacade;
		$this->leafletFacade = $leafletFacade;
		$this->dealFacade = $dealFacade;
		$this->eventDispatcher = $eventDispatcher;
		$this->landingPageFacade = $landingPageFacade;
		$this->configuration = $configuration;
	}

	public function generateStaticFeed(Localization $localization)
	{
		$feed = [];

		$defaultLastMod = new \DateTime();
		$defaultLastMod->setDate($defaultLastMod->format('Y'), 1, 1);

		$feed[] = $this->generateItem('front.homepage.default.title', 'NewFront:Homepage:default', null, self::FREQUENCY_DAILY, $defaultLastMod);
//		$feed[] = $this->generateItem('front.sitemap.web.title', 'NewFront:SitemapData:web', null, self::FREQUENCY_DAILY, $defaultLastMod);

		$feed[] = $this->generateItem('front.static.contact.title', 'NewFront:Static:contact', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		$feed[] = $this->generateItem('front.static.conditions.title', 'NewFront:Static:conditions', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
//		$feed[] = $this->generateItem('front.static.forMedia.title', 'NewFront:Static:forMedia', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
//        $feed[] = $this->generateItem('front.static.payout.title', 'NewFront:Static:payout');
		if ($this->configuration->isThereAddon($localization)) {
			$feed[] = $this->generateItem('front.static.addon.title', 'NewFront:Static:addon', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		}
//        $feed[] = $this->generateItem('front.static.faq.title', 'NewFront:Static:faq');
//        $feed[] = $this->generateItem('front.static.helpdesk.title', 'NewFront:Static:helpdesk', null, self::FREQUENCY_WEEKLY);
		$feed[] = $this->generateItem('front.static.howItWorks.title', 'NewFront:Static:howItWorks', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
//        $feed[] = $this->generateItem('front.static.cashbackRules.title', 'NewFront:Static:cashbackRules', null, self::FREQUENCY_WEEKLY);
//		$feed[] = $this->generateItem('front.static.guarantee.title', 'NewFront:Static:guarantee', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
//		$feed[] = $this->generateItem('front.static.workWithUs.title', 'NewFront:Static:workWithUs', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		$feed[] = $this->generateItem('front.static.faq.title', 'NewFront:Static:faq', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		$feed[] = $this->generateItem('front.static.cookies.title', 'NewFront:Static:cookies', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		$feed[] = $this->generateItem('front.static.jobs.title', 'NewFront:Jobs:Job:jobs', null, self::FREQUENCY_WEEKLY, $defaultLastMod);

		if ($this->configuration->isThereMobileApp($localization)) {
			$feed[] = $this->generateItem('front.static.phoneApp.title', 'NewFront:Static:phoneApp', null, self::FREQUENCY_WEEKLY, $defaultLastMod);
		}

//		$feed[] = $this->generateItem('front.shops.sale.allSales.title', 'NewFront:Shops:Sale:sales');
//		$feed = array_merge($feed, $this->generateItems($saleTags, 'NewFront:Shops:Sale:sales'));
//		$feed = array_merge($feed, $this->generateItems($shops, 'NewFront:Shops:Sale:sales'));
//		$feed = array_merge($feed, $this->generateItems($sales, 'NewFront:Shops:Sale:sale'));

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateArticlesFeed(Localization $localization)
	{
		$feed = [];

		$articleTags = $this->tagFacade->getVisibleTags($localization, Tag::TYPE_ARTICLE)->getQuery()->getResult();
		$articles = $this->articleFacade->findPublishedArticles($localization);

		$feed[] = $this->generateItem('front.articles.article.allArticles.title', 'NewFront:Articles:Articles:default', null, self::FREQUENCY_DAILY);
		$feed = array_merge($feed, $this->generateItems($articleTags, 'NewFront:Articles:Articles:default', self::FREQUENCY_DAILY));
		$feed = array_merge($feed, $this->generateItems($articles, 'NewFront:Articles:Article:default', self::FREQUENCY_WEEKLY));

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateTopOffersFeed(Localization $localization)
	{
		$feed = [];

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateLeafletsFeed(Localization $localization)
	{
		$feed = [];

		$shopsQuery = $this->shopFacade->createShopsQuery($localization)
			->onlyPublished(false)
			->onlyWithLeafletTag();

		$shops = $this->shopFacade->fetch($shopsQuery);

//		$tags = $this->tagFacade->getVisibleTags($localization, Tag::TYPE_LEAFLET)->getQuery()->getResult();

		$leafletsQuery = $this->leafletFacade->createLeafletsQuery($localization)
			->onlyVisible()
			->sortByTopShops()
			->onlyValid()
			->onlyConfirmed()
			->onlyWithShop();

		$leaflets = $this->leafletFacade->fetch($leafletsQuery);

		$feed[] = $this->generateItem('front.leaflet.list.defaultTitle', 'NewFront:Leaflets:Leaflet:default', null, self::FREQUENCY_DAILY);

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			if ($this->leafletFacade->isShopIndexable($shop) === false) {
				continue;
			}

			$feed[] = $this->generateItem($shop->getName(), 'NewFront:Leaflets:Leaflet:leaflets', ['shop' => $shop], self::FREQUENCY_DAILY, $shop->getLastLeafletCreatedAt() ?? new \DateTime('2019-01-01'));
		}
//
//		foreach ($tags as $tag) {
//			$feed[] = $this->generateItem($tag->getName(), 'NewFront:Leaflets:Leaflet:leaflets', ['tag' => $tag], self::FREQUENCY_DAILY, $tag->getUpdatedAt());
//		}

		foreach ($leaflets as $leaflet) {
			$shop = $leaflet->getShop();

			if ($this->leafletFacade->isShopIndexable($shop) === false) {
				continue;
			}

			$feed[] = $this->generateItem($leaflet->getTitle(), 'NewFront:Leaflets:Leaflet:leaflet', ['leaflet' => $leaflet], self::FREQUENCY_WEEKLY, $leaflet->getConfirmedAt());
		}

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateDealsFeed(Localization $localization)
	{
		$feed = [];

		$feed[] = $this->generateItem('front.shops.sale.shopSales.title', 'NewFront:Deals:Deals:default', null, self::FREQUENCY_DAILY);

		$saleTags = $this->tagFacade->getVisibleTags($localization, Tag::TYPE_SALE)->getQuery()->getResult();

		$feed = array_merge($feed, $this->generateItems($saleTags, 'NewFront:Deals:Deals:default', self::FREQUENCY_DAILY));

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateShopsFeed(Localization $localization)
	{
		$feed = [];

		$shopsQuery = $this->shopFacade->createShopsQuery($localization)
			->onlyPublished()
			->notOnlyLeaflet()
			->onlyVisible()
		;

		$shops = $this->shopFacade->fetch($shopsQuery)->toArray();
		$shopTags = $this->tagFacade->getVisibleTags($localization, Tag::TYPE_SHOP)->getQuery()->getResult();

		$feed[] = $this->generateItem('', 'NewFront:Shops:Shops:default', null, self::FREQUENCY_DAILY);
		$feed = array_merge($feed, $this->generateItems($shops, 'NewFront:Shops:Shop:default', self::FREQUENCY_WEEKLY));
		$feed = array_merge($feed, $this->generateItems($shopTags, 'NewFront:Shops:Shops:default', self::FREQUENCY_DAILY));

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateLandingPagesFeed(Localization $localization)
	{

		$landingPages = $this->landingPageFacade->getLandingPages()
			->andWhere('lp.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('lp.validTill >= :now')
			->setParameter('now', new \DateTime())
			->getQuery()
			->getResult()
		;

		$feed = $this->generateItems($landingPages, 'NewFront:LandingPages:LandingPage:landingPage', self::FREQUENCY_DAILY);

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateOtherFeed(Localization $localization)
	{
		$feed = [];

		$feed = array_merge($feed, $this->generateStaticFeed($localization));

		# $feed[] = $this->generateItem('front.topOffer.title', 'NewFront:Static:topOffers', null, self::FREQUENCY_WEEKLY);

		$this->eventDispatcher->dispatch(new SitemapFeedGeneratedEvent($feed));

		return $feed;
	}

	public function generateWebFeed(Localization $localization)
	{
		$feed = [];

		$feed = array_merge($feed, $this->generateShopsFeed($localization));

		if ($this->configuration->isThereLeaflets($localization)) {
			$feed = array_merge($feed, $this->generateLeafletsFeed($localization));
		}

		$feed = array_merge($feed, $this->generateDealsFeed($localization));
		$feed = array_merge($feed, $this->generateArticlesFeed($localization));

		return array_merge($feed, $this->generateOtherFeed($localization));
	}

	private function generateItems($items, $destination, $frequency = null)
	{
		$feed = [];
		foreach ($items as $item) {
			if ($item instanceof Article) {
				$feed[] = $this->generateItem($item->getName(), $destination, ['article' => $item, 'page' => null], $frequency);
			} elseif ($item instanceof LandingPage && $destination === 'NewFront:LandingPages:LandingPage') {
				$feed[] = $this->generateItem($item->getName(), $destination, ['landingPage' => $item], $frequency, $item->getTag()->getUpdatedAt());
			} elseif ($item instanceof Tag) {
				$feed[] = $this->generateItem($item->getName(), $destination, $item, $frequency, $item->getUpdatedAt());
			} else {
				$feed[] = $this->generateItem($item->getName(), $destination, $item, $frequency);
			}
		}

		return $feed;
	}

	private function generateItem($name, $destination, $item = null, $frequency = null, $lastMod = null)
	{
		return new SitemapUrlObject(
			Strings::contains($name, 'front.') ? $this->translator->translate($name) : $name,
			$item,
			$item ? $this->linkGenerator->link($destination, is_array($item) ? $item : [$item]) : $this->linkGenerator->link($destination),
			$frequency,
			$lastMod
		);
	}
}
