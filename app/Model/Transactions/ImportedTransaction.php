<?php

namespace tipli\Model\Transactions;

use DateTime;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;

class ImportedTransaction
{
	public const STATUS_REGISTERED = 'registered';
	public const STATUS_CONFIRMED = 'confirmed';
	public const STATUS_CANCELLED = 'cancelled';

	public const CHANNEL_API = 'api';
	public const CHANNEL_API_REGISTRATION = 'api_registration';
	public const CHANNEL_API_CONFIRMATION = 'api_confirmation';
	public const CHANNEL_WEBHOOK = 'webhook';
	public const CHANNEL_IMPORT = 'import';

	/** @var float */
	private $commissionAmount;

	/** @var float */
	private $orderAmount;

	/** @var string  */
	private $status;

	/** @var DateTime */
	private $registeredAt;

	/** @var string|null */
	private $shopDomain;

	/** @var string|null */
	private $channel;

	/** @var \DateTime|null */
	private $createdAt;

	public function __construct(
		private $partnerSystem,
		private $partnerSystemKey,
		private $userId,
		private $transactionId,
		$commissionAmount,
		$orderAmount,
		private $currency,
		$status = self::STATUS_REGISTERED,
		$registeredAt = null,
		private $confirmedAt = null,
		private $name = null,
		$channel = null,
		\DateTime $createdAt = null
	) {
		if ($commissionAmount < 0) {
			$commissionAmount = 0;
		}

		if ($commissionAmount == 0 && ($status == ImportedTransaction::STATUS_CONFIRMED)) {
			$status = ImportedTransaction::STATUS_CANCELLED;
		}

		$this->commissionAmount = $commissionAmount;
		$this->orderAmount = is_string($orderAmount) && empty(trim($orderAmount)) ? 0 : $orderAmount;
		$this->status = $status;
		$this->registeredAt = $registeredAt ?: new DateTime();

		if ($channel === self::CHANNEL_API) {
			$this->channel = $status === self::STATUS_REGISTERED ? self::CHANNEL_API_REGISTRATION : self::CHANNEL_API_CONFIRMATION;
		} else {
			$this->channel = $channel;
		}

		$this->createdAt = $createdAt ? : new \DateTime();
	}

	/**
	 * @return array
	 */
	public function toStaticArray()
	{
		return [
			'partnerSystem' => is_integer($this->partnerSystem) ? $this->partnerSystem : $this->partnerSystem->getId(),
			'partnerSystemKey' => $this->partnerSystemKey,
			'userId' => $this->userId,
			'transactionId' => $this->transactionId,
			'commissionAmount' => $this->commissionAmount,
			'orderAmount' => $this->orderAmount,
			'currency' => $this->currency,
			'status' => $this->status,
			'registeredAt' => $this->registeredAt ? $this->registeredAt->getTimestamp() : null,
			'confirmedAt' => $this->confirmedAt ? $this->confirmedAt->getTimestamp() : null,
			'createdAt' => $this->createdAt ? $this->createdAt->getTimestamp() : null,
			'name' => $this->name,
			'channel' => $this->channel,
			'uniqueIdentifier' => $this->getUniqueIdentifier(),
		];
	}

	/**
	 * @return ImportedTransaction
	 */
	public static function createFromArray(array $data)
	{
		foreach (['partnerSystem', 'partnerSystemKey', 'userId', 'transactionId', 'commissionAmount', 'orderAmount', 'currency', 'status', 'registeredAt', 'confirmedAt', 'name'] as $requiredKey) {
			if (!array_key_exists($requiredKey, $data)) {
				throw new InvalidArgumentException($requiredKey . ' is missing in imported data');
			}
		}

		$registeredAt = $data['registeredAt'] ? (new \DateTime())->setTimestamp($data['registeredAt']) : null;
		$confirmedAt = $data['confirmedAt'] ? (new \DateTime())->setTimestamp($data['confirmedAt']) : null;
		$createdAt = $data['createdAt'] ? (new \DateTime())->setTimestamp($data['createdAt']) : null;

		return new ImportedTransaction(
			$data['partnerSystem'],
			$data['partnerSystemKey'],
			$data['userId'],
			$data['transactionId'],
			$data['commissionAmount'],
			$data['orderAmount'],
			$data['currency'],
			$data['status'],
			$registeredAt,
			$confirmedAt,
			$data['name'],
			$data['channel'],
			$createdAt
		);
	}

	/**
	 * @return PartnerSystem|int
	 */
	public function getPartnerSystem()
	{
		return $this->partnerSystem;
	}

	/**
	 * @return string
	 */
	public function getPartnerSystemKey()
	{
		return $this->partnerSystemKey;
	}

	/**
	 * @return int
	 */
	public function getUserId()
	{
		return $this->userId;
	}

	/**
	 * @return string|null
	 */
	public function getTransactionId()
	{
		return $this->transactionId;
	}

	/**
	 * @return float
	 */
	public function getCommissionAmount()
	{
		return $this->commissionAmount;
	}

	public function setCommissionAmount($commissionAmount)
	{
		$this->commissionAmount = $commissionAmount;

		return $this;
	}

	/**
	 * @return float
	 */
	public function getOrderAmount()
	{
		return $this->orderAmount;
	}

	public function setOrderAmount($orderAmount)
	{
		$this->orderAmount = $orderAmount;

		return $this;
	}

	/**
	 * @return string
	 */
	public function getCurrency()
	{
		return $this->currency;
	}

	/**
	 * @return string
	 */
	public function getStatus()
	{
		return $this->status;
	}

	public function setStatus($status)
	{
		$this->status = $status;

		return $this;
	}

	/**
	 * @return DateTime
	 */
	public function getRegisteredAt()
	{
		return $this->registeredAt;
	}

	public function getName()
	{
		return $this->name;
	}

	public function setShopDomain($shopDomain)
	{
		$this->shopDomain = $shopDomain;
	}

	public function getShopDomain()
	{
		return $this->shopDomain;
	}

	public function setUserId($userId)
	{
		$this->userId = $userId;
	}

	public function getChannel()
	{
		return $this->channel;
	}

	/**
	 * @return DateTime|null
	 */
	public function getCreatedAt(): ?DateTime
	{
		return $this->createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getConfirmedAt()
	{
		return $this->confirmedAt;
	}

	public function getUniqueIdentifier(): string
	{
		if (is_integer($this->getPartnerSystem())) {
			$partnerSystemId = $this->getPartnerSystem();
		} elseif ($this->getPartnerSystem() instanceof PartnerSystem) {
			$partnerSystemId = $this->getPartnerSystem()->getId();
		} else {
			$partnerSystemId = null;
		}

		if ($partnerSystemId !== null && in_array($partnerSystemId, [8, 9, 85, 114, 125, 126, 127, 132, 152])) {
			$hashDecimalCut = 0;
		} else {
			$hashDecimalCut = 1;
		}

		return Strings::substring(md5(sprintf(
			"%s-%s-%s-%s-%s-%s-%s-%s-%s",
			$this->getTransactionId(),
			$partnerSystemId,
			number_format((float) $this->getCommissionAmount(), $hashDecimalCut, '.', ''),
			number_format((float) $this->getOrderAmount(), $hashDecimalCut, '.', ''),
			$this->getCurrency(),
			$this->getStatus(),
			$this->getShopDomain(),
			$this->getChannel(),
			$this->getUserId()
		)), 0, 16);
	}

	public function getUniqueStateKey(): ?string
	{
		$partnerSystemId = $this->getPartnerSystem() instanceof PartnerSystem ? $this->getPartnerSystem()->getId() : null;
		$partnerSystemId = $partnerSystemId ?? (is_numeric($this->getPartnerSystem()) ? (int) $this->getPartnerSystem() : null);

		$key = null;

		if ($partnerSystemId !== null) {
			$key = $this->getStatus() === ImportedTransaction::STATUS_REGISTERED ? 'registered_' : 'confirmed_';
			$key .= $partnerSystemId . '__' . $this->getTransactionId();

			if ($this->getStatus() !== ImportedTransaction::STATUS_REGISTERED) {
				if ($this->getStatus() === ImportedTransaction::STATUS_CANCELLED || $this->getCommissionAmount() == 0) {
					$key .= '__0';
				} else {
					$key .= '__' . round($this->getCommissionAmount(), 5);
				}
			}
		}

		if ((int) $partnerSystemId === 6) {
			$key = null;
		}

		if ((int) $partnerSystemId === 7) {
			$key .= 'ehub-fix';
		}

		if ((int) $partnerSystemId === 1) {
			$key .= 'awin-fix';
		}

		return $key;
	}

	public function isZeroCommissionAmount(): bool
	{
		return $this->getCommissionAmount() == '' || (float) $this->getCommissionAmount() === 0.0;
	}

	public function isTestTransactionId(): bool
	{
		return $this->transactionId === 'gpt-2070605771|course|4059358';
	}
}
