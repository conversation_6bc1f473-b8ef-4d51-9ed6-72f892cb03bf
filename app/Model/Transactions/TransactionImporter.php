<?php

namespace tipli\Model\Transactions;

use Doctrine\DBAL\Exception\LockWaitTimeoutException;
use Doctrine\ORM\NonUniqueResultException;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Redis\RedisStorageManager;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use Tracy\Debugger;

final class TransactionImporter
{
	/** @var TransactionFacade */
	private $transactionFacade;

	/** @var UserFacade */
	private $userFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var Cache */
	private $cache;

	/** @var PartnerSystemFacade */
	private $partnerSystemFacade;

	/** @var RedirectionResolver */
	private $redirectionResolver;

	/** @var RedisStorageManager */
	private $redisStorageManager;

	/** @var ImportedTransactionChecker */
	private $importedTransactionChecker;

	public function __construct(
		TransactionFacade $transactionFacade,
		UserFacade $userFacade,
		ShopFacade $shopFacade,
		Storage $storage,
		PartnerSystemFacade $partnerSystemFacade,
		RedirectionResolver $redirectionResolver,
		RedisStorageManager $redisStorageManager,
		ImportedTransactionChecker $importedTransactionChecker,
		private SqlQueryScheduler $sqlQueryScheduler
	) {
		$this->transactionFacade = $transactionFacade;
		$this->userFacade = $userFacade;
		$this->shopFacade = $shopFacade;
		$this->cache = new Cache($storage, self::class);
		$this->partnerSystemFacade = $partnerSystemFacade;
		$this->redirectionResolver = $redirectionResolver;
		$this->redisStorageManager = $redisStorageManager;
		$this->importedTransactionChecker = $importedTransactionChecker;
	}

	/**
	 * @param ImportedTransaction $importedTransaction
	 * @return mixed|Entities\Transaction|null
	 */
	public function processImportedTransaction(ImportedTransaction $importedTransaction)
	{
		/** @var ImportedTransactionCheckResult $importedTransactionCheckResult */
		$importedTransactionCheckResult = $this->importedTransactionChecker->checkImportedTransaction($importedTransaction);

		if ($importedTransactionCheckResult->isOk()) {
//			Debugger::log('skipped by check - ' . $importedTransaction->getTransactionId(), 'transaction-importer');
			// @todo testovaci provoz
			//return;
		}

		try {
			$transaction = $this->tryProcessImportedTransaction($importedTransaction);
		} catch (InvalidArgumentException $exception) {
			$partnerSystem = $this->partnerSystemFacade->getPartnerSystemFromImportedTransaction($importedTransaction);

			$key = implode('.', [
				$partnerSystem ? $partnerSystem->getId() : '?',
				$importedTransaction->getTransactionId() ?: '?',
				$importedTransaction->getUserId() ?: '?',
			]);

			if (!$this->cache->load($key)) {
				$this->sqlQueryScheduler->createImportedTransactionFail(
					$partnerSystem,
					$importedTransaction->getTransactionId(),
					$exception->getMessage()
				);

				$this->cache->save($key, true, [Cache::EXPIRE => '48 hours']);
			}

			return null;
		}

		$stateKey = $importedTransaction->getUniqueStateKey();

		if ($stateKey !== null) {
			if ($this->redisStorageManager->read($stateKey) === null) {
				$this->redisStorageManager->write($stateKey, true);
			}
		}

		return $transaction;
	}

	private function tryProcessImportedTransaction(ImportedTransaction $importedTransaction)
	{
		/** @var PartnerSystem $partnerSystem */
		$partnerSystem = $this->partnerSystemFacade->getPartnerSystemFromImportedTransaction($importedTransaction);

		/** @var string $transactionId */
		$transactionId = $importedTransaction->getTransactionId();

		if ($transactionId === 'xhi6e71b') {
			Debugger::log('1', 'cj-test');
		}

		$logMessageSuffix = sprintf(
			' ID transakce: %s, Partnerský systém: %s, ID uživatele: %s',
			$transactionId ?: '?',
			$partnerSystem ? $partnerSystem->getName() : '?',
			$importedTransaction->getUserId() ?: '?'
		);

		if (!$partnerSystem) {
			throw new InvalidArgumentException('Neznámý partnerský systém' . $logMessageSuffix);
		}

		if (!$transactionId) {
			throw new InvalidArgumentException('Chybí ID transakce. Datum registrace transakce: ' . $importedTransaction->getRegisteredAt()->format('d.m.Y H:i:s') . $logMessageSuffix);
		}

		if ($transactionId === 'xhi6e71b') {
			Debugger::log('2', 'cj-test');
		}

		if (!$importedTransaction->getUserId()) {
			throw new InvalidArgumentException('Chybí id uživatele' . $logMessageSuffix);
		}

		try {
			$transaction = $this->transactionFacade->findByTransactionId($transactionId, $partnerSystem);
		} catch (NonUniqueResultException $e) {
			throw new InvalidArgumentException('Duplicitni transakce' . $logMessageSuffix);
		}

		if ($transactionId === 'xhi6e71b') {
			Debugger::log('3', 'cj-test');
		}

		if (!$transaction) {
			if ($transactionId === 'xhi6e71b') {
				Debugger::log('4', 'cj-test');
			}

			/** @var User|null $user */
			$user = $this->userFacade->find($importedTransaction->getUserId());

			/** @var Localization|null $localization */
			$localization = $user ? $user->getLocalization() : null;

			if ($importedTransaction->getShopDomain()) {
				$shop = $this->shopFacade->findShopByDomain($localization, $importedTransaction->getShopDomain());
				if (!$shop) {
					throw new InvalidArgumentException('Obchod s doménou ' . $importedTransaction->getShopDomain() . ' nebyl nalezen' . $logMessageSuffix);
				}
			} elseif ($importedTransaction->getPartnerSystemKey()) {
				$shop = $this->shopFacade->findByPartnerSystem($partnerSystem, $importedTransaction->getPartnerSystemKey(), $localization);
				if (!$shop) {
					throw new InvalidArgumentException('Obchod s partnerským klíčem ' . $importedTransaction->getPartnerSystemKey() . ' nebyl nalezen' . $logMessageSuffix);
				}
			} elseif (
				Strings::contains($importedTransaction->getUserId(), 'x')
				&& !Strings::endsWith($importedTransaction->getUserId(), 'x')
			) {
				/** @var Redirection $redirection */
				$redirection = $this->redirectionResolver->resolveRedirection($importedTransaction->getUserId());

				if (!$redirection) {
					throw new InvalidArgumentException('Přesměrování ' . $importedTransaction->getUserId() . ' nebylo nalezeno');
				}

				$shop = $redirection->getShop();

//				Debugger::log('transactionId: ' . $importedTransaction->getTransactionId() . ' userId' . $importedTransaction->getUserId(), 'transaction-shop-by-redirection');
			}

			if (!isset($shop)) {
				throw new InvalidArgumentException('unknown shop transactionId: ' . $importedTransaction->getTransactionId() . ' userId: ' . $importedTransaction->getUserId() . ' partnerSystemKey: ' . $importedTransaction->getPartnerSystemKey());
			}

			if ($shop && $shop instanceof Shop && !$shop->isVisible()) {
				$message = 'Obchod ' . $shop->getName() . ' je skrytý a nelze pro něj vytvářet transakce' . $logMessageSuffix;
				throw new InvalidArgumentException($message);
			}

			$isZeroCommission = $importedTransaction->isZeroCommissionAmount();

			if ($isZeroCommission) {
				if (($partnerSystem->isEhub() || $partnerSystem->isCj() || $partnerSystem->getId() === 193) && $importedTransaction->getRegisteredAt() > new \DateTime('2025-01-01')) {
					Debugger::log('import transaction: ' . $importedTransaction->getTransactionId(), 'import-zero-commission-transactions');
					# return null;
				} else {
					throw new InvalidArgumentException(
						sprintf(
							'prazdny commission amount, transactionId: %s userId: %s partnerSystemKey: %s',
							$importedTransaction->getTransactionId(),
							$importedTransaction->getUserId(),
							$importedTransaction->getPartnerSystemKey()
						)
					);
				}
			}

			if ($importedTransaction->getStatus() === ImportedTransaction::STATUS_CANCELLED && $partnerSystem->getType() === PartnerSystem::TYPE_CJ) {
				throw new InvalidArgumentException('Nelze vytvorit nulovou transakci, (CJ Bug od 19.11.2022');
			}

			try {
				$billable = true;

				if ($importedTransaction->getRegisteredAt() < (new \DateTime())->modify('-30 days')) {
					$billable = false;
				}

				// podmínka pro Sazka Hry pokud už uživatel jednu odměnu má, tak všechny další budou připsány s billable = 0
				if ($partnerSystem->isAffilMaster() && $user && ($shop->getId() === 11640 || $shop->getId() === 5283) && $this->transactionFacade->findTransactionsByShopForUser($shop, $user)) {
					$billable = false;
				}

				$transaction = $this->transactionFacade->createOrReturnCommissionTransaction(
					$importedTransaction->getUserId(),
					$shop,
					$importedTransaction->getTransactionId(),
					$importedTransaction->getCurrency(),
					$importedTransaction->getCommissionAmount(),
					$importedTransaction->getOrderAmount(),
					$importedTransaction->getRegisteredAt(),
					$importedTransaction->getCreatedAt(),
					$partnerSystem,
					$billable
				);

				if ($partnerSystem && $transaction->getPartnerSystem() !== $partnerSystem) {
					$transaction->setPartnerSystem($partnerSystem);

					$this->transactionFacade->saveTransaction($transaction);
				}
			} catch (NonUniqueResultException $e) {
				throw new \Exception('Two or more same transactions with transaction_id: ' . $importedTransaction->getTransactionId());
			} catch (LockWaitTimeoutException $e) {
				$this->redisStorageManager->remove($importedTransaction->getUniqueStateKey());

				Debugger::log('Lock wait timeout: ' . $importedTransaction->getTransactionId(), 'transaction-importer-lock-wait-timeout');
			}
		}

		if ($transactionId === 'xhi6e71b') {
			Debugger::log('5', 'cj-test');
		}

		if ($transaction->isConfirmed() === false && $transaction->getUser() === null && ($user = $this->userFacade->find($importedTransaction->getUserId()))) {
			$this->transactionFacade->assignUserToTransaction($transaction, $user);
		}

		if ($importedTransaction->getStatus() === ImportedTransaction::STATUS_CONFIRMED) {
			if ($transactionId === 'xhi6e71b') {
				Debugger::log('6', 'cj-test');
			}

			if (($importedTransaction->getCommissionAmount() !== $transaction->getOriginalCommissionAmount()) || !$transaction->isConfirmed()) {
				if ($transactionId === 'xhi6e71b') {
					Debugger::log('7', 'cj-test');
				}

				$this->transactionFacade->confirm($transaction, $importedTransaction->getCommissionAmount(), $importedTransaction->getCurrency());
			} else {
				if ($transactionId === 'xhi6e71b') {
					Debugger::log('8', 'cj-test');
				}

				$this->transactionFacade->confirm($transaction);
			}
		}

		if ($importedTransaction->getStatus() === ImportedTransaction::STATUS_CANCELLED) {
			if ($transactionId === 'xhi6e71b') {
				Debugger::log('9', 'cj-test');
			}

			$this->transactionFacade->cancel($transaction);
		}

		return $transaction;
	}
}
