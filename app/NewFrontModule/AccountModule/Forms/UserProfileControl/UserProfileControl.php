<?php

namespace tipli\NewFrontModule\AccountModule\Forms\UserProfileControl;

use Nette\Localization\Translator;
use Nette;
use Nette\Application\UI\Form;
use Nette\Security\IIdentity;
use Nette\Utils\DateTime;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidStateException;
use tipli\Model\Account\BannedUserFacade;
use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserDuplicity;
use tipli\Model\Account\UserDuplicityCheckerFacade;
use tipli\Model\Account\UserFacade;
use tipli\Model\Configuration;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\BrowserTokenLayer;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Tickets\Entities\Ticket;
use tipli\Model\Tickets\TicketFacade;
use Tracy\Debugger;

class UserProfileControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	/**
	 * @var Translator
	 */
	private $translator;

	/**
	 * @var UserFacade
	 */
	private $userFacade;

	/**
	 * @var TicketFacade
	 */
	private $ticketFacade;

	/**
	 * @var User|IIdentity
	 */
	private $user;

	/**
	 * @var Configuration
	 */
	private $configuration;

	/**
	 * @var ClientLayer
	 */
	private $clientLayer;

	/**
	 * @var BrowserTokenLayer
	 */
	private $browserTokenLayer;

	/**
	 * @var EmailSubscriptionManager
	 */
	private $emailSubscriptionManager;

	/** @var BannedUserFacade */
	private $bannedUserFacade;

	/**@var UserDuplicityCheckerFacade */
	private $userDuplicityCheckerFacade;

	public function __construct(Translator $translator, UserFacade $userFacade, TicketFacade $ticketFacade, \Nette\Security\User $user, Configuration $configuration, ClientLayer $clientLayer, BrowserTokenLayer $browserTokenLayer, EmailSubscriptionManager $emailSubscriptionManager, BannedUserFacade $bannedUserFacade, UserDuplicityCheckerFacade $userDuplicityCheckerFacade)
	{
		if (!$user->isLoggedIn()) {
			throw new InvalidStateException('The user must be logged in.');
		}

		$this->translator = $translator;
		$this->userFacade = $userFacade;
		$this->ticketFacade = $ticketFacade;
		$this->user = $user->getIdentity();
		$this->configuration = $configuration;
		$this->clientLayer = $clientLayer;
		$this->browserTokenLayer = $browserTokenLayer;
		$this->emailSubscriptionManager = $emailSubscriptionManager;
		$this->bannedUserFacade = $bannedUserFacade;
		$this->userDuplicityCheckerFacade = $userDuplicityCheckerFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addGroup();

		$form->addText('firstName', 'front.account.user.settings.form.firstName');

		$form->addText('lastName', 'front.account.user.settings.form.lastName');

		$form->addRadioList('gender', 'front.account.user.settings.form.gender', $this->userFacade->getGenders() + [null => $this->translator->translate('model.account.user.gender.na')]);

		$form->addText('birthdate', 'front.account.user.settings.form.birthdate')
			->setHtmlType('date')
			->addCondition(Form::FILLED)
			->addRule(Form::PATTERN, 'front.account.user.settings.form.validator.birthdatePattern', '^\d{4}-\d{2}-\d{2}$')
		;

		if ($this->user->isAdmin()) {
			$allowedPhoneCountryCodes = ['+420', '+421', '+48', '+40', '+36', '+386', '+385', '+359'];
		} else {
			$allowedPhoneCountryCodes = $this->configuration->getAllowedPhoneCountryCodes($this->user->getLocalization());
		}

		if ($userPhoneNumberCountryCode = $this->user->getPhoneNumberCountryCode()) {
			$allowedPhoneCountryCodes[] = $userPhoneNumberCountryCode;
		}

		$form->addSelect('phoneNumberCountryCode')
			->setItems($allowedPhoneCountryCodes, false);

		$form->addText('phoneNumber', 'front.account.user.settings.form.phoneNumber')
			->addCondition(Form::FILLED)
			->addRule(Form::PATTERN, 'front.account.user.settings.form.validator.phoneNumber', '^[()0-9 -]{9,11}$');

		$form->addGroup($this->translator->translate('model.account.sendingPolicy.messageType.email'));
		foreach (SendingPolicy::getEmailContentTypes() as $emailContentType => $contentType) {
			$form->addCheckbox($emailContentType, 'model.account.sendingPolicy.contentType.' . $contentType);
		}

		$form->addGroup($this->translator->translate('model.account.sendingPolicy.messageType.SMS'));
		foreach (SendingPolicy::getSMSContentTypes() as $smsContentType => $contentType) {
			$form->addCheckbox($smsContentType, 'model.account.sendingPolicy.contentType.' . $contentType);
		}

		$form->addGroup();
		$form->addSubmit('submit', 'front.account.user.settings.form.submit');

		if ($this->user) {
			$defaults = [
				'firstName' => $this->user->getFirstName(),
				'lastName' => $this->user->getLastName(),
				'gender' => $this->user->getGender() ? $this->user->getGender() : null,
				'birthdate' => $this->user->getBirthdate() ? $this->user->getBirthdate()->format('Y-m-d') : null,
//                'accountNumber' => $this->user->getAccountNumber(),
			];

			try {
				if ($this->user->getPhoneNumber()) {
					$form['phoneNumberCountryCode']->setDefaultValue($this->user->getPhoneNumberCountryCode());
					$form['phoneNumber']->setDefaultValue($this->user->getPhoneNumberWithoutCountryCode());
				}
			} catch (InvalidArgumentException $e) {
				Debugger::log('Invalid phone number - user id:' . $this->user->getId(), 'phone-number-format-errors');
			}

//            $form->setDefaults(array_merge($defaults, $this->user->getSendingPolicyValues(), $this->user->getPrivacyPolicyValues()));
			$form->setDefaults(array_merge($defaults, $this->user->getSendingPolicyValues()));

			if ($this->user->hasUnsubscribedEmails()) {
				foreach (SendingPolicy::getEmailContentTypes() as $emailContentType => $contentType) {
					$form[$emailContentType]->setDefaultValue(false);
				}
			}
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$user = $this->user;
			$oldPhoneNumber = $user->getPhoneNumber();

			if (
				$this->configuration->getMode() === 'normal'
				&& !$user->isMember()
				&& $oldPhoneNumber !== $values->phoneNumberCountryCode . ' ' .  str_replace(' ', '', $values->phoneNumber)
			) {
				throw new InvalidArgumentException('Administrátorům není umožněno měnit telefonní číslo v tomto formuláři.');
			}

			$birthdate = !empty($values->birthdate) ? DateTime::createFromFormat('Y-m-d', $values->birthdate) : null;


			if ($birthdate) {
				if ($birthdate->diff(new DateTime())->y > 125) {
					throw new InvalidArgumentException($this->translator->translate('front.account.personalization.form.validator.birthdatePattern'));
				}
			}

			$phoneNumber = str_replace(' ', '', $values->phoneNumber);

			if (
				!empty($phoneNumber) &&
				(
					$this->bannedUserFacade->isPhoneNumberBanned($phoneNumber) ||
					$this->bannedUserFacade->isPhoneNumberBanned($values->phoneNumberCountryCode . ' ' . $phoneNumber)
				)
			) {
				$this->ticketFacade->createTicket(
					$user,
					Ticket::TYPE_SUSPECTED_USER,
					Ticket::REASON_TYPE_SET_BANNED_PHONE_NUMBER,
					'Pokus o zadání zabanovaného telefonního čísla: ' . $phoneNumber,
					new \DateTime()
				);

				throw new InvalidArgumentException($this->translator->translate('front.account.user.settings.form.validator.phoneNumber'));
			}

			$change = $this->userFacade->createChange($user, $this->browserTokenLayer->getBrowserToken(), $this->clientLayer->getIp(), $this->clientLayer->getUserAgent(), false);

			if ($values->firstName !== $user->getFirstName()) {
				$change->setFirstName(ContentFilter::stringSanitize($values->firstName));
			}

			if ($values->lastName !== $user->getLastName()) {
				$change->setLastName(ContentFilter::stringSanitize($values->lastName));
			}

			if (($values->phoneNumberCountryCode . ' ' . str_replace(' ', '', $values->phoneNumber)) !== $user->getPhoneNumber()) {
				$phoneNumberWithCountryCode = ContentFilter::stringSanitize($values->phoneNumberCountryCode . ' ' . $phoneNumber);
				$change->setPhoneNumber($phoneNumberWithCountryCode);

				if ($phoneNumber) {
					$this->userDuplicityCheckerFacade->scheduleUserDuplicityCheck($user, UserDuplicity::TYPE_PHONE_NUMBER, $phoneNumberWithCountryCode);
				}
			}

			if ($values->gender !== $user->getGender()) {
				$change->setGender($values->gender !== '' ? $values->gender : null);
			}

			if (
				($birthdate && !$user->getBirthdate())
				|| (!$birthdate && $user->getBirthdate())
				|| ($birthdate && $user->getBirthdate() && $birthdate->format('d.m.Y') !== $user->getBirthdate()->format('d.m.Y'))
			) {
				$change->setBirthdate($birthdate);
			}

			$this->userFacade->saveChange($change);

			$user->setFirstName(ContentFilter::stringSanitize($values->firstName));
			$user->setLastName(ContentFilter::stringSanitize($values->lastName));
			$user->setGender($values->gender !== '' ? $values->gender : null);
			$user->setBirthDate($birthdate ?: null);

			if (!empty($values->phoneNumber) && !empty($values->phoneNumberCountryCode)) {
				$user->setPhoneNumber($values->phoneNumberCountryCode, str_replace(' ', '', $values->phoneNumber));
			} else {
				$user->clearPhoneNumber();
			}

			$emailsSubscribed = false;
			foreach (
				[
				'emailNewsletter' => SendingPolicy::CONTENT_TYPE_NEWSLETTER,
				'emailNotification' => SendingPolicy::CONTENT_TYPE_NOTIFICATION,
				'emailAutoresponder' => SendingPolicy::CONTENT_TYPE_AUTORESPONDER,
				'emailLucky_shop' => SendingPolicy::CONTENT_TYPE_LUCKY_SHOP] as $key => $contentType
			) {
				if ($key === 'emailLucky_shop' && $user->isAdmin() === false && $this->configuration->isThereLuckyShops($user->getLocalization()) === false) {
					continue;
				}

				if ($values[$key]) {
					$emailsSubscribed = true;
					$this->emailSubscriptionManager->subscribeContentType($user, null, $contentType, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_SETTINGS, 'prihlaseno samotnym uzivatelem');
				} else {
					$this->emailSubscriptionManager->unsubscribeContentType($user, null, $contentType, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_SETTINGS, 'odhlaseno samotnym uzivatelem');
				}
			}

			if ($user->hasUnsubscribedEmails() && $emailsSubscribed) {
				$this->emailSubscriptionManager->subscribe($user, null, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_SETTINGS, 'prihlaseno samotynm uzivatelem');
			}

			if ($values->SMSNotification) {
				$this->userFacade->subscribeSendingPolicy($user, SendingPolicy::MESSAGE_TYPE_SMS, SendingPolicy::CONTENT_TYPE_NOTIFICATION);
			} else {
				$this->userFacade->unsubscribeSendingPolicy($user, SendingPolicy::MESSAGE_TYPE_SMS, SendingPolicy::CONTENT_TYPE_NOTIFICATION);
			}

			$needPhoneNumberVerification = $user->getPhoneNumber() && (($oldPhoneNumber != $user->getPhoneNumber()) || !$user->hasVerifiedPhoneNumber());

			if ($needPhoneNumberVerification === true && ($user->isSlovenian() || Strings::startsWith($user->getPhoneNumber(), '+386'))) {
				$userWithVerifiedPhoneNumber = $this->userFacade->findUserWithVerifiedPhoneNumber($user->getPhoneNumber());

				if ($userWithVerifiedPhoneNumber !== null) {
					$user->verifyPhoneNumber();
					$needPhoneNumberVerification = false;
				}
			}

			$this->userFacade->saveUser($user);

			if (!$user->getPhoneNumber() || $needPhoneNumberVerification) {
				$this->userFacade->cancelPhoneNumberVerification($user);
			}

			$this->onSuccess($this, $needPhoneNumberVerification);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}
