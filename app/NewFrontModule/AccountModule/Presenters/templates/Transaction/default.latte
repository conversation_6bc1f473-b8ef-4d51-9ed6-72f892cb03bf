{block title}{_front.account.transaction.default.title}{/block}

{block heading}{_front.account.transaction.default.title}{/block}

{block innerContent}
<div class="grid grid-cols-1 md:grid-cols-3 gap-5 mb-5 px-5 md:px-0">
    <div class="bg-primary-blue-dark rounded-2xl pt-[18px] pl-[22px] pb-4 relative">
        <svg class="absolute top-0 right-0" xmlns="http://www.w3.org/2000/svg" width="51" height="50"
             viewBox="0 0 51 50" fill="none">
            <g opacity="0.7">
                <path d="M27.6146 -9.37803C28.0219 -9.88435 28.5371 -10.2929 29.1229 -10.5736C29.7087 -10.8543 30.35 -11 30.9995 -11C31.649 -11 32.2903 -10.8543 32.8761 -10.5736C33.4619 -10.2929 33.9771 -9.88435 34.3844 -9.37803L38.4142 -4.36203C38.8705 -3.79296 39.463 -3.34808 40.1365 -3.06846C40.81 -2.78883 41.5432 -2.68345 42.2681 -2.76203L48.6647 -3.45803C49.3124 -3.53001 49.9678 -3.45534 50.5824 -3.23958C51.1972 -3.02379 51.7556 -2.67243 52.2164 -2.2115C52.677 -1.75057 53.0283 -1.19187 53.2439 -0.576782C53.4595 0.0383373 53.5341 0.694098 53.4622 1.34196L52.7665 7.74196C52.6879 8.46734 52.7932 9.20084 53.0728 9.87476C53.3524 10.5487 53.7969 11.1413 54.3657 11.598L59.379 15.6299C59.8851 16.0374 60.2934 16.5531 60.5738 17.139C60.8545 17.7251 61 18.3667 61 19.0166C61 19.6664 60.8545 20.308 60.5738 20.8942C60.2934 21.4803 59.8851 21.996 59.379 22.4032L54.3497 26.4165C53.7812 26.8739 53.3366 27.4672 53.057 28.1414C52.7777 28.8157 52.6722 29.5496 52.7505 30.2752L53.4462 36.6752C53.5187 37.3232 53.4443 37.9792 53.2289 38.5947C53.0133 39.2099 52.6623 39.7688 52.2015 40.2299C51.7407 40.6909 51.182 41.0421 50.5669 41.2579C49.952 41.4733 49.2964 41.5477 48.6487 41.4752L42.2522 40.7792C41.5272 40.7003 40.794 40.8056 40.1202 41.0853C39.4467 41.3651 38.8545 41.8099 38.3982 42.3792L34.3844 47.3765C33.9776 47.8832 33.4622 48.2923 32.8764 48.5733C32.2905 48.8541 31.6493 49 30.9995 49C30.35 49 29.7085 48.8541 29.1226 48.5733C28.5368 48.2923 28.0216 47.8832 27.6146 47.3765L23.5875 42.3632C23.1305 41.7941 22.5377 41.3493 21.8637 41.0696C21.1897 40.7901 20.4563 40.6848 19.7309 40.7632L13.3343 41.4592C12.6865 41.5323 12.0307 41.4584 11.4155 41.2429C10.8002 41.0275 10.2414 40.6763 9.78045 40.2149C9.31955 39.7539 8.9684 39.1947 8.7531 38.5792C8.53781 37.9635 8.4639 37.3072 8.53685 36.6592L9.23248 30.2592C9.31102 29.5336 9.20569 28.7995 8.92621 28.1254C8.64674 27.451 8.20212 26.8579 7.63333 26.4006L2.62268 22.3712C2.1162 21.9643 1.70749 21.4486 1.42663 20.8624C1.14579 20.2763 1 19.6347 1 18.9846C1 18.3347 1.14579 17.6928 1.42663 17.1067C1.70749 16.5206 2.1162 16.0051 2.62268 15.5979L7.63333 11.566C8.20231 11.1095 8.64708 10.5169 8.92659 9.84289C9.20609 9.16894 9.31129 8.43534 9.23248 7.70996L8.53685 1.30996C8.46441 0.661991 8.53869 0.00599139 8.75417 -0.609341C8.96963 -1.22467 9.3208 -1.78361 9.7816 -2.24465C10.2424 -2.70569 10.801 -3.05705 11.416 -3.27262C12.031 -3.48822 12.6867 -3.56254 13.3343 -3.49003L19.7309 -2.79403C20.4562 -2.71574 21.1897 -2.82123 21.8636 -3.10083C22.5375 -3.38043 23.1303 -3.82512 23.5875 -4.39403L27.6146 -9.37803Z"
                      stroke="#66B940" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M39.2639 14.2178L30.4942 25.7411C30.3521 25.9276 30.171 26.0817 29.9633 26.1933C29.7553 26.3049 29.5258 26.3711 29.2896 26.3876C29.0537 26.4041 28.8169 26.3703 28.5953 26.2888C28.3735 26.2072 28.1723 26.0798 28.0049 25.9148L22.7422 20.7306"
                      stroke="#66B940" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            </g>
        </svg>
        <div class="text-[26px] md:text-[33px] leading-[39px] leading-[49.5px] font-bold text-white md:mb-6 account-info-table__value--green">{$confirmedBalance|amount, 2, ',', false}&nbsp;{$user->getIdentity()->getLocalization()->getCurrency() |currency: 2}</div>
        <div class="flex items-center gap-2.5 uppercase text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-white">
            {_'front.account.transaction.default.confirmedBalance.title'}
            <svg id="confirmedBalance-tooltip" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                <path d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
                      stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                <path d="M8.41797 7.60938V12.2089" stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="8.41848" cy="4.88918" r="0.906757" fill="#8B95A4" />
            </svg>
        </div>

        <span class="hidden help-tooltip" data-toggle="tooltip" data-placement="top" title="" data-original-title="{_'front.account.transaction.default.confirmedBalance.tooltip'}">?</span>
    </div>

    <div class="bg-primary-blue-dark rounded-2xl pt-[18px] pl-[22px] pb-4 relative">
        <svg class="absolute top-0 right-0" xmlns="http://www.w3.org/2000/svg" width="51" height="50"
             viewBox="0 0 51 50" fill="none">
            <g opacity="0.7">
                <path d="M12.2924 14.7966C9.61773 15.9467 7.26546 17.7343 5.44118 20.0035C3.6169 22.2724 2.37614 24.9538 1.82736 27.8131C1.27857 30.6722 1.43845 33.6224 2.29302 36.4056C3.14762 39.1889 4.67087 41.7203 6.72967 43.7791C8.78847 45.8377 11.3202 47.3605 14.1035 48.2148C16.8867 49.0692 19.8369 49.2287 22.6961 48.6797C25.5552 48.1306 28.2365 46.8897 30.5055 45.065C32.7742 43.2405 34.5615 40.8881 35.7115 38.2132M19.3493 13.3303C19.3493 18.058 21.2274 22.5924 24.5705 25.9354C27.9135 29.2784 32.4478 31.1566 37.1756 31.1566C41.9034 31.1566 46.4377 29.2784 49.7807 25.9354C53.1238 22.5924 55.002 18.058 55.002 13.3303C55.002 8.60242 53.1238 4.06821 49.7807 0.725126C46.4377 -2.61796 41.9034 -4.49609 37.1756 -4.49609C32.4478 -4.49609 27.9135 -2.61796 24.5705 0.725126C21.2274 4.06821 19.3493 8.60242 19.3493 13.3303Z"
                      stroke="#66B940" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M18.8 37.3302C18.561 37.3302 18.3129 37.2836 18.0559 37.1935C17.5288 37.0035 17.0959 36.6021 16.8549 36.0789C16.7265 35.8057 16.6636 35.492 16.6636 35.1472V33.0477H21.065V30.0942H16.6633V26.3613H13.8516V35.1349C13.8516 36.0323 14.0039 36.8117 14.3089 37.467C14.6136 38.1255 15.0052 38.6658 15.4862 39.0913C15.9672 39.5169 16.4962 39.8305 17.0699 40.0385C17.6466 40.2466 18.2085 40.3521 18.7523 40.3521C19.296 40.3521 19.8609 40.2466 20.4466 40.0385C23.5691 38.9267 23.7274 35.9454 23.7274 34.4546H20.9396C20.9336 35.0945 20.8859 35.728 20.7096 36.0789C20.5752 36.3491 20.4079 36.5788 20.2076 36.7618C19.8176 37.1175 19.3186 37.3192 18.8 37.3302Z"
                      fill="#66B940" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M36.906 18.8696C36.6271 18.8696 36.3378 18.8176 36.0379 18.7169C35.4229 18.5045 34.9179 18.0559 34.6367 17.4712C34.4869 17.1658 34.4135 16.8152 34.4135 16.4299V14.0833H39.5485V10.7824H34.4132V6.61035H31.1328V16.4161C31.1328 17.4191 31.3106 18.2902 31.6663 19.0226C32.0218 19.7585 32.4787 20.3624 33.0399 20.838C33.6011 21.3136 34.2182 21.6642 34.8875 21.8965C35.5603 22.1292 36.2159 22.2471 36.8503 22.2471C37.4846 22.2471 38.1437 22.1292 38.827 21.8965C42.4699 20.654 42.6546 17.3219 42.6546 15.6558H39.4022C39.3952 16.3709 39.3396 17.0789 39.1338 17.4712C38.9771 17.7731 38.7818 18.0299 38.5482 18.2344C38.0932 18.632 37.511 18.8574 36.906 18.8696Z"
                      fill="#66B940" />
            </g>
        </svg>
        <div class="text-[26px] md:text-[33px] leading-[39px] leading-[49.5px] font-bold text-white md:mb-6 account-info-table__value--green">{$registeredCommissionBalance|amount, 2, ',', false}&nbsp;{$user->getIdentity()->getLocalization()->getCurrency() |currency}</div>
        <div class="flex items-center gap-2.5 uppercase text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-white">
            {_'front.account.transaction.default.registeredCommissionBalance.title'}
            <svg id="registeredCommissionBalance-tooltip" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                <path d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
                      stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                <path d="M8.41797 7.60938V12.2089" stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="8.41848" cy="4.88918" r="0.906757" fill="#8B95A4" />
            </svg>
        </div>

        <span class="hidden help-tooltip" data-toggle="tooltip" data-placement="top" title="" data-original-title="{_'front.account.transaction.default.registeredCommissionBalance.tooltip'}">?</span>
    </div>

    <div class="bg-primary-blue-dark rounded-2xl pt-[18px] pl-[22px] pb-4 relative">
        <svg class="absolute top-[7px] right-0" xmlns="http://www.w3.org/2000/svg" width="47" height="49"
             viewBox="0 0 47 49" fill="none">
            <g opacity="0.7">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M17.1486 16.5128C16.9391 16.5128 16.7218 16.4762 16.4966 16.4053C16.0347 16.2556 15.6554 15.9397 15.4441 15.5278C15.3316 15.3127 15.2765 15.0658 15.2765 14.7944V13.1416H19.1333V10.8165H15.2763V7.87793H12.8125V14.7846C12.8125 15.4911 12.946 16.1047 13.2132 16.6206C13.4802 17.1389 13.8233 17.5643 14.2449 17.8993C14.6664 18.2343 15.1299 18.4812 15.6326 18.6448C16.1379 18.8087 16.6303 18.8918 17.1067 18.8918C17.5832 18.8918 18.0782 18.8087 18.5914 18.6448C21.3275 17.7696 21.4662 15.4227 21.4662 14.2491H19.0234C19.0182 14.7528 18.9764 15.2515 18.8219 15.5278C18.7041 15.7405 18.5575 15.9214 18.382 16.0654C18.0402 16.3454 17.603 16.5042 17.1486 16.5128Z"
                      fill="#66B940" />
                <path d="M18.1721 36.0483L4.90949 31.8116L4.81776 31.786C4.07175 31.6226 3.29161 31.7361 2.62325 32.1056C1.9549 32.4751 1.44376 33.0752 1.18542 33.7939C0.927085 34.5124 0.939032 35.3006 1.21913 36.011C1.49944 36.7216 2.0285 37.3057 2.70795 37.6547C28.5995 51.0602 21.9949 50.4543 49 41.4583M48.9979 25.0321H44.3047C38.8497 24.9764 33.5578 26.8914 29.4016 30.425L29.3142 30.5018H20.5677C19.6952 30.5018 18.8586 30.8483 18.2416 31.4652C17.6249 32.0821 17.2782 32.9188 17.2782 33.7913C17.2776 34.6611 17.6217 35.4958 18.235 36.1127C18.8481 36.7295 19.6809 37.0785 20.5507 37.083L33.8495 37.1491M28.5803 13.0004C28.5803 16.1914 27.3125 19.2517 25.0562 21.5081C22.7998 23.7645 19.7396 25.0321 16.5486 25.0321C13.3575 25.0321 10.2973 23.7645 8.04092 21.5081C5.78456 19.2517 4.51696 16.1914 4.51696 13.0004C4.51696 9.80943 5.78456 6.74913 8.04092 4.49275C10.2973 2.23638 13.3575 0.96875 16.5486 0.96875C19.7396 0.96875 22.7998 2.23638 25.0562 4.49275C27.3125 6.74913 28.5803 9.80943 28.5803 13.0004Z"
                      stroke="#66B940" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </g>
        </svg>
        <div class="text-[26px] md:text-[33px] leading-[39px] leading-[49.5px] font-bold text-white md:mb-6 account-info-table__value--green">{$registeredBonusBalance|amount, 2, ',', false}&nbsp;{$user->getIdentity()->getLocalization()->getCurrency() |currency}</div>
        <div class="flex items-center gap-2.5 uppercase text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-white">
            {_'front.account.transaction.default.registeredBonusBalance.title'}
            <svg id="registeredBonusBalance-tooltip" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                <path d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
                      stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                <path d="M8.41797 7.60938V12.2089" stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10"
                      stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="8.41848" cy="4.88918" r="0.906757" fill="#8B95A4" />
            </svg>
        </div>
    </div>

    <span class="hidden help-tooltip" data-toggle="tooltip" data-placement="top" title="" data-original-title="{_'front.account.transaction.default.registeredBonusBalance.tooltip'}">?</span>
</div>

{if $nonProfitTransactionsAmount > 0}
    <div class="hidden md:block text-white bg-primary-blue-dark rounded-lg pt-[25px] pb-[26px] pl-[22px] leading-7 mb-5">
        {_'newFront.account.transactionsList.nonProfit', [amount => ($nonProfitTransactionsAmount |amount)]|noescape}
    </div>
{/if}

<!-- 1. Boxík -->
<div n:if="!$hasTransaction && $hasRedirection" class="p-5 lg:p-10 lg:bg-white rounded-2xl mb-10 relative w-full max-w-[773px] lg:rounded-tr-none">
	<div class="text-[20px] lg:text-[30px] leading-[32px] lg:leading-[37px] font-bold mb-[15px] text-primary-orange">💤
		{_'newFront.account.transactions.box1.title'}
	</div>
	<div class="text-sm leading-[24.5px] text-dark-1 mb-5">
		<div class="font-medium lg:font-bold">
			{_'newFront.account.transactions.box1.descriptionBold'}
		</div>
		<div class="w-full max-w-[554px]">
			{_'newFront.account.transactions.box1.description'|noescape}
		</div>
	</div>

	<div class="grid grid-cols-3 lg:grid-cols-4 gap-[7px] lg:gap-0 w-full max-w-[564px] mb-5">
		{var tipli\Model\Shops\Entities\Redirection[] $redirections = $getRedirections()}
		<a n:if="current($redirections) !== false"
			class="bg-white lg:bg-transparent w-full lg:max-w-[126px] h-[77px] md:border md:border-light-4 rounded-xl p-4 flex items-center justify-center shadow-hover" n:href=":NewFront:Account:LastRedirections:default">
			<img class="max-h-[30px] max-w-[80px]" alt="{current($redirections)->getShop()}" src="{current($redirections)->getShop()->getCurrentLogo()|image:300}">
		</a>
		<a n:if="next($redirections) !== false"
			class="bg-white lg:bg-transparent w-full lg:max-w-[126px] h-[77px] md:border md:border-light-4 rounded-xl p-4 flex items-center justify-center shadow-hover" n:href=":NewFront:Account:LastRedirections:default">
			<img class="max-h-[30px] max-w-[80px]" alt="{current($redirections)->getShop()}" src="{current($redirections)->getShop()->getCurrentLogo()|image:300}">
		</a>
		<a n:if="next($redirections) !== false"
			class="bg-white lg:bg-transparent w-full lg:max-w-[126px] h-[77px] md:border md:border-light-4 rounded-xl p-4 flex items-center justify-center shadow-hover" n:href=":NewFront:Account:LastRedirections:default">
			<img class="max-h-[30px] max-w-[80px]" alt="{current($redirections)->getShop()}" src="{current($redirections)->getShop()->getCurrentLogo()|image:300}">
		</a>
		<!-- Tento div bude pod tromi na mobile a vedľa na lg -->
		<div n:if="isset($redirections) && count($redirections) > 3"
			class="bg-white lg:bg-light-6 col-span-3 lg:col-span-1 bg-light-6 rounded-xl p-4 flex items-center justify-center">
			<a class="text-sm text-center text-dark-1 leading-[24.5px] hover:cursor-pointer hover:underline" n:href=":NewFront:Account:LastRedirections:default">
				{_'newFront.account.transactions.box1.morePlural', [amount => (count($redirections) - 3)]|noescape}
			</a>
		</div>
	</div>

	<a n:href=":NewFront:Account:Refund:default#missing-reward"
	   class="w-full lg:w-auto inline-flex items-center justify-center gap-[11px] text-base font-bold text-white leading-7 bg-orange-gradient pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
		{_'newFront.account.transactions.box1.button'}
		<svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
			<path d="M4.19576 15.0328L14.0938 5.13477" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
		</svg>
	</a>

	<div class="hidden lg:block absolute top-[39px] right-[-8px]">
		<div class="relative w-[129px] h-[129px]">
			<svg xmlns="http://www.w3.org/2000/svg" width="123" height="122" viewBox="0 0 123 122" fill="none">
				<path d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z" fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571"/>
				<defs>
					<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126" y2="102.866" gradientUnits="userSpaceOnUse">
						<stop stop-color="#FDBB47"/>
						<stop offset="0.495" stop-color="white"/>
						<stop offset="1" stop-color="#FDBB47"/>
					</linearGradient>
				</defs>
			</svg>
			<div
				class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-sm font-bold text-white">
				{_'newFront.account.transactions.box1.guarantee'}
			</div>
		</div>
	</div>

	<img class="hidden lg:block absolute right-[-156px] top-[-8px]"
		 src="{$basePath}/new-design/tipli-oslik-redirect-1.png" alt="oslik">
</div>

<!-- 2. Boxík -->
<div n:if="!$hasTransaction && !$hasRedirection" class="p-5 lg:p-10 lg:bg-white rounded-2xl mb-10 relative">
	<div
		class="flex items-center gap-[13px] text-[30px] lg:text-[30px] leading-[32px] lg:leading-[37px] font-bold mb-[15px] text-primary-orange">
		<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
			<rect width="30" height="30" fill="url(#pattern0_1468_5381)"/>
			<defs>
				<pattern id="pattern0_1468_5381" patternContentUnits="objectBoundingBox" width="1" height="1">
					<use xlink:href="#image0_1468_5381" transform="scale(0.0138889)"/>
				</pattern>
				<image id="image0_1468_5381" width="72" height="72" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAC+lBMVEUAAADgx8fZsbLkoqPaUVninp/bVV1WVlaJFBW5m5vqh4q7T1N9FBW0WFnTXl+8ZWXFcHDlhYbhjo7W2t+mP0CcPj/Mbm3YkI+4wszfkpKoq7HQaGrgZ23Rhobel5dxcHGjR0e4X1/UdHPQe3rpgofgf3+yt7ywcnOxw9bdXWS9XmDLgoGpbm6kc3S3R0rYaWqou83cdnXwgYajgXuulJVxZWi8OEPHZWPCaWrScHDLf36PUlKrSUnrdHmcOj3idnzLd3ezW1u5x9TAYF2BcGx+Hh3id3qYPz+HhX+dj4mJYGDPWFtZVFW0v9BZWlrAy9VdYGGgQELBytLCQ0itU1LY3+XUV1y9xMWZg4SkkZPO2eHU4Op7d3NWWFnn6Ojm5ubk5ebk4+Ti4uLg4OHd3t/a2trc3NzV1dabHh/m6OjS0tO+vb3Ozs+lICHCwsOSMS9/JCSvIiPSUleDKyvlam3p6+zLy8vVSFDROkSRFxjt8PKpW1zIQEmvOz6OKCbhZWqrT0/FPESZODfY19i1UlLYTFLNSVHMNT+mLCzk3t7kyMjf1cPZXWPlX2DMRE3GPz23PDyvLS2iGBje0r7kqanngYJhXl6/OjuEHRybFRWHDw/moKHmcHLfW2LVQku5LS2IJSTk2Nnk09PmlZXleHjQTUaIMDDHxsfLwcK4tbXisK+4rK63j5CvgoTKVVvBP07QP0nDOUi8REfENEHHMj2ONTabLix7HyCUDQ3o4ODIycnCrq+zYWLGTlCvRUa4zN/lt7m3paaEfn/pZmnNPzrFMjG2HiDmzM3lwMDnjIzaiofYeXXwa2/ZXFriVVnFSUVyGBjcx8fPkpGIiImjgYf4goXehH+5dHWjaWu7LzW+IySkoqayoKF3eHr1dHnbamynODaqFBXc0dLWw77Ht7jYp6acm5yQjI7AgoH1+PjhoaP5k5RtXWJaOjuTKSeWKCeni41lZWXZtrf/pqbTgnl/bHC4aGhsT0+JT1ZQKirqyMiyo46SeVn/uba5leTDAAAAXnRSTlMACA8X/iD9/v78VP7+/fjhpGJE/v720zw4M/z7yU8o/v3xs5N0cBj7+OrQfSr8+9qxo5j+/f3858PBV1Lx4sO2bqR7+9/Zzp6ATDvr5NzLk3t3Ut7c1KTyzMWupJuVVYLR7AAADL1JREFUWMOF12dYU1cYB3D23oIyBHEjrtZVt9YOW7v3zt47IQQjBEIYggIByt57g4iCbNl7iaLgAveerdr5PH3vvSEBQfv/oNdw7u++5z3n3IjWtGjr21gvWWJto6v+RGfPG2j26Kg/0p2HjtHXnnan5hJGmLw5f4WBnp7BivlL9FWf2c85icbNXvWB/pLJMW+awPNezjJ7ex2T+alBafv3H4SUi+abYM+Yt+HxGSTrP8QebDJfL20/DNq/v9zLYL61jr39smnOT26Ou1YgDAIdP378WMRHzuhPVu10tXO1s7PbuQr9p80Kr3IYgz3OJ9hi1ydub0yV7L8r6EWelOblBQMPArTJfZ4WJm3b9sP327aBg8TBvbw8zSsoyCsNHeUj6i12/HIKtOrd8aA0GKFnYGAgivAHB61A18Fkybp1u4yNd61bt8TEQVcbRn4bpGeQmppq4FWOSEeDLVbbT4FMYFrApPr5+YWJgn2ObXpbV8vFZN37O8R+qal6JaVwr594x/vrrF20db9tbfUTi/0MgkE6BtIKE43jsFbPyytoINxPnCIG6OimL/RN1u0Qp+p5RUT4+PiXrvaH+ESIwsZ3rDPRX7rjckqKXxhUfgygI2VrbdTLvssgCJjWvhR0gM9Xu623WIh84Nbg4GDoWllZWgSI/vFj8f5lTluWgiRGK/cHCKRdk9tgaZie3kBr3+XLfWLkQV9ZbTaNjBJG+QdDV6FrsGv0gmARyiN8xq5Ec2WmmxevbcWgo+AcaXBaoipovoFeeN/IyAhAYSL/Ne7bQxjcqLGDaXqpqWFlDaaBEFPThuDgiPKD7cLo6sSq7e4f6RmIgiN8wIGUzdXHOm1hkNo38ttv9y73pYrGqmwVDEZIensaIKbey0uNi+sgxcalq73XVPjsbz8vTKyWKlxl/hEA+QBTUVFhYY1u1jdFiPMvQOERwsqNlVJG1PmDBmGm3qXFtXQKmUTuaqKQKTR6fnGpd6P/8fMd3OqkvBbvqOP+sBD+8ZGRkQ2LtJHDsza4tS88Z2SkNWiMMfTi0v1HwnYvUeDoRQmFRCAQiURSUUwokUQikylz8i+ONsaDJH3m+GwoUZiubFMq45VK0Vpkbg4fKQeC2pVKZXsHV/DngwV/CNtFpqPNLDJBFWJMcigKkoDiNI/KxjqujN568KcHI1rY3tamzM3NtUCOgXNDeroQCdduO//PB4qOtmDvDQVXSZiCx+OJydmheLjEqGvXN8hBqnzwoHK7XXV0enp6fFuuBbIpt67hRiOR7nSfkCY87ChvGGWTs5o6AUGCw+Gzu0OZcIXOs/PGNXrhqKzjCt8sSeG+00gqZYSETARuRV4U2xnSxMRqo71WtonV3PNppus5TCapKDkTUSAAxdagfwMVevfuVcoc3nrbjiuMpERbq71mZgmPqqq22yDN/trXyChJ8MXuNQxwggIfz/FkMpmHuouAwaBugJCA3BTbRIROSR7bpl+B4YErv3gv4eEj/tcuyPK/nWc29N62eW+FgDNg+pgCDtKY2E41lAMQmqzY7kwi2nNMCnnLZtt7Gb6Kt9EX8Va7off26SyqSIw+P1B2huLpiTya2ZmTTcRppoYlWdzEJGDSGVk67PI1i3T37fS124q939/eu0rL2kKYeH4gbPWcA+BA4KbWJtXtmVmqi0Pi7ho8HpNqV0eOCRO5Tku1Vu2Flw4m6Wjpz2+40jGQ4i05gEHINFKgkOnpbj2Ew6MSiTKH7b1pTBht+r4Lcr86Sy180r1GzJvVDqQpBv8S1FSExxaPxCtkzbnYeDReKIOjrwkUJAoTXR5fXYNBuNcGj6M5nj7NCyhYvelIhazsfX2NAx0KE/vdMy+o+auzhon7v0hOL2i58PTGjTr50YY1a6BL6ugs6hf7jfSWHsD9lZzcVfN6huS44EnLhuGeu6fopZuOmMrKFmla5LCiP8fvXn3BnTu4ruyc7EOvc1i34p603O49W5RJoRTK4ysiK5zmaVrdL06J7V7ueQc6lBmTIs7OfKXDXmD4ZNRpPLsTjjCFtTweJIuVk472ov4c8T2nZsSBdHaLu3A4CmWm4km7GWd46aLT2S4iHjYTLcA4Mn4wUjM3/bX9KeJ79VdRCFLTWYOjnL7EngHVthjG3eLRD4UyCeiupPLkyoaKhrUuKsjGot8vJXY5Dhz1ylPhnptUgDUhOj4xXHCBQyUy8QQMosHcBgcHnZzVu7G/v2+8BBymGiLX3oJZsKdMi37L0LCFTacQkL2NSWRaSbxyUBk22aTFoiBRa1ndNIhI4zguUBWFx6u6fLOWSsLj8GjgLUcmk+silbnK/sWAYF8jubnhpgXq44qGQKazT0NRPDwu6zCOAl1eUMyiIS8EzAEI5na9sS13sH+RNrYdtzQoc4PMC1TPUp8EIpVzAYoq9Dx8GHfTMA4OBRmG4DAGHFg2aoG8LTdXtAU7/bpzB+OVbeb5FBKRgGdC1EVRkKIu8Q7/6njpEtJlvKocZBScXGg3xxyghrn6KigyPb7du+BpUVFMTExychNRXRSJymE7tqxff5otgS4jn2DpSk6+ezcmpqjoWj1MbVADRUWle9f1nD2bkyJuDY8NVZUE0yBQqNTCf/4ppFGQciYlYnc48l+gnLM9N+rbBuMr1JBMCFDBqVOH0GRNeY8ARWL9/TcLZfDqZKIDT0Hq2yMjGwHCmi3jRnXIC2hk+FJkQjQtR28nX7tGRrqjCbQS2o3sSIl5u2yica6u6qg1ckOEjdepIBGJ2Pfi1KIIv/5K0CBTFo1CDSgITJ8Iadyio9qQCCQrDqBRyBoIqMmyDs8OwerT6yqiJkLkb2pj0MpARgg3yphOpUBJBI2k2lgaSONg2zGAbhwZMlEVCDsbzVZzgISlLJgbBqkzK0RQz4zOuR1Vpah6x3ryBWlZxWAIvfPVc2NOL2A6hPx4cmb5z4VVVYq3Jl+ROpttGVXRsmI6lQYQHt8Zk/VKCB6TGdNFIKAFserWMBQPbdETgnVbLq1KjCrlBNDIOHxmsji8i/kaqDM8PDuLCAWxJCVRDIVv4GLtSch54cNHMDcei0oJbYoNz2kizlKRpj9dseEpMQE0Oof3PPphFf8dE80vapsV0kfRMmMO/VRyTk5MJrLdZoMIWJihRTl+PU9ZEuNIqcLXFva1Oivl0kdSYT37Rk9PTJZq+EyIoAosSNbd3t7h6+e4vnxfbPHV68aX+ibKSoaHr6m2NwS9UwVhyNSlvzZ8uyRSyvdF1kwT7cWuRg+lXO9iCZ06KeFJoVgBABGxi9BQvHoLsfLr3onO4Geg21qTeZZ8I9/qqPU8jloiZsP7ZAqEfTJ5yFi17NtRRkhBNiigKUme4JuQKNvAw2qCHd4VHkucrEjVteyBGILaMa6Q+vIzAqGgaXFxV5j5GkXLL6ISHLrQ2PBDeKyQohskTMoSi7MmnWbzxARFhu1aB62XsnuhR0JGEte7mSdhBVAppKLwbCIS2MRnezPx2HXMQDJ6xPLZzfVcI34G/52lWi9Hx0p+ApUusmtZdOrVnJwsIgm9PRQg7FcIUubZ8VOY85ybxPeFielozYjLZruhjARUyufQb4wXwfcfCYJCyAUZ8nR8mCXhFV6sD0mCBrnOdZnBoCunGBKcSOLKNxTyrv/ecxVaRUEwWk9vFqJQKHDeh53q2IUl5lxwBLawYrNmq2XlHyWV8JvB+mLj29dZ9AAqlQYYref3q/AnjQqbh3P93O3m0kZGEv/MH4q34D30Cmk5j3BGYFYd4l1qzM6XcMACLAAg+JMOjITHNi6tj5Im5K12c1xurf0qyP6bA+z7L/LMkhInRmF+QHFYLDpr+HcOnQVKLY9daLxcxjDKqHyx8YLbJ/avcpb9eIDzLO7FkEBglsSQ1ZcUs3n5tRKOZPh2gURSm89jF5c8b+QaJfD5ggeGzxxPvrFsdkf7swOUlrh3vSuHTnggVIh8eUlzIZvHMy7hQS3NJecCJxhGCbBaAoX3x4Ytbic/05kV2uPpeSvu3Q93W7p6oJSZESNEFlh/Dk19oCyEIU0AxlfAl1uu3Pdp3C03tz2zdelLwoEL4MA7xWqhnQAoD8EJwKQMNFKjhASBBx+q4bsutII3x76P79886fblbI2+U3x/ATgwR2erha55J04IPPJAE5yAK4GHR15lnocggy9fuMgZndG2jzdeODlLw/fAgm38QFWqjo2VpbldngDBgMgD0AOwSldzSytgsHxuCNJnM6APTm8ER9N5l91gye0qKwFBylG4ykHZ7TJlyOeG91tWzlz7Xz79QGf6Kuo7r7Ta7G65EGLpvtlqpbNGwer+/NOfl2mGqy+WYZfTMV19BxtnZxsHfd1pP515y3/azmW35li5jAAAAABJRU5ErkJggg=="/>
			</defs>
		</svg>
		{_'newFront.account.transactions.box2.title'}
	</div>
	<div class="text-sm leading-[24.5px] text-dark-1 mb-[14px] md:mb-5">
		<div class="font-medium lg:font-bold">
			{_'newFront.account.transactions.box2.descriptionBold'}
		</div>
		<div class="w-full max-w-[554px] md:mb-[30px]">
			{_'newFront.account.transactions.box2.description'}
		</div>

		<div class="md:hidden w-full h-px bg-light-4 my-5"></div>

		<div class="font-medium md:font-normal leading-[24.5px] w-[187px] md:w-auto md:italic">
			{_'newFront.account.transactions.box2.descriptionItalic'}
		</div>
	</div>

	<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2.5 md:gap-5 mb-5">
		{var tipli\Model\Shops\Entities\Shop[] $topShops = $getTopShops()}
		{foreach $topShops as $shop}
			<a
				class="hidden md:flex flex-col items-center justify-center rounded-xl cursor-pointer shadow-hover p-2 border border-bg-light-4" href="{plink :NewFront:Shops:Shop:default $shop}">
				<div class="w-full h-[92px] px-4 py-4 flex items-center justify-center">
					<img class="max-w-[100px] max-h-[60px] w-full" loading="lazy" alt="{$shop->getName()}" src="{$shop->getCurrentLogo()|image:300}">
				</div>
				<img class="m-auto" src="/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">

				<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
					{($shop|reward:true, 'extended')|noescape}
				</div>
			</a>

			<a href="{plink :NewFront:Shops:Shop:default $shop}" class="md:hidden group flex gap-4 items-center">
				<div
					class="border border-light-5 bg-white rounded-xl flex justify-center items-center w-[97px] h-[55px]">
					<img class="max-w-[58px] max-h-[38px]" alt="{$shop->getName()}" src="{$shop->getCurrentLogo()|image:300}" loading="lazy">
				</div>

				<div class="text-sm text-dark-1 leading-[18.75px]">
					<div class="leading-[24.5px] mb-1.5 line-clamp-1">{$shop->getName()}</div>
					<div class="similar-shop__value">
						{($shop|reward:true, 'extended')|noescape}
					</div>
				</div>
			</a>
		{/foreach}
	</div>

	<div class="flex flex-col md:flex-row items-center gap-2.5 md:gap-[19px]">
		<a n:href=":NewFront:Shops:Shops:default" class="w-full inline-flex items-center justify-center gap-[11px] text-sm md:text-base font-bold text-white leading-7 bg-orange-gradient pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
			{_'newFront.account.transactions.box2.table.buttonShops'}
		</a>
		<a n:href=":NewFront:Account:Refund:default"
		   class="w-full inline-flex items-center justify-center gap-[11px] text-sm md:text-base font-bold text-dark-1 md:text-primary-orange leading-7 border border-bg-light-4 md:border-none md:bg-pastel-orange-light pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-primary-orange/15">
			{_'newFront.account.transactions.box2.table.buttonHelp'}
		</a>
	</div>

	<div class="hidden lg:block absolute top-[39px] right-[127px]">
		<div class="relative w-[129px] h-[129px]">
			<svg xmlns="http://www.w3.org/2000/svg" width="123" height="122" viewBox="0 0 123 122" fill="none">
				<path d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z" fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571"/>
				<defs>
					<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126" y2="102.866" gradientUnits="userSpaceOnUse">
						<stop stop-color="#FDBB47"/>
						<stop offset="0.495" stop-color="white"/>
						<stop offset="1" stop-color="#FDBB47"/>
					</linearGradient>
				</defs>
			</svg>
			<div
				class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-sm font-bold text-white">
				{_'newFront.account.transactions.box2.guarantee'}
			</div>
		</div>
	</div>

	<img class="hidden lg:block absolute right-[25px] top-[-10px] w-[143px] h-[227px]"
		 src="{$basePath}/new-design/tipli-oslik-redirect-1.png" alt="oslik">
</div>

<div n:if="$showAddonPromo && $hasRedirection" class="hidden md:flex flex-col xl:flex-row items-center justify-between rounded-2xl bg-white px-5 xl:pl-[41px] py-[43px] xl:pr-[27px] relative mb-10">
    <div class="text-dark-1 leading-[37px] text-[24px] font-bold mb-10 xl:mb-0">
        {_newFront.account.addonPromo.title |noescape}
        <div class="text-sm leading-[26px] font-normal mt-[19px] mb-[22px] max-w-[344px]">
            {_newFront.account.addonPromo.text, ['count' => ($countOfCashbackShops |amount)] |noescape}
        </div>

        <a n:href=":NewFront:Static:addon" class="inline-flex items-center gap-[11px] text-base font-bold text-white leading-7 bg-orange-gradient pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
            {_newFront.account.addonPromo.cta}
            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
                <path d="M4.19576 15.0328L14.0938 5.13477" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </a>
    </div>
    <div class="relative">
        <img class="w-[383px] xl:mt-[-7px]" src="/new-design/01_notebook.png" alt="pocitac">
        <div class="absolute top-[-4px] xl:top-[26px] lg:top-[26px] left-[-2px] lg:left-[-42px] w-[126px] bg-white p-2.5 rounded-[10px] rotate-3 shadow-lg">
            <div class="flex justify-between">
				{if $localization->isHungarian()}
					<img src="{$basePath}/images/tiplino_logo_new_color.svg" width="30px" title="Tipli" alt="Tiplino" loading="lazy" class="max-w-[50px] max-h-[40px]">
				{else}
                <svg width="30" height="14" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
                </svg>
				{/if}
                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                    <path d="M1.17053 9.0205L9.32664 1.80457M8.85656 9.4906L1.64064 1.33447" stroke="#80899C" stroke-width="0.888507" stroke-linecap="round"></path>
                </svg>
            </div>

            <div class="text-center bg-light-6 py-[14px] my-[7px] rounded-[10px]">
                <div class="text-[38px] font-black leading-[40x] text-secondary-green tracking-[-5px]">10 %</div>
                <div class="text-[9px] leading-[18px] text-dark-1">
                    {_newFront.addon.sampleBox.reward}
                </div>
            </div>

            <button class="w-full text-white text-[8px] font-bold leading-[20px] py-[7px] bg-orange-gradient rounded mb-5 mb-[9px]">
                {_newFront.addon.sampleBox.cta}
            </button>

            <div class="text-center text-[6px] leading-[16px] underline text-dark-2">
                {_newFront.addon.sampleBox.coupons}
            </div>
        </div>

        <div class="absolute top-[-24px] right-[-2px]">
            <div class="relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="110" height="108" viewBox="0 0 110 108" fill="none">
                    <path d="M50.5387 2.11742C53.0584 -0.208009 56.9416 -0.208008 59.4613 2.11743L65.7748 7.94442C67.283 9.33637 69.3545 9.9446 71.3758 9.589L79.8374 8.10041C83.2143 7.50634 86.4811 9.6058 87.3435 12.9243L89.5045 21.2396C90.0208 23.226 91.4345 24.8576 93.3272 25.6513L101.25 28.9737C104.412 30.2996 106.026 33.8319 104.957 37.0899L102.279 45.2535C101.64 47.2037 101.947 49.3406 103.11 51.0315L107.979 58.1101C109.922 60.935 109.37 64.7788 106.709 66.9418L100.043 72.3619C98.4508 73.6566 97.5539 75.6204 97.6183 77.6718L97.8875 86.2591C97.9949 89.6862 95.4519 92.6209 92.0445 93.0023L83.5062 93.9579C81.4666 94.1862 79.6504 95.3534 78.5955 97.1139L74.1793 104.484C72.4169 107.425 68.6909 108.519 65.6182 106.997L57.9187 103.185C56.0795 102.274 53.9205 102.274 52.0813 103.185L44.3818 106.997C41.3091 108.519 37.5831 107.425 35.8207 104.484L31.4045 97.1139C30.3496 95.3534 28.5334 94.1862 26.4938 93.9579L17.9555 93.0023C14.5481 92.6209 12.0051 89.6862 12.1125 86.2591L12.3817 77.6718C12.4461 75.6204 11.5492 73.6566 9.95679 72.3619L3.2906 66.9418C0.630253 64.7788 0.0776076 60.935 2.02079 58.1101L6.88995 51.0315C8.0531 49.3406 8.36034 47.2037 7.7207 45.2535L5.04305 37.0899C3.97445 33.8319 5.58762 30.2996 8.7496 28.9737L16.6728 25.6513C18.5655 24.8576 19.9792 23.226 20.4955 21.2396L22.6565 12.9243C23.5189 9.60579 26.7857 7.50634 30.1626 8.10041L38.6242 9.589C40.6455 9.9446 42.717 9.33637 44.2252 7.94441L50.5387 2.11742Z" fill="url(#paint0_linear_830_2909)"/>
                    <defs>
                        <linearGradient id="paint0_linear_830_2909" x1="65" y1="38.5" x2="55" y2="59" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#FEF3E9"/>
                            <stop offset="1" stop-color="white"/>
                        </linearGradient>
                    </defs>
                </svg>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs text-center font-bold text-primary-orange">
                    {_newFront.addon.tier1.smallTitle |noescape}
                </div>
            </div>
        </div>
    </div>

    <img class="absolute right-[-44px] bottom-[-16px]" src="{$basePath}/new-design/donkey-rewards.png" alt="oslík">
</div>

<div class="flex justify-between items-center text-dark-1 text-lg font-medium leading-[31.5px] mb-5 pl-5 md:pl-0">
	{_'front.account.transactionsList.title'}
	<a n:href=":NewFront:Account:Refund:default#missing-reward" class="md:hidden text-xs leading-[21px] text-dark-4 underline hover:cursor-pointer text-primary-orange">
		{_'newFront.account.transactionsList.filter.report'}
	</a>
</div>

<div class="flex justify-between items-center mb-5" n:snippet="filters">
	<div class="flex gap-[15px] overflow-x-auto">
		<a n:href=":NewFront:Account:Transaction:default, filter => null" n:class="$filter === null ? 'bg-white', 'flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white'">
			<svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
				<path d="M1.01059 8.5L8.02364 11.8853C8.16947 11.9607 8.33115 12 8.49531 12C8.65939 12 8.82114 11.9607 8.96697 11.8853L16 8.5M1.01059 11.5L8.02364 14.8853C8.16947 14.9607 8.33115 15 8.49531 15C8.65939 15 8.82114 14.9607 8.96697 14.8853L16 11.5M15.7682 4.63535L8.96631 1.11468C8.82048 1.03932 8.65872 1 8.49464 1C8.33049 1 8.1688 1.03932 8.02297 1.11468L1.22245 4.63535C1.15545 4.6698 1.09925 4.72206 1.06 4.78639C1.02076 4.85073 1 4.92464 1 5.00001C1 5.07539 1.02076 5.1493 1.06 5.21363C1.09925 5.27797 1.15545 5.33023 1.22245 5.36468L8.02364 8.88533C8.16947 8.96073 8.33115 9 8.49531 9C8.65939 9 8.82114 8.96073 8.96697 8.88533L15.7682 5.36468C15.835 5.33007 15.8909 5.27774 15.9301 5.21343C15.9692 5.14911 15.9898 5.07529 15.9898 5.00001C15.9898 4.92474 15.9692 4.85091 15.9301 4.7866C15.8909 4.72229 15.835 4.66997 15.7682 4.63535Z"
					  stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			{_'front.account.transactionsList.filter.all'}
		</a>
		<a n:href=":NewFront:Account:Transaction:default, filter => 'waiting'" n:class="$filter === 'waiting' ? 'bg-white', 'flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white'">
			<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
				<path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z"
					  stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			{_'front.account.transactionsList.filter.wait'}
		</a>
		<a n:href=":NewFront:Account:Transaction:default, filter => 'confirmed'" n:class="$filter === 'confirmed' ? 'bg-white', 'flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white'">
			<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
				<path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z"
					  stroke="#080B10" stroke-linecap="round" />
			</svg>
			{_'front.account.transactionsList.filter.confirm'}
		</a>
	</div>

	<a n:href=":NewFront:Account:Refund:default#missing-reward" class="hidden md:block text-sm leading-[24.5px] text-dark-1 underline hover:cursor-pointer hover:no-underline text-primary-orange">
		{_'newFront.account.transactionsList.filter.report'}
	</a>
</div>

{snippetArea transactionsWrapper}
	{include 'snippets/transactions.latte'}
{/snippetArea}

{snippetArea shareRewardsWrapper}
	{include 'snippets/shareRewards.latte'}
{/snippetArea}

<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/animations/scale.css" />
<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/border.css" />

<script>
	tippy('#confirmedBalance-tooltip', {
		animation: 'scale',
		content: {_front.account.transaction.default.confirmedBalance.tooltip},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#registeredCommissionBalance-tooltip', {
		animation: 'scale',
		content: {_front.account.transaction.default.registeredCommissionBalance.tooltip},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#registeredBonusBalance-tooltip', {
		animation: 'scale',
		content: {_front.account.transaction.default.registeredBonusBalance.tooltip},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

</script>

{*
<div class="md:hidden">
    <div class="flex justify-between bg-white rounded-xl p-5 mb-5">
        <div>
            <div class="text-lg leading-[31.5px] font-bold text-dark-1">
                Decatlon
            </div>
            <div class="text-xs leading-[21px] text-dark-1">29.04.2024</div>
        </div>
        <div>
            <div class="flex items-center bg-pastel-green-light rounded-[14.5px] pl-2.5 pr-[7px] gap-2.5 mb-[3px] text-sm font-bold leading-[24.5px] text-secondary-green">
                3,47 €
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z"
                          stroke="#66B940" stroke-width="1.5" stroke-linecap="round" />
                </svg>
            </div>
            <div class="flex items-center gap-[5px] text-xs leading-[21px] text-dark-4">
                Zobraziť detail
                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
                    <path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656"
                          stroke="#ADB3BF" />
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-5 mb-5">
        <div class="flex justify-between">
            <div>
                <div class="text-lg leading-[31.5px] font-bold text-dark-1">
                    Decatlon
                </div>
                <div class="text-xs leading-[21px] text-dark-1">29.04.2024</div>
            </div>
            <div>
                <div class="flex items-center bg-pastel-green-light rounded-[14.5px] pl-2.5 pr-[7px] gap-2.5 mb-[3px] text-sm font-bold leading-[24.5px] text-secondary-green">
                    3,47 €
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z"
                              stroke="#66B940" stroke-width="1.5" stroke-linecap="round" />
                    </svg>
                </div>
                <div class="flex items-center gap-[5px] text-xs leading-[21px] text-dark-4">
                    Zatvoriť detail
                    <svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
                        <path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656"
                              stroke="#ADB3BF" />
                    </svg>
                </div>
            </div>
        </div>
        <div class="w-full h-px bg-light-5 my-5 mb-3"></div>
        <div>
            <div class="flex justify-between mb-2">
                <div class="text-xs leading-[21px] text-dark-1">Stav</div>
                <div class="text-sm leading-[24.5px] font-medium text-primary-orange">Čaká na potvrdenie obchodníkom
                </div>
            </div>
            <div class="flex justify-between mb-2">
                <div class="text-xs leading-[21px] text-dark-1">Hodnota objednávky</div>
                <div class="flex items-center gap-[5px] text-sm leading-[24.5px] text-dark-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
                              stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
                              stroke-linejoin="round" />
                        <circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF" />
                    </svg>
                    47,5 € (2%)
                </div>
            </div>
            <div class="flex justify-between">
                <div class="text-xs leading-[21px] text-dark-1">Odhad potvrdenia</div>
                <div class="text-sm leading-[24.5px] text-dark-1">47,5 € (2%)</div>
            </div>
        </div>
    </div>

    <div class="flex justify-between bg-white rounded-xl p-5 mb-5">
        <div>
            <div class="text-lg leading-[31.5px] font-bold text-dark-1">
                Decatlon
            </div>
            <div class="text-xs leading-[21px] text-dark-1">29.04.2024</div>
        </div>
        <div>
            <div class="flex items-center bg-[#FFEBED] rounded-[14.5px] pl-2.5 pr-[7px] gap-2.5 mb-[3px] text-sm font-bold leading-[24.5px] text-secondary-red">
                3,47 €
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.48965 10.5199L8.01471 7.99995M8.01471 7.99995L10.5398 5.47993M8.01471 7.99995L10.5398 10.5199M8.01471 7.99995L5.48961 5.47993M1 8C1 9.85653 1.73899 11.637 3.0544 12.9497C4.36981 14.2625 6.15389 15 8.01416 15C9.87445 15 11.6585 14.2625 12.9739 12.9497C15.6754 10.2539 15.6753 5.74614 12.9739 3.05025C11.6585 1.7375 9.87445 1 8.01416 1C6.15389 1 4.36981 1.7375 3.0544 3.05025C1.73899 4.36301 1 6.14349 1 8Z"
                          stroke="#F72F49" stroke-width="1.5" stroke-linecap="round" />
                </svg>
            </div>
            <div class="flex items-center gap-[5px] text-xs leading-[21px] text-dark-4">
                Zobraziť detail
                <svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
                    <path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656"
                          stroke="#ADB3BF" />
                </svg>
            </div>
        </div>
    </div>

    <div class="text-center mt-5">
        <button class="w-full leading-[24.5px] text-sm font-medium text-dark-1 pt-3 pb-[11px] border border-light-2 rounded-xl xl:hover:bg-light-4">
            Ďalšie položky
        </button>
    </div>
</div>
*}
