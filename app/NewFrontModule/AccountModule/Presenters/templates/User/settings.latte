{block title}{_'front.account.user.settings.title'}{/block}
{block heading}{_'front.account.user.settings.title'}{/block}

{block scripts}

<script>
	window.addEventListener('DOMContentLoaded', function() {
		document.querySelectorAll('.js-scroll-animate').forEach(function(element) {
			element.addEventListener('click', function() {
				// Získání ID cílového prvku z atributu data-section
				const sectionId = element.getAttribute('data-section');
				const targetElement = document.getElementById(sectionId);

				if (targetElement) {
					const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - 100;
					const startPosition = window.scrollY;
					const distance = targetPosition - startPosition;
					const duration = 1000;
					let startTime = null;

					function animateScroll(currentTime) {
						if (startTime === null) startTime = currentTime;
						const timeElapsed = currentTime - startTime;
						const run = ease(timeElapsed, startPosition, distance, duration);
						window.scrollTo(0, run);
						if (timeElapsed < duration) requestAnimationFrame(animateScroll);
					}

					function ease(t, b, c, d) {
						t /= d / 2;
						if (t < 1) return c / 2 * t * t + b;
						t--;
						return -c / 2 * (t * (t - 2) - 1) + b;
					}

					requestAnimationFrame(animateScroll);
				}
			});
		});

	});
</script>
{/block}

{block innerContent}
<div class="pl-5 md:pl-0">
	<div class="md:hidden font-bold leading-[31.5px] text-lg text-dark-1 mb-2.5">{_'newFront.account.user.settings.title'}</div>
	<div
		class="flex flex-row overflow-x-scroll md:overflow-auto text-nowrap gap-[14px] md:gap-[26px] text-xs md:text-sm leading-[24.5px] font-bold md:text-white mt-2 mb-5">
		<button data-section="account" class="js-scroll-animate border-b-2 cursor-pointer border-black md:border-white pb-[5px] mb-[5px] md:mb-0">1. {_'newFront.account.user.settings.personalData'}</button>
		<button data-section="bank" class="js-scroll-animate border-b-2 cursor-pointer border-transparent xl:hover:border-white pb-[5px] mb-[5px] md:mb-0">2. {_'newFront.account.user.accountNumber.title'}</button>
		<button data-section="password" class="js-scroll-animate border-b-2 cursor-pointer border-transparent xl:hover:border-white pb-[5px] mb-[5px] md:mb-0">3. {_'newFront.account.user.changePassword.title'}</button>
	</div>
</div>

<div id="account" class="p-5 md:pb-10 rounded-2xl bg-white shadow-sm mb-5">
	<div class="md:text-lg font-medium leading-7 md:leading-[31.5px] text-dark-1 md:pl-5">
		<span class="text-dark-4">1.</span>
		{_'front.account.user.settings.personalData'}
	</div>
	<div class="w-full h-px bg-light-5 mt-[18px] md:mt-[19px] mb-[20px]"></div>

	<form n:name="userProfileControl-form" class="md:px-5 relative">
		{* @todo: NewFrontModule: stylování chybových hlášek *}
		<div class="alert alert-danger" n:foreach="$form->errors as $error">{$error |noescape}</div>

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<label n:name="firstName"
					class="block text-xs font-medium text-gray-700">{_'front.account.user.settings.form.firstName'}</label>
				<input n:name="firstName"
					class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
			<div class="w-full">
				<label n:name="lastName"
					class="block text-xs font-medium text-gray-700">{_'front.account.user.settings.form.lastName'}</label>
				<input n:name="lastName"
					class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
		</div>

		<div class="block text-xs font-medium text-gray-700 -mb-[18px]">
			{_'newFront.account.user.settings.form.gender'}
		</div>
		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="flex flex-wrap md:flex-nowrap gap-[10px] md:gap-[25px] w-full mt-5">
				{foreach $form[gender]->getItems() as $key => $label}
				<label n:name="gender:$key" class="flex w-full text-sm justify-start md:justify-between items-center border border-light-4 px-3 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:ring-1 focus:ring-primary-orange">
					<input n:name="gender:$key" n:attr="checked: $key === '' && $user->getIdentity()->getGender() === null" class="leading-[24.5px] checked:bg-primary-orange checked:hover:bg-primary-orange checked:active:bg-primary-orange checked:focus:bg-primary-orange focus:bg-primary-orange focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange" />

					<div class="flex items-center mr-2 md:mr-0">
						<span class="ml-2">{$label}</span>
					</div>

					{* @todo: NewFrontModule: ikony oranžové + šedé pro každé pohlavý *}
					{if $key === 'female'}
					<svg xmlns="http://www.w3.org/2000/svg" width="14" height="20" viewBox="0 0 14 20" fill="none">
						<path
							d="M7 13V19M7 13C5.40872 13 3.88258 12.3678 2.75736 11.2426C1.63214 10.1174 1 8.59128 1 7C1 5.4087 1.63214 3.88258 2.75736 2.75736C3.88258 1.63214 5.40872 1 7 1C8.59128 1 10.1174 1.63214 11.2426 2.75736C12.3678 3.88258 13 5.4087 13 7C13 8.59128 12.3678 10.1174 11.2426 11.2426C10.1174 12.3678 8.59128 13 7 13ZM4.6 16.6H9.4"
							stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
					{elseif $key === 'male'}
					<svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
						<path
							d="M9.94155 5.15764L14 1M14 1H10.2083M14 1V4.5M12.25 9.55554C12.25 12.5624 9.7316 15 6.625 15C3.5184 15 1 12.5624 1 9.55554C1 6.54864 3.5184 4.11107 6.625 4.11107C9.7316 4.11107 12.25 6.54864 12.25 9.55554Z"
							stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
					{/if}
				</label>
				{/foreach}

			</div>
			<div class="w-full">
				<label n:name="birthdate"
					class="block text-xs font-medium text-gray-700">{_'front.account.user.settings.form.birthdate'}</label>
				<input n:name="birthdate" type="date"
					class="account-settings__input datepicker leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
			</div>
		</div>

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<label n:name="phoneNumber"
					class="block text-xs font-medium text-gray-700">{_'front.account.user.settings.form.phoneNumber'}</label>
				<div class="relative flex items-center mt-1">
					<select n:name="phoneNumberCountryCode"
						class="absolute inset-y-0 top-[3px] left-0 pl-3 pr-7 text-sm bg-transparent border-none focus:ring-0 rounded-md"></select>
					<input n:name="phoneNumber"
						class="leading-[24.5px] text-sm pl-20 pr-3 pb-[11px] pt-3 block w-full bg-white border border-light-4 rounded-md focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				</div>
			</div>
		</div>


		<div class="block text-xs font-medium text-gray-700 mb-1">
			{_'front.account.user.settings.form.sendingPolicy.emailLabel'}</div>
		<div class="flex flex-col md:flex-row gap-5 mb-[25px]">
			<label n:name="emailNewsletter"
				class="flex text-sm items-center border leading-[24.5px] border-light-4 px-5 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				<div class="relative flex cursor-pointer items-center rounded-full mr-3"
					data-ripple-dark="true">
					<input n:name="emailNewsletter"
						class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10">
					<div
						class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20"
							fill="currentColor" stroke="currentColor" stroke-width="1">
							<path fill-rule="evenodd"
								d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
								clip-rule="evenodd"></path>
						</svg>
					</div>
				</div>
				{_'model.account.sendingPolicy.contentType.newsletter'}
			</label>
			<label n:name="emailNotification"
				class="flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				<div class="relative flex cursor-pointer items-center rounded-full mr-3"
					data-ripple-dark="true">
					<input n:name="emailNotification"
						class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10">
					<div
						class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20"
							fill="currentColor" stroke="currentColor" stroke-width="1">
							<path fill-rule="evenodd"
								d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
								clip-rule="evenodd"></path>
						</svg>
					</div>
				</div>
				{_'model.account.sendingPolicy.contentType.notification'}
			</label>

			<label n:name="emailAutoresponder"
				class="flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				<div class="relative flex cursor-pointer items-center rounded-full mr-3"
					data-ripple-dark="true">
					<input n:name="emailAutoresponder"
						class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10">
					<div
						class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20"
							fill="currentColor" stroke="currentColor" stroke-width="1">
							<path fill-rule="evenodd"
								d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
								clip-rule="evenodd"></path>
						</svg>
					</div>
				</div>
				{_'model.account.sendingPolicy.contentType.autoresponder'}
			</label>

			{if $user->getIdentity()->isAdmin() || $isThereLuckyShops}
				<label n:name="emailLucky_shop"
						class="flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
					<div class="relative flex cursor-pointer items-center rounded-full mr-3"
						 data-ripple-dark="true">
						<input n:name="emailLucky_shop"
								class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10">
						<div
								class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20"
								 fill="currentColor" stroke="currentColor" stroke-width="1">
								<path fill-rule="evenodd"
									  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									  clip-rule="evenodd"></path>
							</svg>
						</div>
					</div>
					{_'model.account.sendingPolicy.contentType.luckyShop'}
				</label>
			{/if}
		</div>

		<div class="block text-xs font-medium text-gray-700 mb-1">
			{_'front.account.user.settings.form.sendingPolicy.smsLabel'}</div>
		<label n:name="SMSNotification"
			class="inline-flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
			<div class="relative flex cursor-pointer items-center rounded-full mr-3"
				data-ripple-dark="true">
				<input n:name="SMSNotification"
					class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10">
				<div
					class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
					<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"
						stroke="currentColor" stroke-width="1">
						<path fill-rule="evenodd"
							d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
							clip-rule="evenodd"></path>
					</svg>
				</div>
			</div>
			{_'model.account.sendingPolicy.contentType.smsNotification'}
		</label>

		<input type="submit" n:name="submit"
			class="w-full md:w-auto mt-5 block sm:absolute bottom-[-20px] right-[20px] leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
	</form>
</div>
<div id="bank">
	<form n:name="accountNumberControl-form" class="ajax p-5 rounded-2xl bg-white shadow-sm mb-5">
		<div class="md:text-lg font-medium leading-7 md:leading-[31.5px] text-dark-1 md:pl-5">
			<span class="text-dark-4">2.</span>
			{_'front.account.user.accountNumber.title'}
		</div>

		<div n:snippet="accountNumberControlErrors">
			{var $form = $control['accountNumberControl-form']}
			<div class="bg-secondary-red text-white mt-3 rounded p-2 md:ml-5 md:mr-5" n:foreach="$form->errors as $error">
				{$error |noescape}

				<script>window.location = "#accountNumberForm";</script>
			</div>
		</div>

		<div class="w-full h-px bg-light-5 mt-[19px] mb-[20px]"></div>

		<div class="md:px-5">
			<div class="w-full mb-5 relative">
				{if $user->getIdentity()->isCzech()}
				<label n:name="accountNumber"
					class="flex flex-col sm:flex-row justify-between text-xs font-medium leading-[21px] text-gray-700">
					<span class="flex gap-[5px] items-center">
						{_'front.account.user.accountNumber.form.accountNumber'}
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
							<path
								d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
								stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
								stroke-linejoin="round" />
							<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10"
								stroke-linecap="round" stroke-linejoin="round" />
							<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF" />
						</svg>
					</span>
					<span class="hidden">{_'front.account.user.accountNumber.form.accountNumberTooltip'}</span>
					<span
						class="font-normal text-dark-2">{_'front.account.user.accountNumber.form.accountPlaceholder'}</span>
				</label>

				<input n:name="accountNumber"
					class="leading-[31.5px] text-lg mt-1 block w-full pl-5 pb-[11px] pt-3 bg-white border border-light-4 focus:border-transparent rounded-md focus:outline-none focus:ring-1 focus:ring-primary-orange">

					{if $account->getAccountNumber() !== null && $account->hasVerifiedAccountNumber() === false}
						<div class="text-xs md:absolute mt-2.5 md:mt-0 top-[33px] right-[10px] flex items-center justify-center gap-2.5 px-2.5 py-2.5 md:py-2 md:text-sm font-medium bg-pastel-orange-light text-primary-orange rounded-lg leading-[21px] md:leading-[24.5px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
								<path d="M9 11.9091V8.27273M9 6.09091H9.00879M17 9C17 13.4183 13.4183 17 9 17C4.58172 17 1 13.4183 1 9C1 4.58172 4.58172 1 9 1C13.4183 1 17 4.58172 17 9Z" stroke="#EF7F1A" stroke-width="1.16471" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							{_'newFront.account.user.settings.form.waiting'}
						</div>

						<div class="text-right" n:if="$resendAccountNumberVerificationEmailAllowed">
							<a n:href='resendAccountNumberVerificationEmail!' class="text-primary-orange text-xs hover:underline">
								{_newFront.account.user.accountNumber.resend}
							</a>
						</div>
					{elseif $account->hasVerifiedAccountNumber() === true}
						<div class="text-xs md:absolute mt-2.5 md:mt-0 top-[33px] right-[10px] flex items-center justify-center gap-2.5 px-2.5 py-2.5 md:py-2 md:text-sm font-medium bg-pastel-green-light text-secondary-green rounded-lg leading-[21px] md:leading-[24.5px]">
							<svg class="shrink-0" xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
								<circle cx="9.5" cy="9.92285" r="8.75" stroke="#66B940" stroke-width="1.5"></circle>
								<path d="M6.33594 10.4029L8.35657 12.2972L12.6693 8.33887" stroke="#66B940" stroke-width="1.5" stroke-linecap="round"></path>
							</svg>
							{_'newFront.account.user.settings.form.confirmed'}
						</div>
					{/if}
				{else}
				<label n:name="accountNumber"
					class="flex flex-col sm:flex-row justify-between text-xs font-medium leading-[21px] text-gray-700">
					<span class="flex gap-[5px] items-center">
						{_'front.account.user.accountNumber.form.iban'}
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
							<path
								d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
								stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
								stroke-linejoin="round" />
							<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10"
								stroke-linecap="round" stroke-linejoin="round" />
							<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF" />
						</svg>
					</span>
					<span class="hidden">{_'front.account.user.accountNumber.form.accountNumberTooltip'}</span>
					<span
						class="font-normal text-dark-2">{_'front.account.user.accountNumber.form.accountPlaceholder'}</span>
				</label>

				<input n:name="accountNumber"
					class="leading-[31.5px] text-lg mt-1 block w-full pl-5 pb-[11px] pt-3 bg-white border border-light-4 focus:border-transparent rounded-md focus:outline-none focus:ring-1 focus:ring-primary-orange">
					{if $account->getAccountNumber() !== null && $account->hasVerifiedAccountNumber() === false}
						<div class="text-xs md:absolute mt-2.5 md:mt-0 top-[33px] right-[10px] flex items-center justify-center gap-2.5 px-2.5 py-2.5 md:py-2 md:text-sm font-medium bg-pastel-orange-light text-primary-orange rounded-lg leading-[21px] md:leading-[24.5px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
								<path d="M9 11.9091V8.27273M9 6.09091H9.00879M17 9C17 13.4183 13.4183 17 9 17C4.58172 17 1 13.4183 1 9C1 4.58172 4.58172 1 9 1C13.4183 1 17 4.58172 17 9Z" stroke="#EF7F1A" stroke-width="1.16471" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							{_'newFront.account.user.settings.form.waiting'}
						</div>

						<div class="text-right" n:if="$resendAccountNumberVerificationEmailAllowed">
							<a n:href='resendAccountNumberVerificationEmail!' class="text-primary-orange text-xs hover:underline">
								{_newFront.account.user.accountNumber.resend}
							</a>
						</div>
					{elseif $account->hasVerifiedAccountNumber() === true}
						<div class="text-xs md:absolute mt-2.5 md:mt-0 top-[33px] right-[10px] flex items-center justify-center gap-2.5 px-2.5 py-2.5 md:py-2 md:text-sm font-medium bg-pastel-green-light text-secondary-green rounded-lg leading-[21px] md:leading-[24.5px]">
							<svg class="shrink-0" xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
								<circle cx="9.5" cy="9.92285" r="8.75" stroke="#66B940" stroke-width="1.5"></circle>
								<path d="M6.33594 10.4029L8.35657 12.2972L12.6693 8.33887" stroke="#66B940" stroke-width="1.5" stroke-linecap="round"></path>
							</svg>
							{_'newFront.account.user.settings.form.confirmed'}
						</div>
					{/if}
				{/if}
			</div>

			<div class="text-end">
				<input type="submit" n:name="submit"
					class="account-number-submit w-full md:w-auto leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
			</div>
		</div>
	</form>
</div>

<div id="password">
	<form n:name="changePasswordControl-form" class="ajax p-5 rounded-2xl bg-white shadow-sm mb-5">
		<div class="md:text-lg font-medium leading-7 md:leading-[31.5px] text-dark-1 md:pl-5">
			<span class="text-dark-4">3.</span>
			{_'front.account.user.changePassword.title'}
		</div>

		<div n:snippet="changePasswordControlErrors">
			{var $form = $control['changePasswordControl-form']}
			<div class="bg-secondary-red text-white mt-3 rounded p-2 md:ml-5 md:mr-5" n:foreach="$form->errors as $error">
				{$error |noescape}

				<script>window.location = "#accountNumberForm";</script>
			</div>
		</div>

		<div class="w-full h-px bg-light-5 mt-[18px] md:mt-[19px] mb-5"></div>

		<div class="grid grid-cols-1 gap-[25px] mb-5 sm:grid-cols-2 sm:grid-rows-2">
			<div n:if='$user->getIdentity()->hasFilledPassword()'>
				<label n:name="oldPassword"
					class="block text-xs font-medium">{_'front.account.user.changePassword.form.oldPassword'}</label>
				<input n:name="oldPassword"
					class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
			<div class="sm:row-start-2">
				<label n:name="newPassword"
					class="block text-xs font-medium">{_'front.account.user.changePassword.form.newPassword'}</label>
				<input n:name="newPassword"
					class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
			<div class="sm:row-start-2">
				<label n:name="newPasswordVerify"
					class="block text-xs font-medium">{_'front.account.user.changePassword.form.newPasswordVerify'}</label>
				<input n:name="newPasswordVerify"
					class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
		</div>

		<div class="text-end">
			<input type="submit" n:name="submit"
				class="w-full md:w-auto leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
		</div>
	</form>
</div>

<div class="flex justify-between items-center mb-5">
	<div class="flex gap-[15px] overflow-x-auto">
		<a href="{plink :NewFront:Account:TwoFactor:}" n:if="$user->getIdentity()->isAdmin()" class="flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white">
			{_'newFront.account.myApprovals.2fa'} »
		</a>
		<a href="{plink :NewFront:Account:Condition:}" class="flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white">
			{_'newFront.account.myApprovals.link'} »
		</a>
		<a href="{plink :NewFront:Account:User:accountDeletionRequest}" class="flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white">
			{_'newFront.account.accountDeletionRequest.link'} »
		</a>

		<div id="cancel-newsletter-modal" class="flex items-center shrink-0 md:shrink-1 text-xs md:text-sm font-medium leading-[21px] md:leading-[24.5px] text-dark-1 gap-2 border border-light-4 rounded-lg px-[14px] py-2 hover:bg-white">
			Zrušiť odber »
		</div>
	</div>
</div>

{if $unsubscribeContentType}
	<div id="cancel-newsletter-modal-overlay" class="fixed isolate z-[100] inset-0 bg-[rgba(24,43,74,0.70)] backdrop-blur-[3px] flex items-start justify-center pt-[272px] px-4">
		<div class="bg-pastel-orange-light text-center max-w-[454px] py-10 px-[55px] relative rounded-lg">
			<a n:href="this, unsubscribeContentType: null" id="cancel-newsletter-modal-close-btn" class="cursor-pointer absolute top-[-21px] right-[-32px]">
				<svg xmlns="http://www.w3.org/2000/svg" width="79" height="78" viewBox="0 0 79 78" fill="none">
					<g filter="url(#filter0_d_6_550)">
						<rect x="26.5" y="8" width="38" height="38" rx="19" fill="white"/>
					</g>
					<path d="M40.5 32L51.5 21M51.5 32L40.5 21" stroke="#80899C" stroke-width="1.5" stroke-linecap="round"/>
					<defs>
						<filter id="filter0_d_6_550" x="0.5" y="0" width="78" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix"/>
							<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
							<feOffset dx="-6" dy="12"/>
							<feGaussianBlur stdDeviation="10"/>
							<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_550"/>
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_550" result="shape"/>
						</filter>
					</defs>
				</svg>
			</a>
			<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="43" height="45" viewBox="0 0 43 45" fill="none">
				<path d="M37.0554 22.2034V9.89648" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M28.1666 1H5.94434V22.2034" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M28.167 1L37.0248 9.89655H28.167V4.7069" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M28.9074 28.5956L41.5 18.7931V44H1.5V18.7931L14.1726 28.6579" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M1.5 41.7758L21.5 27.6896L41.5 41.7758" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M37.0481 15.1633L41.4999 18.7931" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M1.5 18.7931L5.94445 15.1692" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M20.0188 20.307C20.0188 19.4915 20.6855 18.8242 21.5003 18.8242C22.3151 18.8242 22.9818 19.4915 22.9818 20.307" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M29.7608 17.3095C29.7608 16.438 29.0383 15.7138 28.1658 15.7138C27.2933 15.7138 26.5708 16.438 26.5708 17.3095C26.5708 18.1809 27.2933 18.9051 28.1658 18.9051C29.0383 18.9051 29.7608 18.1809 29.7608 17.3095Z" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
				<path d="M16.4293 17.3102C16.4293 16.4384 15.7064 15.7138 14.8335 15.7138C13.9606 15.7138 13.2378 16.4384 13.2378 17.3102C13.2378 18.1821 13.9606 18.9066 14.8335 18.9066C15.7064 18.9066 16.4293 18.1821 16.4293 17.3102Z" stroke="#EF7F1A" stroke-width="1.71" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
			<div class="mt-5 mb-[30px] text-base md:text-xl leading-[31.5px]">
				{_'newFront.account.user.settings.unsubscribeModal.text'}
				<span class="font-bold">
					{_'model.account.sendingPolicy.contentType.' . $unsubscribeContentType}
				</span>
			</div>
			<div class="text-sm md:text-lg leading-[31.5px] mb-5">{_'newFront.account.user.settings.unsubscribeModal.areYouSure'}</div>

			<a n:href="unSubscribeEmail! $unsubscribeContentType" class="w-full block leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[86px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
				{_'newFront.account.user.settings.unsubscribeModal.cta'}
			</a>
		</div>
	</div>
{/if}