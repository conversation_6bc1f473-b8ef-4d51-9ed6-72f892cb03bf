{block styles}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<style>
	.swiper,
	.swiper-coupon {
		width: 100%;
		height: 100%;
		margin-left: auto;
		margin-right: auto;
		position: relative;
		overflow: hidden;
		list-style: none;
		padding: 0;
		z-index: 1;
		display: block;
		visibility: hidden;
	}

	.swiper {
		visibility: visible;
	}

	.swiper-coupon.swiper-initialized {
		visibility: visible;
	}

	.swiper-slide {
		text-align: center;
		justify-content: center;
		align-items: center;
	}

	.swiper-button-prev {
		top: 41%;
		left: -79px;
	}

	.swiper-button-next {
		top: 41%;
		right: -79px;
	}

	.swiper-coupon-button-next {
		top: 46%;
		right: -80px;
	}

	.swiper-coupon-button-prev {
		top: 50%;
		left: -80px;
	}

	.swiper-button-next,
	.swiper-button-prev {
		display: none;
		width: 60px;
		height: 60px;
		padding: 8px;
		border-radius: 16px;
		background: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
	}

	.swiper-button-next:after,
	.swiper-button-prev:after {
		font-weight: bold;
		font-size: 20px;
		color: white;
	}

	.swiper-pagination.swiper-pagination-bullets {
		display: none;
	}

	.swiper-pagination-bullet-active {
		border-radius: 4px;
		width: 30px;
		background: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
	}

	@media (min-width: 768px) {

		.swiper-button-next,
		.swiper-button-prev {
			display: flex;
		}

		.swiper-pagination.swiper-pagination-bullets {
			display: block;
		}
	}
</style>
{/block}

{block scripts}
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script>
	document.addEventListener("DOMContentLoaded", function () {
		// Dropdown menu
		document.getElementById('dropdownButton').addEventListener('click', function () {
			const menu = document.getElementById('dropdownMenu');
			const icon = document.getElementById('dropdownIcon');
			menu.classList.toggle('hidden');
			icon.classList.toggle('rotate-180');
		});

		document.addEventListener('click', function (event) {
			const dropdownButton = document.getElementById('dropdownButton');
			const dropdownMenu = document.getElementById('dropdownMenu');
			const dropdownIcon = document.getElementById('dropdownIcon');
			const isClickInside = dropdownButton.contains(event.target) || dropdownMenu.contains(event.target);
			if (!isClickInside) {
				dropdownMenu.classList.add('hidden');
				dropdownIcon.classList.remove('rotate-180');
			}
		});
	});

	if (window.location.href.includes("couponsPaginator-page")) {
		const dealsElement = document.getElementById("deals");

		if (dealsElement) {
			dealsElement.scrollIntoView({ behavior: 'smooth' });
		}
	}
</script>

	<script>
		document.addEventListener("DOMContentLoaded", function() {

			const swiper = new Swiper('.banner-swiper', {
				direction: 'horizontal',
				spaceBetween: 20,
				navigation: {
					nextEl: '.banner-swiper-button-next',
					prevEl: '.banner-swiper-button-prev',
				},
				pagination: {
					el: '.banner-swiper-pagination',
					clickable: true,
				},
				breakpoints: {
					0: {
						slidesPerView: 1.2,
					},
					768: {
						slidesPerView: 2.2,
					},
					1024: {
						slidesPerView: 3,
					}
				},
			});

			const swiperCoupon = new Swiper('.swiper-coupon', {
				direction: 'horizontal',
				spaceBetween: 20,
				navigation: {
					nextEl: '.swiper-coupon-button-next',
					prevEl: '.swiper-coupon-button-prev',
				},
				pagination: {
					el: '.swiper-coupon-pagination',
					clickable: true,
				},
				breakpoints: {
					0: { slidesPerView: 2.2 },
					768: { slidesPerView: 4.2 },
					1024: { slidesPerView: 6 }
				},
			});

			document.querySelectorAll('.swiper-actions').forEach((swiperElement, index) => {
				let id = $(swiperElement).data('id');

				const swiper = new Swiper(swiperElement, {
					direction: 'horizontal',
					spaceBetween: 20,
					navigation: {
						nextEl: '.swiper-actions-button-next-' + id,
						prevEl: '.swiper-actions-button-prev-' + id,
					},
					pagination: {
						el: '.swiper-actions-pagination-' + id,
						clickable: true,
					},
					breakpoints: {
						0: { slidesPerView: 1.2 },
						768: { slidesPerView: 2.2 },
						1024: { slidesPerView: 3 }
					},
				});
			});

			document.querySelectorAll('.swiper-biggest-sales').forEach((swiperElement, index) => {
				let id = $(swiperElement).data('id');

				const swiper = new Swiper(swiperElement, {
					direction: 'horizontal',
					spaceBetween: 20,
					navigation: {
						nextEl: '.swiper-biggest-sales-button-next-' + id,
						prevEl: '.swiper-biggest-sales-button-prev-' + id,
					},
					pagination: {
						el: '.swiper-biggest-sales-pagination-' + id,
						clickable: true,
					},
					breakpoints: {
						0: { slidesPerView: 1.2 },
						768: { slidesPerView: 2.2 },
						1024: { slidesPerView: 4 }
					},
				});
			});

			document.querySelectorAll('.swiper-top-shops').forEach((swiperElement, index) => {
				let id = $(swiperElement).data('id');

				const swiper = new Swiper(swiperElement, {
					direction: 'horizontal',
					spaceBetween: 20,
					navigation: {
						nextEl: '.swiper-top-shops-button-next-' + id,
						prevEl: '.swiper-top-shops-button-prev-' + id,
					},
					pagination: {
						el: '.swiper-top-shops-pagination-' + id,
						clickable: true,
					},
					breakpoints: {
						0: { slidesPerView: 2.2 },
						768: { slidesPerView: 4.2 },
						1024: { slidesPerView: 6 }
					},
				});
			});

			document.querySelectorAll('.swiper-most-sales').forEach((swiperElement, index) => {
				let id = $(swiperElement).data('id');

				const swiper = new Swiper(swiperElement, {
					direction: 'horizontal',
					spaceBetween: 20,
					navigation: {
						nextEl: '.swiper-most-sales-button-next-' + id,
						prevEl: '.swiper-most-sales-button-prev-' + id,
					},
					pagination: {
						el: '.swiper-most-sales-pagination-' + id,
						clickable: true,
					},
					breakpoints: {
						0: { slidesPerView: 1.2 },
						768: { slidesPerView: 2.2 },
						1024: { slidesPerView: 4 }
					},
				});
			});
		});
	</script>
{/block}

{block footerBackgroundColor}bg-light-6{/block}

{block content}

{*{cache 'dealBanners-' . $localization->getId() . '-' . ($user->isLoggedIn() ? 1 : 0), expire => '1 hour'}*}
	{control bannerSlider, $banners()}
{*{/cache}*}

<div class="relative bg-light-6 md:pt-10">
	<section class="container relative z-20">
		<div class="swiper-container relative pb-10 md:pb-[85px] pt-5 md:pt-10">
			<div class="swiper-coupon">
				{cache 'dealTopShops-' . $localization->getId()}
					<div class="swiper-wrapper">
						{var $topShops = $topShops()}
						{foreach $topShops as $topShop}
						{var $countOfCoupons = $topShop->getCountOfCouponDeals()}

						<a n:href=":NewFront:Shops:Shop:, $topShop"
							class="swiper-slide flex flex-col items-center justify-center bg-white rounded-xl cursor-pointer shadow-hover mb-5">
							<div class="w-full h-[96px] flex items-center justify-center">
								<img src="{$topShop->getCurrentLogo() |image:200,0,'fit',false,$topShop->getName()}"
									data-lazy="true" alt="{$topShop->getName()}" class="max-h-[60px] max-w-[100px] w-full">
							</div>
							<img class="m-auto mb-[17px]" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" loading="lazy" alt="wave">
							<div class="flex flex-col items-center gap-2 mb-4">
								<div class="flex items-center gap-3" n:if="$countOfCoupons > 0">
									<div>
										<img src="{$basePath}/new-design/coupon.svg" loading="lazy" alt="coupon">
									</div>
									{_"newFront.deals.shop.countOfCoupons", ['count' => $countOfCoupons]}
								</div>
							</div>
						</a>
						{/foreach}
					</div>
				{/cache}
			</div>
			<div class="swiper-button-prev swiper-coupon-button-prev"></div>
			<div class="swiper-button-next swiper-coupon-button-next"></div>
		</div>
	</section>
</div>

{ifset $contentSectionBlocks['top']}
	{foreach $contentSectionBlocks['top'] as $contentSectionBlock}
		{control contentSection, $contentSectionBlock, $iterator->isLast()}
	{/foreach}
{/ifset}

{if $user->isLoggedIn() === true && $user->getIdentity()->hasInstalledAddon() === false}
	{control addonPromo}
{/if}

<div class="relative bg-light-6 md:pt-10 pb-10 md:pb-36">
	<section class="container relative z-20">
		<h2 class="text-lg text-dark-1 font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] mb-5 md:mb-10">
			{_"newFront.deals.topCouponsTitle"}
		</h2>

		{cache 'deals-topDailyDeals-' . $localization->getId() . '-' . $isAdmin . '-' . ($user->isLoggedIn() ? 1 : 0), expire => '1 hour'}
			<div class="pb-10">
				{control topDailyDeals}
			</div>
		{/cache}

		{cache 'dealsTags-' . $localization->getId() . '-' . ($tag ? $tag->getId() : 0) . '-' . ($user->isLoggedIn() ? 1 : 0), expire => '1 hour'}
			<div id="deals" class="flex flex-col md:flex-row items-center justify-between mb-[23px] md:mb-[38px]">
				<h1 class="text-lg text-dark-1 font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] mb-3 md:mb-0">
					{if $tag}
						{_"newFront.deals.couponsTitleWithTag", ['tag' => $tag->getName()]}
					{else}
						{_"newFront.deals.couponsTitle"}
					{/if}
				</h1>
				<div class="relative inline-block text-left">
					{var $tags = $tags()}
					{var $countOfTotalCoupons = $getCountOfTotalCoupons()}

					<button
						class="ajax flex items-center gap-[38px] leading-[24.5px] font-medium text-dark-1 text-sm px-5 py-2.5 border border-light-2 rounded-xl xl:hover:bg-light-4"
						id="dropdownButton">
						<div>
							{if $tag}
								{$tag->getName()} ({$tag->getCountOfCouponDeals()})
							{else}
								{_'front.deals.deals.allCategory'} ({$countOfTotalCoupons})
							{/if}
						</div>
						<svg class="transition" id="dropdownIcon" xmlns="http://www.w3.org/2000/svg" width="14" height="8"
							viewBox="0 0 14 8" fill="none">
							<path d="M1 1.00009L7.00018 7L13 1" stroke="#080B10" stroke-miterlimit="10"
								stroke-linecap="round" stroke-linejoin="round" />
						</svg>
					</button>
					<div class="absolute left-0 mt-2 w-[232px] max-w-full origin-top-right bg-white divide-y divide-gray-100 rounded-xl overflow-hidden shadow-lg z-30 hidden"
						id="dropdownMenu">
						<div>
							<a n:if="$tag" n:href="this#deals, entity => null"
								class="block px-[15px] py-2.5 font-medium }border-b border-light-5 text-sm text-gray-700 xl:hover:bg-light-4">{_'front.deals.deals.allCategory'}
								({$countOfTotalCoupons})</a>

							<a n:if="$item !== $tag" n:href="this#deals, entity => $item" n:foreach="$tags as $item"
								class="block px-[15px] py-2.5 font-medium {sep}border-b border-light-5{/sep} text-sm text-gray-700 xl:hover:bg-light-4">
								{$item->getName()}
								<span class="font-normal">({$item->getCountOfCouponDeals()})</span></a>
						</div>
					</div>
				</div>
			</div>
		{/cache}

		<div n:snippet="coupons-wrapper">
			{cache 'dealCoupons-' . $currentCouponsPageNumber . '-' . $couponsCachePostfix}
				<div class="datalist">
					<div data-ajax-append="true" n:snippet="coupons"
						class="grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4 md:gap-7">
						{if !isset($coupons) && isset($getCoupons)}
							{var $coupons = $getCoupons()}
						{/if}

						{foreach $coupons as $coupon}
							{include 'couponItem.latte', coupon: $coupon}
						{/foreach}
					</div>

					<div class="text-center mt-10">
						{control couponsPaginator}
					</div>
				</div>
			{/cache}
		</div>

		<h2
			class="text-lg text-dark-1 font-medium leading-[31.5px] pl-5 md:text-[26px] md:leading-[39px] mb-3 mt-[51px] md:mt-[100px] md:pl-0 md:mb-[31px]">
			{_"newFront.deals.otherDealsTitle"}
		</h2>

		{cache 'dealOtherDeals-' . $localization->getId() . '-' . $isAdmin . '-' . ($user->isLoggedIn() ? 1 : 0)}
			<div class="grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4 md:gap-7">
				{var $otherDeals = $getOtherDeals()}

				{foreach $otherDeals as $deal}
					{include 'dealItem.latte', deal: $deal}
				{/foreach}
			</div>
		{/cache}
	</section>
</div>

<section class="w-[720px] max-w-full px-5 m-auto mt-[51px] md:mt-[100px] mb-14 content"
	n:if="(isset($pageExtension) && $pageExtension->getMiddleDescription()) || (isset($pageExtension) && $pageExtension->getBottomDescription())">
	{if $pageExtension->getMiddleDescription()}
	{$pageExtension->getMiddleDescription() |noescape}
	{/if}

	{if $pageExtension->getBottomDescription()}
	{$pageExtension->getBottomDescription() |noescape}
	{/if}
</section>
