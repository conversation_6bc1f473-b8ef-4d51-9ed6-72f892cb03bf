<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Presenters;

use Nette\DI\Attributes\Inject;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\WidgetTypeRepository;
use tipli\Model\LuckyShop\UserWidgetProvider;
use tipli\Model\LuckyShop\WidgetConditions\StreakWidgetCondition;
use tipli\Model\LuckyShop\WidgetFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\NewFrontModule\Presenters\BasePresenter;

class WidgetsPresenter extends BasePresenter
{
	#[Inject]
	public UserWidgetProvider $userWidgetProvider;

	#[Inject]
	public StreakWidgetCondition $streakWidgetCondition;

	#[Inject]
	public WidgetFacade $widgetFacade;

	#[Inject]
	public WidgetTypeRepository $widgetTypeRepository;

	public function renderDefault()
	{
		$validTill = (new \DateTime())->setTime(23, 59, 59);

		$userWidgets = $this->userWidgetProvider->provideUserWidgets($this->getUserIdentity(), new \DateTime(), $validTill);

		$this->template->userWidgets = $userWidgets;
	}

	public function handleClickWidget(int $userWidgetId): void
	{
		$user = $this->getUserIdentity();
		if (!$user) {
			$this->error('User not logged in', 401);
		}

		$userWidget = $this->widgetFacade->findUserWidgetById($userWidgetId);
		if (!$userWidget) {
			$this->error('Widget type not found', 404);
		}

		$widgetType = $userWidget->getWidgetType();

		$this->widgetFacade->createUserWidgetClick($user, $userWidget, $widgetType);

		$this->redirectWidget($widgetType, $userWidget);
	}

	private function redirectWidget(WidgetType $widgetType, UserWidget $userWidget): void
	{
		$localization = $widgetType->getLocalization();
		$type = $widgetType->getType();

		if ($type === WidgetType::TYPE_DEFAULT) {
			$this->redirect(':NewFront:Shops:Shops:default');
		}

		if ($type === WidgetType::TYPE_TIP) {
			$this->redirect(':NewFront:Shops:Redirection:banner', ['banner' => $userWidget->getBanner(), 'source' => 'widget']);
		}

		if ($type === WidgetType::TYPE_STREAK) {
			$this->redirect(':NewFront:LuckyShops:LuckyShops:setUserLuckyShop', ['source' => UserLuckyShop::SOURCE_STREAK]);
		}

		if ($type === WidgetType::TYPE_ADDON) {
			$this->redirect(':NewFront:Static:addon');
		}

		if ($type === WidgetType::TYPE_MOBILE_APP) {
			if ($this->clientLayer->isMobileApp() && $this->configuration->isThereMobileApp($localization)) {
				$this->redirectUrl($this->configuration->getMobileAppStoreLink($localization, $this->clientLayer->getPlatform()));
			}

			$this->redirect(':NewFront:Static:phoneApp');
		}

		if ($type === WidgetType::TYPE_PROMO_SAZKA) {
			$sazkaShop = $this->shopFacade->find(Shop::SAZKA_ID);

			$this->redirect(':NewFront:Shops:Shop:', ['shop' => $sazkaShop]);
		}

		$this->redirect('this');
	}
}
