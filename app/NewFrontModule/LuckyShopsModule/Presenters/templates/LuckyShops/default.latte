{block title}{_newFront.luckyShops.meta.title}{/block}

{block metaDescription}{_newFront.luckyShops.meta.description}{/block}

{block image}{_newFront.luckyShops.meta.image}{/block}

{block head}
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
            crossorigin="anonymous"></script>

    {if $localization->isCzech() && $currentStateForUser === 'luckyShopReveal'}
        <!--Google GPT/ADM code -->
        <script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
        <script type="text/javascript" n:syntax="off">
            window.googletag = window.googletag || { cmd: [] };
            if (!window.googletag.cmd) {
                window.googletag.cmd = [];
            }
            window.googletag.cmd.push(function () {
                window.googletag.pubads().enableSingleRequest();
            });
        </script>

        <!--Site config -->
        <script type="text/javascript" async="async" src="https://protagcdn.com/s/tipli.cz/site.js"></script>
        <script type="text/javascript" n:syntax="off">
            window.protag = window.protag || { cmd: [] };
            window.protag.config = { s:'tipli.cz', childADM: '23037269705', l: 'FbM3ys2m' };
            window.protag.cmd.push(function () {
                window.protag.pageInit();
            });
        </script>

        <script type="text/javascript" n:syntax="off">
            window.googletag = window.googletag || { cmd: [] };
            window.protag = window.protag || { cmd: [] };
            window.protag.cmd.push(function () {
                window.protag.display("protag-interstitial");
            });
        </script>
    {/if}

    {if $localization->isSlovak() && $currentStateForUser === 'luckyShopReveal'}
        <!--Google GPT/ADM code -->
        <script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
        <script type="text/javascript" n:syntax="off">
            window.googletag = window.googletag || {};
            if (!window.googletag.cmd) {
                window.googletag.cmd = [];
            }
            window.googletag.cmd.push(function () {
                window.googletag.pubads().enableSingleRequest();
            });
        </script>

        <!--Site config -->
        <script type="text/javascript" async="async" src="https://protagcdn.com/s/tipli.sk/site.js"></script>
        <script type="text/javascript" n:syntax="off">
            window.protag = window.protag || { cmd: [] };
            window.protag.config = { s:'tipli.sk', childADM: '23037269705', l: 'FbM3ys2m' };
            window.protag.cmd.push(function () {
                window.protag.pageInit();
            });
        </script>
        <script type="text/javascript" n:syntax="off">
            window.googletag = window.googletag || {};
            if (!window.googletag.cmd) {
                window.googletag.cmd = [];
            }
            window.protag = window.protag || { cmd: [] };
            window.protag.cmd.push(function () {
                window.protag.display("protag-interstitial");
            });
        </script>
    {/if}
{/block}

{block content}
<link
        href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght,HEXP@160..700,0..100&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
        rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>

<div class="bg-[#182B4A]">
<div n:if="$currentStateForUser != 'luckyShopReveal'" class="flex lg:hidden items-center justify-center gap-2.5">
    <div class="relative flex items-center overflow-x-hidden w-full">
        {capture $countdown}
            {var $nextLuckyShopAt = $luckyShopCampaign->nextLuckyShopAfter()}
            <div class="flex min-w-[120px] justify-start text-[20px] leading-[19px] text-secondary-green tracking-[5px] font-medium font-['DS-Digital'] js-count-down">
                <div class="flex flex-col items-center">
                    <span class="js-count-down-hour">{$nextLuckyShopAt->format('%H')}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span>:</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="js-count-down-minute">{$nextLuckyShopAt->format('%I')}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span>:</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="js-count-down-second">{$nextLuckyShopAt->format('%S')}</span>
                </div>
            </div>
        {/capture}

        <div class="flex lg:hidden w-full bg-secondary-green/10 relative py-1.5 z-30 overflow-hidden whitespace-nowrap text-white">
            <div class="marquee__item">
                {_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop, ['countdown' => $countdown] |noescape}
            </div>

            <div class="marquee__item">
                {_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop, ['countdown' => $countdown] |noescape}
            </div>

            <div class="marquee__item">
                {_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop, ['countdown' => $countdown] |noescape}
            </div>

            <div class="marquee__item">
                {_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop, ['countdown' => $countdown] |noescape}
            </div>
        </div>
    </div>
</div>

<div class="container relative">
    <div class="hidden lg:block absolute top-0 left-[-120px] z-20">
        <img src="{$basePath}/new-design/gives-out-bg-star.svg" alt="star">
    </div>

    <div class="lg:hidden absolute top-[-230px] left-[-290px] z-20">
        <img src="{$basePath}/new-design/gives-out-bg-star-mobile.svg" alt="star">
    </div>

    <div class="hidden lg:block absolute top-[-260px] left-[-1230px] z-20">
        <img class="blur-3xl" src="{$basePath}/new-design/gives-out-bg-circle.png" alt="circle">
    </div>

    <div class="w-full max-w-[968px] m-auto flex flex-col lg:flex-row justify-between items-center pt-5 lg:pt-[56px] relative z-40">
        <div class="{if $localization->isHungarian()}text-base{else}text-lg{/if} lg:text-[33px] leading-[45px]">
            {if $localization->isHungarian()}
                <div class="flex items-center text-white font-bold">
                    <div class="gradient-text-lg">{_newFront.luckyShops.title}</div>
                    <div class="flex items-center text-white font-bold">
                        <div class="relative mx-2 lg:ml-4 lg:mr-0">
                            <img class="hidden lg:block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-md.svg" alt="donkey">
                            <img class="lg:hidden absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-[57px]" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
                            <div class="relative text-center font-bold text-white">
                                <span class="text-white text-nowrap leading-[67px] rounded-md lg:rounded-[13px] bg-orange-gradient px-2 py-1 lg:px-[17px]">
                                    {_newFront.luckyShops.amount}
                                </span>
                            </div>
                        </div>
                        <div class="text-white lg:hidden font-bold">
                            {_newFront.luckyShops.everyDay}
                        </div>
                    </div>
                </div>
            {else}
                <div class="flex items-center text-white font-bold">
                    <div class="gradient-text-lg">{_newFront.luckyShops.title}</div>
                    <div class="relative mx-2 lg:ml-4 lg:mr-0">
                        <img class="hidden lg:block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-md.svg" alt="donkey">
                        <img class="lg:hidden absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
                        <div class="relative text-center font-bold text-white">
                            <span class="text-white text-nowrap leading-[67px] rounded-md lg:rounded-[13px] bg-orange-gradient px-2 py-1 lg:px-[17px]">
                                {_newFront.luckyShops.amount}
                            </span>
                        </div>
                    </div>
                    <div class="text-white lg:hidden font-bold">
                        {_newFront.luckyShops.everyDay}
                    </div>
                </div>
            {/if}

            <div class="hidden lg:block text-white font-bold mb-[18px] lg:-mt-1 leading-8">
                {_newFront.luckyShops.everyDay}
            </div>

            <div class="w-full max-w-[371px] text-white text-sm lg:text-base leading-[24.5px] lg:leading-[31.5px]">
                {_newFront.luckyShops.text}
            </div>
        </div>

        <div class="relative w-full sm:w-auto swiper-lucky-shops-container">
            <div id="swiper-lucky-shops"
				 class="bg-[#30415D] lg:bg-[#30415D]/40 backdrop-blur-[7.5px] text-white w-full lg:w-[455px] lg:ml-auto rounded-2xl pb-[18px] lg:pb-5 pt-5 lg:pt-[25px] shadow-custom relative px-5 lg:px-[30px] mt-10 lg:mt-0">
				<div class="flex items-center justify-between">
					<div
						class="text-sm font-medium lg:text-base lg:font-bold leading-[24.5px] lg:leading-[31.5px] text-white">
						{_newFront.luckyShops.detail.userLuckyShops} <span
						class="text-sm leading-[24.5px] font-normal text-[#9099A8]">( {count($activeUserLuckyShopsWithShop)} / {count($userLuckyShops)} )</span>
					</div>
                    {if count($userLuckyShopsWithShop) + count($userLuckyShopsWithoutShop) >= 4}
                        <button class="moreSlotsOpenModalBtn text-sm leading-[24.5px] font-normal text-[#9099A8] underline hover:no-underline">
                            {_newFront.luckyShops.detail.moreUserLuckyShops}
                        </button>
                    {/if}
				</div>

                <div class="swiper swiper-lucky-shops !py-5 md:!pl-0">
                    <div class="swiper-wrapper">
                        {foreach $userLuckyShopsWithShop as $index => $userLuckyShop}
                            <div class="swiper-slide" data-position="{$index}">
                                {include snippets/userLuckyShop.latte, shop: $userLuckyShop->getShop(), userLuckyShop => $userLuckyShop}
                            </div>

                            {if $iterator->counter0 === 0}
                                {foreach $userLuckyShopsWithoutShop as $index => $userLuckyShop}
                                    <div class="swiper-slide" data-position="{$index}">
                                        {include snippets/userLuckyShop.latte, shop: $userLuckyShop->getShop(), userLuckyShop => $userLuckyShop}
                                    </div>
                                {/foreach}
                            {/if}
                        {/foreach}

                        <button class="swiper-slide moreSlotsOpenModalBtn">
                            <span class="relative cursor-pointer">
                                <img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px] hover:scale-95 transition-transform" src="/new-design/gives-out-add-lucky-shop.svg" alt="obchod">
                            </span>
                        </button>

                        {for $i = 0; $i < (3 - count($userLuckyShops)); $i++}
                            <div class="swiper-slide">
                                <img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px] hover:scale-95 transition-transform" src="{$basePath}/new-design/gives-out-empty-lucky-shop.svg" alt="obchod">
                            </div>
                        {/for}
                    </div>
                </div>

                <div class="h-px bg-white/10 w-full lg:mt-2 mb-5"></div>

                <div class="flex items-center justify-between">
                    <div class="text-xs lg:text-sm leading-[24.5px] text-white">
                        {_newFront.luckyShops.detail.checkLuckyShops}
                    </div>
                    <div class="text-sm font-bold leading-[24.5px]" id="streak">
                        🔥 {_newFront.luckyShops.detail.checkLuckyShopsStreak, ['count' => $userIdentity->getLuckyShopCheckStreak()] |noescape}
                    </div>
                </div>

				<button
					class="swiper-button-prev-custom relative left-[-13px] transition-[left] hover:md:left-[-26px] md:left-[-23px]"></button>
				<button
                    class="swiper-button-next-custom hover:md:right-[-26px] transition-[right] relative right-[-13px] md:right-[-23px]">
                </button>
            </div>
        </div>
    </div>
</div>

<div class="bg-lucky-shops mt-[191px] relative">
    {if $currentStateForUser === 'win'}
        {include 'snippets/winScreen.latte'}
    {elseif $currentStateForUser === 'lose'}
        {include 'snippets/loseScreen.latte'}
    {elseif $currentStateForUser === 'nextLuckyShop'}
        {include 'snippets/nextLuckyShopScreen.latte'}
    {elseif $currentStateForUser === 'luckyShopReveal'}
        {include 'snippets/luckyShopRevealScreen.latte'}
    {/if}

    <div class='w-full max-w-[968px] m-auto px-5 lg:px-0'>
        <div id="guide-section" data-default-tab='{$isMobileInAppBrowser === true ? 'content-4' : 'content-1'}' class="mt-10 md:mt-[70px] flex gap-10 pb-10 md:pb-0">
            <div class="w-full">
                <div class="flex gap-[30px] mb-4 text-nowrap overflow-x-auto">
                    <div id="tab-1"
						 data-target="content-1"
						 class="cursor-pointer hover:text-white item-tab flex items-center gap-[9px] text-xs lg:text-sm text-white/50 leading-[24.5px] lg:leading-[24.5px] transition duration-200">
                        <svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                             fill="none">
                            <path d="M6.01419 6.38579C6.01419 5.97891 6.13557 5.58117 6.36288 5.24287C6.59019 4.90456 6.91338 4.64088 7.29142 4.48518C7.66946 4.32948 8.08547 4.28873 8.48679 4.36811C8.88811 4.44749 9.25677 4.64343 9.54609 4.93113C9.83553 5.21883 10.0325 5.5854 10.1124 5.98445C10.1922 6.3835 10.1512 6.79714 9.99464 7.17306C9.83806 7.54896 9.5728 7.87025 9.23261 8.0963C8.89241 8.32235 8.4923 8.44299 8.08315 8.44299M6.06208 1.27692C4.76145 1.65525 3.59886 2.39971 2.71434 3.42047C1.82982 4.44123 1.26132 5.69466 1.07727 7.02957C0.643464 10.1777 2.00039 13.5564 6.06704 14.7463C7.85142 15.237 9.7584 15.0102 11.3762 14.1151C12.994 13.22 14.193 11.7283 14.7145 9.96198C15.2359 8.19559 15.0379 6.29618 14.1632 4.67366C12.5833 1.74261 9.18276 0.369116 6.06208 1.27692ZM8.08315 11.7346C7.85462 11.7346 7.66936 11.5504 7.66936 11.3231C7.66936 11.0959 7.85462 10.9116 8.08315 10.9116C8.31167 10.9116 8.49694 11.0959 8.49694 11.3231C8.49694 11.5504 8.31167 11.7346 8.08315 11.7346Z" stroke="currentColor" stroke-linecap="round"/>
                        </svg>
                        {_newFront.luckyShops.default.tabs.howItWorks.title}
                    </div>
                    <div id="tab-2" data-target="content-2"
						 class="hidden cursor-pointer hover:text-white item-tab flex items-center gap-[9px] text-xs lg:text-sm text-dark-3 leading-[24.5px] lg:leading-[24.5px] transition duration-200">
                        <svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                             fill="none">
                            <path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="currentColor" stroke-linecap="round"/>
                        </svg>
                        {_newFront.luckyShops.default.tabs.conditions.title}
                    </div>
					<div id="tab-4" data-target="content-4"
						 class="cursor-pointer hover:text-white item-tab flex items-center gap-[9px] text-xs lg:text-sm text-white/50 leading-[24.5px] lg:leading-[24.5px] transition duration-200">
						<svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
							 viewBox="0 0 16 16"
							 fill="none">
							<path d="M4.54266 2.84399C3.85054 3.22833 3.28005 3.79905 2.89599 4.49133M3.98333 1C3.273 1.20196 2.62606 1.58211 2.10393 2.10435C1.5818 2.6266 1.2018 3.27362 1 3.984M8 3.28801C10.292 3.28801 12 5.50001 12 8C12 11.1293 13 12 13 12H3C3 12 4 11.146 4 8C4 5.50001 5.708 3.28801 8 3.28801ZM8 3.28801V2M9 14C9 14.2652 8.89467 14.5196 8.70713 14.7071C8.5196 14.8947 8.2652 15 8 15C7.7348 15 7.4804 14.8947 7.29287 14.7071C7.10533 14.5196 7 14.2652 7 14M11.4573 2.84399C11.8011 3.03595 12.1169 3.27417 12.396 3.55199C12.6743 3.83087 12.9125 4.14699 13.104 4.49133M12.0167 1C12.727 1.20196 13.3739 1.58211 13.8961 2.10435C14.4182 2.6266 14.7982 3.27362 15 3.984" stroke="currentColor" stroke-linecap="round"/>
						</svg>
                        {_newFront.luckyShops.default.notifications.title}
					</div>
                </div>

                <div id="content-1" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">
                            {_newFront.luckyShops.default.tabs.howItWorks.steps.step1.title}
                        </div>
                        <div class="text-sm leading-[24.5px] text-white">
                            {_newFront.luckyShops.default.tabs.howItWorks.steps.step1.text}
                        </div>
                    </div>

                    <div class="h-px bg-white/10 w-full my-5"></div>

                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">
                            {_newFront.luckyShops.default.tabs.howItWorks.steps.step2.title}
                        </div>
                        <div class="text-sm leading-[24.5px] text-white">
                            {_newFront.luckyShops.default.tabs.howItWorks.steps.step2.text}
                        </div>
                    </div>

                    <div class="h-px bg-white/10 w-full my-5"></div>

                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.title}</div>
                        <div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.text}</div>
                    </div>

                    <div class="h-px bg-white/10 w-full my-5"></div>

                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.title}</div>
                        <div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.text}</div>
                    </div>
                </div>

                <div id="content-2" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">
                            {_newFront.luckyShops.default.tabs.conditions.condition1.title}
                        </div>
                        <div class="text-sm leading-[24.5px] text-white">
                            {_newFront.luckyShops.default.tabs.conditions.condition1.text}
                        </div>
                    </div>

                    <div class="h-px bg-white/10 w-full my-5"></div>

                    <div>
                        <div class="text-white font-bold leading-6 mb-[5px]">
                            {_newFront.luckyShops.default.tabs.conditions.condition2.title}
                        </div>
                        <div class="text-sm leading-[24.5px] text-white">
                            {_newFront.luckyShops.default.tabs.conditions.condition2.text}
                        </div>
                    </div>
                </div>

				<div id="content-4" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
					<div class="text-white">
						<div class="text-sm md:text-base font-bold leading-7 mb-[5px]">{_newFront.luckyShops.default.notifications.title}</div>
						<div class="text-xs md:text-sm leading-[24.5px]">{_newFront.luckyShops.default.notifications.text}</div>
					</div>

                    {snippet notificationSettingsControl}
                    {form notificationSettingsControl-form, class: "ajax"}
                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div class="text-white">
                            <div class="flex w-full items-center justify-between mb-[5px]">
                                <div class="flex items-center text-sm md:text-base font-bold leading-7">
                                    📩 <span class="ml-2">{_newFront.luckyShops.default.notifications.email}</span>
                                </div>

                                <div class="relative inline-block">
                                    <label class="hidden" for="notifications-1"></label>
                                    <input n:name="email" onchange="this.form.requestSubmit()" type="checkbox" id="notifications-1"
                                           class="no-default-style peer focus:outline-none focus:ring-0 relative w-[3.25rem] h-7 p-px bg-white/30 checked:bg-[#66B940] border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-[#66B940] checked:[#66B940] focus:checked:border-[#66B940] before:inline-block before:size-6 before:bg-white/50 checked:before:bg-white before:translate-x-0 checked:before:translate-x-full before:rounded-full before:transform before:ring-0 before:transition before:ease-in-out before:duration-200">
                                    <span
                                        class="focus:outline-none focus:ring-0 peer-checked:text-white text-white size-6 absolute top-0.5 start-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200">
                                            <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                                  <path d="M18 6 6 18"></path>
                                                  <path d="m6 6 12 12"></path>
                                            </svg>
                                    </span>
                                    <span
                                        class="focus:outline-none focus:ring-0 text-white peer-checked:text-[#66B940] size-6 absolute top-0.5 end-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200">
                                            <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                              <polyline points="20 6 9 17 4 12"></polyline>
                                            </svg>
                                      </span>
                                </div>
                            </div>
                            <div class="text-xs md:text-sm leading-[24.5px]">
                                {_newFront.luckyShops.default.notifications.emailText}
                            </div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div class="text-white">
						<div class="flex w-full items-center justify-between mb-[5px]">
							<div class="text-sm md:text-base font-bold leading-7">
								🔔 <span class="ml-1">{_newFront.luckyShops.default.notifications.push}</span>
							</div>

							<div class="relative inline-block">
								<label class="hidden" for="notifications"></label>
								<input n:name="pushNotifications" onchange="this.form.requestSubmit()" type="checkbox" id="notifications" class="no-default-style peer focus:outline-none focus:ring-0 relative w-[3.25rem] h-7 p-px bg-white/30 checked:bg-[#66B940] border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-[#66B940] checked:[#66B940] focus:checked:border-[#66B940] before:inline-block before:size-6 before:bg-white/50 checked:before:bg-white before:translate-x-0 checked:before:translate-x-full before:rounded-full before:transform before:ring-0 before:transition before:ease-in-out before:duration-200">
									<span class="focus:outline-none focus:ring-0 peer-checked:text-white text-white size-6 absolute top-0.5 start-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200">
										<svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
											<path d="M18 6 6 18"></path>
											  <path d="m6 6 12 12"></path>
										</svg>
								  	</span>
								<span class="focus:outline-none focus:ring-0 text-white peer-checked:text-[#66B940] size-6 absolute top-0.5 end-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200">
									<svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
									  <polyline points="20 6 9 17 4 12"></polyline>
									</svg>
							  	</span>
							</div>

						</div>
						<div class="text-xs md:text-sm leading-[24.5px]">
                            {_newFront.luckyShops.default.notifications.pushText}
						</div>
					</div>
                    {/form}
                    {/snippet}
				</div>
            </div>

            <div id="tab3-container" class="hidden lg:block w-[330px]">
                <div id="tab-3"
					 data-target="content-3"
                     class="cursor-pointer lg:mb-4 item-tab hover:text-white flex items-center gap-[9px] text-xs lg:text-sm text-white/50 lg:text-white leading-[24.5px] lg:leading-[24.5px] transition duration-200">
                    <svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    {_newFront.luckyShops.default.tabs.history.title}
                </div>

                <div id="content-3" class="hidden lg:flex flex-col gap-5">
                    <div id="shops-list" class="datalist">
                        <div class="datalist-data flex flex-col gap-5" n:snippet="luckyShopHistory">
                            {foreach $luckyShopsHistory as $luckyShop}
                                {var $shop = $luckyShop->getShop()}
                                {var $userLuckyShopsForLuckyShop = $getUserLuckyShopsForLuckyShop($luckyShop)}

                                <div class="bg-[#30415D] lg:bg-[#30415D]/50 backdrop-blur p-5 pb-3 rounded-2xl md:mb-0">
                                    <div class="flex justify-between items-center text-white text-sm leading-[24.5px] font-bold uppercase">
                                        <a n:href=":NewFront:Shops:Shop:default $shop" target="_blank">
                                            {$shop->getName()}
                                        </a>
                                        <div class="font-normal">{$luckyShop->getValidSince() |localDate}</div>
                                    </div>
                                    <div class="h-px bg-white/10 w-full mt-2.5 mb-[14px]"></div>
                                    <div class="flex items-center justify-between gap-[15px] w-full">
                                        <a n:href=":NewFront:Shops:Shop:default $shop" target="_blank"
										   class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-lg">
                                            <img class="max-w-[70px] max-h-[40px]" src="{$shop->getCurrentLogo() |image:200,0}">
                                        </a>
                                        <div class="flex flex-col items-end text-sm text-white text-nowrap z-10">
                                            <div>{_newFront.luckyShops.default.checkLuckyShop.winners}: <span class="text-primary-orange">{$luckyShop->getCountOfUsers()}x</span></div>
                                            <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest}: <span class="text-primary-orange">{$luckyShop->getCountOfUsersWithCheck()}x</span></div>
                                        </div>
                                    </div>

                                    {if $userLuckyShopsForLuckyShop}
                                    <div class="h-px bg-white/10 w-full mt-[14px] mb-2"></div>
                                    <div class="flex justify-between items-center">
                                        <div class="text-sm text-white leading-[21px]">{_newFront.luckyShops.default.tabs.history.userLuckyShops}:</div>
                                        <div class="flex gap-1.5">
                                            {foreach $userLuckyShopsForLuckyShop as $userLuckyShop}
                                                <div>
                                                    <a n:href=":NewFront:Shops:Shop:default $userLuckyShop->getShop()" target="_blank"
                                                       class="flex items-center justify-center bg-white rounded-full w-12 h-12 border-2 border-primary-blue-dark">
                                                        <img class="min-w-[30px] max-w-[30px]" src="{$userLuckyShop->getShop()->getCurrentLogo() |image:40,40}" alt="">
                                                    </a>
                                                </div>

                                                {breakIf $iterator->counter > 2}
                                            {/foreach}

                                            {if count($userLuckyShopsForLuckyShop) > 3}
                                                <span class="inline-flex items-center text-white text-xs">+{count($userLuckyShopsForLuckyShop) - 3}</span>
                                            {/if}
                                        </div>
                                    </div>
                                    {/if}
                                </div>
                            {/foreach}
                        </div>
                        <div class="js--auto-load-more">
                            {control luckyShopsPaginator}
                        </div>
                    </div>
                </div>
            </div>
        </div>

		<div
			class="hidden lg:block relative z-10 text-[#2F67C2] ml-[-500px] py-[33px] opacity-10 text-[160px] leading-[178px] font-bold text-nowrap">
            {_newFront.luckyShops.bottomLargeText}
        </div>
    </div>
</div>

{control userLuckyShopPopupControl}

{control phoneNumberVerificationControl}

<div id="moreSlotsModalOverlay" class="fixed hidden inset-0 px-5 backdrop-blur-sm bg-primary-blue-dark/90 flex flex-col gap-5 items-center justify-center z-50">
        <div class="bg-white p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
            <div id="moreSlotsCloseModalBtn" class="absolute right-[-20px] top-[-16px] hover:cursor-pointer">
                <img src="{$basePath}/new-design/gives-out-close-modal-icon.svg" alt="zavriet">
            </div>
            <div class="text-lg font-bold leading-[31.5px] mb-[5px]">
                {_newFront.luckyShops.default.moreSlotsPopup.title}
            </div>
            <div class="text-xs leading-[21px]">
                {_newFront.luckyShops.default.moreSlotsPopup.text |noescape}
            </div>
            <div class="h-px bg-black/10 w-full my-5"></div>
            <div>
                <ol class="text-dark-1 text-sm font-medium list-decimal pl-[19px] list-outside max-h-[350px] md:max-h-[500px] overflow-auto">
                    <li>
                        <div class="flex flex-col md:flex-row md:gap-2 md:items-center text-sm font-medium leading-[24.5px] mb-[5px]">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step2.title}
                        </div>
                        <div class="ml-[-19px] md:ml-0">
                            <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step2.text}</div>
                            <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                                {_newFront.luckyShops.default.moreSlotsPopup.steps.step2.link |noescape}
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                    <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="h-px bg-black/10 w-full my-5"></div>
                        </div>
                    </li>

                    <li>
                        <div class="flex flex-col md:flex-row md:gap-2 md:items-center text-sm font-medium leading-[24.5px] mb-[5px]">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step3.title}
                            <div n:if="in_array('addon', $userLuckyShopSources)" class="flex items-center gap-1 text-xs leading-[21px] text-secondary-green px-2 py-1 rounded-full border border-secondary-green/30 bg-pastel-green-light w-max my-2.5 md:my-0 ml-[-19px] md:ml-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <rect width="13" height="13" rx="6.5" fill="#66B940"/>
                                    <path d="M4 6.56429L5.59524 8L9 5" stroke="white" stroke-width="1.2087" stroke-linecap="round"/>
                                </svg>
                                {_newFront.luckyShops.default.moreSlotsPopup.active}
                            </div>
                        </div>
                        <div class="ml-[-19px] md:ml-0">
                            <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step3.text}</div>
                            <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                                {_newFront.luckyShops.default.moreSlotsPopup.steps.step3.link |noescape}
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                    <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="h-px bg-black/10 w-full my-5"></div>
                        </div>
                    </li>

                    <li>
                        <div class="flex flex-col md:flex-row md:gap-2 md:items-center text-sm font-medium leading-[24.5px] mb-[5px]">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step4.title}
                            <div n:if="in_array('streak', $userLuckyShopSources)" class="flex items-center gap-1 text-xs leading-[21px] text-secondary-green px-2 py-1 rounded-full border border-secondary-green/30 bg-pastel-green-light w-max my-2.5 md:my-0 ml-[-19px] md:ml-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                                    <rect width="13" height="13" rx="6.5" fill="#66B940"/>
                                    <path d="M4 6.56429L5.59524 8L9 5" stroke="white" stroke-width="1.2087" stroke-linecap="round"/>
                                </svg>
                                {_newFront.luckyShops.default.moreSlotsPopup.active}
                            </div>
                        </div>
                        <div class="ml-[-19px] md:ml-0">
                            <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step4.text}</div>

                            <div class="h-px bg-black/10 w-full my-5"></div>
                        </div>
                    </li>

                    <li>
                        <div class="flex flex-col md:flex-row md:gap-2 md:items-center text-sm font-medium leading-[24.5px] mb-[5px]">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step1.title}
                        </div>
                        <div class="ml-[-19px] md:ml-0">
                            <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step1.text}</div>
                            <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                                {_newFront.luckyShops.default.moreSlotsPopup.steps.step1.link |noescape}
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                    <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="h-px bg-black/10 w-full my-5"></div>
                        </div>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-lucky-shops {
        position: relative;
        width: 100%;
        min-height: 1119px;
        height: auto;
        z-index: 20;
    }

    .bg-image {
        background-image: url(/new-design/bg-lucky-shops.svg);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
    }
</style>

<script n:syntax=off>
    // SWIPER LUCKY SHOPS
    const swiperContainer = document.querySelector('.swiper.swiper-lucky-shops');
    const slides = swiperContainer.querySelectorAll('.swiper-slide');

	const swiper = new Swiper('.swiper.swiper-lucky-shops', {
		direction: 'horizontal',
		loop: false,
		slidesPerView: 4,
		navigation: {
			nextEl: '.swiper-button-next-custom',
			prevEl: '.swiper-button-prev-custom',
		},
	});

	document.addEventListener('DOMContentLoaded', () => {
		const tabs = document.querySelectorAll('.item-tab');
		const guideSection = document.getElementById('guide-section');
		const defaultContentElementId = guideSection.getAttribute('data-default-tab');
		const defaultContent = document.getElementById(defaultContentElementId);

        if (defaultContentElementId === 'content-4') {
            const tab1 = document.getElementById('tab-1');
            const tab4 = document.getElementById('tab-4');

            if (tab1 && tab4 && tab1.parentElement === tab4.parentElement) {
                tab1.parentElement.insertBefore(tab4, tab1);
            }
        }

		const defaultTab = Array.from(tabs).find(tab =>
			tab.getAttribute('data-target') === defaultContentElementId
		);

		document.querySelectorAll('#content-1, #content-2, #content-3, #content-4').forEach(content =>
			content.classList.add('hidden')
		);

		if (defaultContent) defaultContent.classList.remove('hidden');
		if (defaultTab) {
			defaultTab.classList.remove('text-white/50');
			defaultTab.classList.add('text-white');
		}

		tabs.forEach((tab) => {
			if (tab.id === 'tab-3' && window.innerWidth > 1024) return;

			tab.addEventListener('click', function () {
				document.querySelectorAll('#content-1, #content-2, #content-3, #content-4').forEach(content =>
					content.classList.add('hidden')
				);

				const targetContentId = tab.getAttribute('data-target');
				if (targetContentId) {
					document.getElementById(targetContentId).classList.remove('hidden');
				}

				tabs.forEach(t => {
					if (t.id === 'tab-3' && window.innerWidth > 1024) return;
					t.classList.remove('text-white');
					t.classList.add('text-white/50');
				});
				tab.classList.remove('text-white/50');
				tab.classList.add('text-white');
			});
		});

        const modals = [
            { overlay: 'moreSlotsModalOverlay', openBtn: 'moreSlotsOpenModalBtn', closeBtn: 'moreSlotsCloseModalBtn' },
            { overlay: 'smsVerificationModalOverlay', openBtn: 'smsVerificationOpenModalBtn', closeBtn: 'smsVerificationCloseModalBtn' }
        ];

        function handleModal(modal) {
            const modalOverlay = document.getElementById(modal.overlay);
            const closeModalBtn = document.getElementById(modal.closeBtn);
            const openModalBtns = document.querySelectorAll(`#${modal.openBtn}, .${modal.openBtn}`);

            if (openModalBtns.length) {
                openModalBtns.forEach((btn) => {
                    btn.addEventListener('click', () => {
                        modalOverlay.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                    });
                });
            }

            if (closeModalBtn) {
                closeModalBtn.addEventListener('click', () => {
                    modalOverlay.classList.add('hidden');
                    document.body.style.overflow = '';
                });
            }

            if (modalOverlay) {
                modalOverlay.addEventListener('click', (e) => {
                    if (e.target === modalOverlay) {
                        modalOverlay.classList.add('hidden');
                        document.body.style.overflow = '';
                    }
                });
            }
        }

        modals.forEach(handleModal);
    });

    class CountdownTimer {
        constructor(hourElement, minuteElement, secondElement) {
            this.hourElement = hourElement;
            this.minuteElement = minuteElement;
            this.secondElement = secondElement;
            this.interval = null;
        }

        getTimeInSeconds() {
            const hours = parseInt(this.hourElement.textContent) || 0;
            const minutes = parseInt(this.minuteElement.textContent) || 0;
            const seconds = parseInt(this.secondElement.textContent) || 0;
            return hours * 3600 + minutes * 60 + seconds;
        }

        updateDisplay(totalSeconds) {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            this.hourElement.textContent = hours.toString().padStart(2, '0');
            this.minuteElement.textContent = minutes.toString().padStart(2, '0');
            this.secondElement.textContent = seconds.toString().padStart(2, '0');
        }

        start() {
            if (this.interval) return;

            let totalSeconds = this.getTimeInSeconds();

            this.interval = setInterval(() => {
                if (totalSeconds <= 0) {
                    this.stop();
                    location.reload();
                    return;
                }

                totalSeconds--;
                this.updateDisplay(totalSeconds);
            }, 1000);
        }

        stop() {
            if (this.interval) {
                clearInterval(this.interval);
                this.interval = null;
            }
        }

        reset(hours = 0, minutes = 0, seconds = 0) {
            this.stop();
            this.updateDisplay(hours * 3600 + minutes * 60 + seconds);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        const countdownElements = document.querySelectorAll('.js-count-down');

        if (countdownElements.length === 0) {
            console.warn('Žiadne odpočítavacie elementy nenájdené.');
            return;
        }

        countdownElements.forEach((countdownElement) => {
            const hourElement = countdownElement.querySelector('.js-count-down-hour');
            const minuteElement = countdownElement.querySelector('.js-count-down-minute');
            const secondElement = countdownElement.querySelector('.js-count-down-second');

            if (!hourElement || !minuteElement || !secondElement) {
                console.warn('Některé potřebné elementy pro odpočítávání chybí');
                return;
            }

            const timer = new CountdownTimer(hourElement, minuteElement, secondElement);
            timer.start();
        });
    });

    // JS for moving elements on mobile
    function moveElements() {
        const swiperElement = document.getElementById('swiper-lucky-shops');
        const swiperContainer = document.querySelector('.swiper-lucky-shops-container');
        const guideSection = document.getElementById('guide-section');

        const tab3 = document.getElementById('tab-3');
        const tab2 = document.getElementById('tab-2');
        const content3 = document.getElementById('content-3');
        const content2 = document.getElementById('content-2');

        const tab3Container = document.getElementById('tab3-container');

        if (window.innerWidth <= 1023) {
            guideSection.parentNode.insertBefore(swiperElement, guideSection);
            tab2.parentNode.insertBefore(tab3, tab2.nextSibling);
            content2.parentNode.insertBefore(content3, content2.nextSibling);
        } else {
            if (!swiperContainer.contains(swiperElement)) {
                swiperContainer.appendChild(swiperElement);
            }

            if (tab3Container && !tab3Container.contains(tab3)) {
                tab3Container.appendChild(tab3);
            }
            if (tab3Container && !tab3Container.contains(content3)) {
                tab3Container.appendChild(content3);
            }
        }

        swiperElement.style.visibility = 'visible';
        tab3.style.visibility = 'visible';
        content3.style.visibility = 'visible';
    }

    document.getElementById('swiper-lucky-shops').style.visibility = 'hidden';
    document.getElementById('tab-3').style.visibility = 'hidden';
    document.getElementById('content-3').style.visibility = 'hidden';

    window.addEventListener('DOMContentLoaded', moveElements);
    window.addEventListener('resize', moveElements);
</script>

<style>
	.swiper-button-prev-custom,
	.swiper-button-next-custom {
		width: 40px;
		height: 40px;
		background-color: white;
		border-radius: 50%;
		position: absolute;
		transform: translateY(-50%);
		z-index: 10;
		cursor: pointer;
        top: 114px ;
	}

    @media only screen and (max-width: 600px) {
        .swiper-button-prev-custom,
        .swiper-button-next-custom {
            top: 94px;
        }
    }

	.swiper-button-lock {
		display: block !important;
	}

	.swiper-button-prev-custom.swiper-button-disabled {
		display: none !important;
	}

	.swiper-button-next-custom.swiper-button-disabled {
		display: none !important;
	}

	.swiper-button-prev-custom::before,
	.swiper-button-next-custom::before {
		font-size: 18px;
		color: black;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.swiper-button-next-custom::before {
		content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='7' height='12' viewBox='0 0 7 12' fill='none'%3E%3Cpath d='M1 11L6.08426 6L1 1' stroke='%23080B10' stroke-width='1.5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	}
	.swiper-button-prev-custom::before {
		left: -2px;
		position: relative;
		content:
			url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='7' height='12' viewBox='0 0 7 12' fill='none'%3E%3Cpath opacity='1' d='M6 11L0.915737 6L6 1' stroke='%23080B10' stroke-width='1.5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	}
</style>
