<div class="is-shops-content absolute top-[-19px] md:top-[31px] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-20 w-full sm:w-auto">
    <img src="{$basePath}/new-design/bg-star-behind-donkey.svg" alt="star">
</div>

<div class="absolute inset-0 -z-10">
    <div class="w-full h-full bg-image"></div>
</div>

<div class='relative'>
    <img src="{$basePath}/new-design/oslik-lucky-shops-bg.png" alt="oslik" class="absolute top-[-43px] left-1/2 -translate-x-1/2 -translate-y-1/2 z-20">

    <div class="is-shops-content">
        <div class="absolute w-[100px] md:w-auto top-0 left-1/2 -translate-x-[152%] md:-translate-x-[132%] translate-y-[-105px] md:translate-y-[-95px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 font-bold border border-[#FDBB47]" style="background:rgba(253, 187,71, 0.10);">
            {_newFront.luckyShops.screens.reveal.text1}
        </div>

        <div class="absolute w-[120px] md:w-auto top-0 left-1/2 translate-x-[59%] md:translate-x-[41%] translate-y-[-96px] md:translate-y-[-75px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 font-bold border border-[#FDBB47]" style="background:rgba(253, 187,71, 0.10);">
            {_newFront.luckyShops.screens.reveal.text2}
        </div>
    </div>
</div>

<div class="pt-[101px] text-center relative px-10 lg:px-0 min-h-[205px]">
    <div class="absolute top-[80%] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-10">
        <img src="{$basePath}/new-design/lucky-shops-cta-star.svg" alt="cta-star">
    </div>

    {if $requestPhoneNumberVerification}
        <button id="smsVerificationOpenModalBtn" class="inline-block w-full" id="showPhoneNumberVerificationPopup">
            <span class="inline-block text-white text-sm lg:text-lg font-bold leading-[31.5px] bg-orange-gradient py-5 w-full max-w-[372px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10" style="box-shadow: 6px 6px 13px 0 rgba(239, 127, 26, 0.51);">
                {_newFront.luckyShops.screens.reveal.cta}
            </span>
        </button>
    {else}
		<div id="revealLoader" class="mx-auto mt-5 text-center py-[11px]">
			<div class="mx-auto">
				<svg xmlns="http://www.w3.org/2000/svg" width="62" height="62" viewBox="0 0 24 24" fill="none" stroke="#EF7F1A" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="animate-spin mx-auto lucide lucide-loader-circle-icon lucide-loader-circle"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
			</div>
		</div>

		{form revealLuckyShopForm}
			<div class="cf-turnstile"
				 data-sitekey="0x4AAAAAABlXArjJRF6FRw7p"
				 data-callback="onTurnstileCallback"
				 data-size="invisible">
			</div>

			<div class="relative mx-auto w-full md:max-w-[372px] mt-5">
				{input reveal, id => "revealLuckyShop", class => "hidden w-full text-white text-sm lg:text-lg text-center font-bold leading-[31.5px] bg-orange-gradient py-[15px] lg:py-[18px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10 slide-up-enter border-0", style => "box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);"}

				<span class="absolute left-0 top-[-34px] pointer-events-none">
					<img src="{$basePath}/new-design/lucky-shops-cta-left-stars.svg" alt="left stars">
				</span>

				<span class="absolute right-0 bottom-[-32px] pointer-events-none">
					<img src="{$basePath}/new-design/lucky-shops-cta-right-stars.svg" alt="right stars">
				</span>
			</div>
		{/form}
    {/if}
</div>

<div class="container">
    <div class="h-px bg-white/10 w-full mb-2.5 mt-[62px]"></div>
    <div class="flex flex-col lg:flex-row items-center justify-center gap-2.5 lg:gap-10 text-sm text-white leading-[24.5px] font-bold">
        <div>{_newFront.luckyShops.default.checkLuckyShop.winners}<span id="countOfWinners" class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">{$countOfUsersWithLuckyShop}x</span></div>
        <div n:if="$countOfUsersChecksWithLuckyShop !== $countOfUsersWithLuckyShop">{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest} <span id="countOfWinnersWithCheck" class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">{$countOfUsersChecksWithLuckyShop}x</span></div>
    </div>
    <div class="h-px bg-white/10 w-full mt-2.5"></div>
</div>


<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>

<script>
    let turnstileToken = null;
    let formSubmissionPending = false;
    let submitTimeout = null;
    let minTimeElapsed = false;

    function onTurnstileCallback(token) {
        turnstileToken = token;
        console.log('Turnstile token received:', token ? 'yes' : 'no');

        // Pokud čekáme na token a máme ho, odešleme formulář
        if (formSubmissionPending && token) {
            submitFormWithToken();
        }

        // Zkontrolujeme, jestli můžeme zobrazit tlačítko
        checkIfCanShowButton();
    }

    function checkIfCanShowButton() {
        // Zobrazíme tlačítko pouze pokud:
        // 1. Uplynulo minimálně 2.5 sekundy
        // 2. Máme Turnstile token NEBO uplynulo dalších 2.5 sekundy (fallback)
        if (minTimeElapsed && turnstileToken) {
            showRevealButton();
        }
    }

    function showRevealButton() {
        const revealBtn = document.getElementById('revealLuckyShop');
        if (revealBtn && revealBtn.style.display !== 'block') {
            revealBtn.style.display = 'block';
            setTimeout(() => {
                revealBtn.classList.add('slide-up-active');
            }, 50);
        }
    }

    function submitFormWithToken() {
        const form = document.querySelector('#revealLuckyShop').closest('form');
        if (form && turnstileToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = 'cf-turnstile-response';
            tokenInput.value = turnstileToken;
            form.appendChild(tokenInput);
            console.log('Submitting form with token');
            formSubmissionPending = false;
            if (submitTimeout) clearTimeout(submitTimeout);
            form.submit();
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        const revealBtn = document.getElementById('revealLuckyShop')
        const loader = document.getElementById('revealLoader')

        if (!revealBtn || !loader) return

        setTimeout(() => {
            loader.style.display = 'none'
            minTimeElapsed = true;

            setTimeout(() => {
                // Zkontrolujeme, jestli můžeme zobrazit tlačítko
                checkIfCanShowButton();

                // Fallback - pokud nemáme token ani po 5 sekundách, zobrazíme tlačítko
                setTimeout(() => {
                    if (!turnstileToken) {
                        console.log('Turnstile fallback - showing button without token');
                        showRevealButton();
                    }
                }, 2500);
            }, 300)
        }, 2500)

        const form = revealBtn.closest('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Vždy zastavíme první odeslání

                if (turnstileToken) {
                    // Máme token, můžeme odeslat hned
                    submitFormWithToken();
                } else {
                    // Nemáme token, počkáme na něj (max 3 sekundy)
                    console.log('Waiting for Turnstile token...');
                    formSubmissionPending = true;

                    // Fallback - pokud token nepřijde do 3 sekund, odešleme bez něj
                    submitTimeout = setTimeout(function() {
                        console.log('Turnstile timeout, submitting without token');
                        formSubmissionPending = false;
                        form.submit();
                    }, 3000);
                }
            });
        }
    })
</script>

<style>
    .slide-up-enter {
        transform: translateY(50px);
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-up-active {
        transform: translateY(0);
        opacity: 1;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
