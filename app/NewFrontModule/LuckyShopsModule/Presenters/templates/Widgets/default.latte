{block head}
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
        crossorigin="anonymous"></script>
{/block}

{block content}
<div class="bg-[#182B4A]" style="padding: 30px">

    <div  class='gap-5' style="display: grid; grid-template-columns: repeat(5, minmax(0, 1fr));">
    {foreach $userWidgets as $userWidget}
        <div>
            {var $widgetType = $userWidget->getWidgetType()}

            {include "./snippets/" . $widgetType->getCamelCaseType() . "Widget.latte", widgetType: $widgetType, userWidget: $userWidget}
        </div>
    {/foreach}
</div>
</div>

<style>
    .bg-lucky-shops {
        position: relative;
        width: 100%;
        min-height: 1119px;
        height: auto;
        z-index: 20;
    }

    .bg-image {
        background-image: url(/new-design/bg-lucky-shops.svg);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
    }
</style>
