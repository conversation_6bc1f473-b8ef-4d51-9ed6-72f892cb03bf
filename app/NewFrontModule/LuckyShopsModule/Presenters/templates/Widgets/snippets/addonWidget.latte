<div class="w-full max-w-[335px] rounded-2xl" style="background: linear-gradient(180deg, #FFF 46.7%, #ECEDF0 89.22%);">
    <div class="relative bg-[#15243E] max-w-fit mx-auto">
        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
            <linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="#FFFFFF" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
        </svg>

        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
            <linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="#FFFFFF" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
        </svg>


        <div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
            {_newFront.luckyShops.widgets.addon.header}
        </div>
    </div>

    <div class="pt-[25px] px-[30px] font-bold leading-7 text-center mb-2">
        {_newFront.luckyShops.widgets.addon.text}
    </div>

    <div class="bg-white pt-[27px] pb-10 w-[200px] rounded-2xl mx-auto bottom-[-29px] relative rotate-[4.5deg]">
        <img class="mx-auto my-[14px] w-[67px]" src="/new-design/chrome.png" alt="widget">
        <img class="mx-auto" src="/new-design/widget-stars.svg" alt="widget-stars">
        <div class="text-dark-2 text-sm leading-[24.5px] text-center">{_newFront.luckyShops.widgets.addon.promo.text}</div>

        <div class="absolute -top-5 -right-[28px]">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                <foreignObject x="-6" y="-6" width="84" height="84"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_8280_3070_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="6" x="1.25" y="1.25" width="69.5" height="69.5" rx="34.75" fill="#66B940" fill-opacity="0.3" stroke="#66B940" stroke-width="2.5"></rect>
                <path d="M36.4999 26C37.0016 26 37.4082 26.4067 37.4082 26.9083L37.4081 46.0917C37.4081 46.5933 37.0014 47 36.4997 47C35.9981 47 35.5914 46.5933 35.5914 46.0917L35.5916 26.9083C35.5916 26.4067 35.9983 26 36.4999 26Z" fill="#66B940"></path>
                <path d="M47 36.5001C47 35.9984 46.5933 35.5918 46.0917 35.5918L26.9083 35.5919C26.4067 35.5919 26 35.9986 26 36.5003C26 37.0019 26.4067 37.4086 26.9083 37.4086L46.0917 37.4084C46.5933 37.4084 47 37.0017 47 36.5001Z" fill="#66B940"></path>
                <foreignObject x="39.1" y="39.1" width="51.8" height="51.8"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5.45px);clip-path:url(#bgblur_1_8280_3070_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="10.9" x="50" y="50" width="30" height="30" rx="15" fill="white" fill-opacity="0.6"></rect>
                <path d="M67 68.1451C67.6348 68.6404 68.4247 68.9435 69.2637 69C72.1356 69 73.9306 66.0833 72.4946 63.75C71.8282 62.6671 70.5965 62 69.2637 62C66.5989 62 65 65.5 65 65.5C65 65.5 63.4011 69 60.7363 69C57.8644 69 56.0694 66.0833 57.5054 63.75C58.1718 62.6671 59.4035 62 60.7363 62C61.5755 62.0574 62.3653 62.3612 63 62.857" stroke="#646C7C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                <defs>
                    <clipPath id="bgblur_0_8280_3070_clip_path" transform="translate(6 6)"><rect x="1.25" y="1.25" width="69.5" height="69.5" rx="34.75"></rect>
                    </clipPath><clipPath id="bgblur_1_8280_3070_clip_path" transform="translate(-39.1 -39.1)"><rect x="50" y="50" width="30" height="30" rx="15"></rect>
                    </clipPath></defs>
            </svg>
        </div>
    </div>

    <div class="px-5 pb-5">
        <a n:href="clickWidget!, userWidgetId: $userWidget->getId()" target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover">
            {_newFront.luckyShops.widgets.addon.cta}
        </a>
    </div>
</div>