{block scripts}

<script>
	window.addEventListener('DOMContentLoaded', function() {
		document.querySelectorAll('.js-form-input-focus').forEach(function(element) {
			element.addEventListener('click', function() {
				const formInput = document.querySelector('.js-form-input');
				if (formInput) {
					const targetPosition = formInput.getBoundingClientRect().top + window.scrollY - 100;
					const startPosition = window.scrollY;
					const distance = targetPosition - startPosition;
					const duration = 1000;
					let startTime = null;

					function animateScroll(currentTime) {
						if (startTime === null) startTime = currentTime;
						const timeElapsed = currentTime - startTime;
						const run = ease(timeElapsed, startPosition, distance, duration);
						window.scrollTo(0, run);
						if (timeElapsed < duration) requestAnimationFrame(animateScroll);
						else formInput.focus();
					}

					function ease(t, b, c, d) {
						t /= d / 2;
						if (t < 1) return c / 2 * t * t + b;
						t--;
						return -c / 2 * (t * (t - 2) - 1) + b;
					}

					requestAnimationFrame(animateScroll);
				}
			});
		});
	});
</script>
{/block}

{block styles}

<link rel="preload" fetchpriority="high" as="image" href="/new-design/wp-mobile-bg-orange.avif" as="image" type="image/avif">

<style>
	.bg-img {
		left: 0;
		z-index: 10;
		position: absolute;
		width: 100%;
		height: 916px;
		background-image: url(/new-design/wp-mobile-bg-orange.avif);
		//background-color: #f2841e;
		background-repeat: no-repeat;
		background-size: cover;
		background-position: bottom;
	}

	body.no-avif .bg-img {
		background-image: url(/new-design/wp-mobile-bg-orange.svg);
		background-color: transparent;
	}

	body.avif .bg-img {
		background-color: transparent;
	}
</style>
{/block}

{block footerBackgroundColor}bg-light-6{/block}

{block content}
<div class="container overflow-hidden md:overflow-visible">
	<section class="flex flex-col items-center w-full lg:justify-between lg:flex-row">
		<div class="bg-img lg:hidden"></div>
		<div class="mt-10 lg:mt-0 z-10 px-5 lg:px-0">
			<div>
				<h1
					class="flex items-center gap-2 text-white lg:text-primary-orange font-bold leading-[35px] text-[26px] lg:text-[45px] lg:leading-[60px]">
					{_newFront.homepage.header.title}
					<span class="hidden xl:block">
						<img src="{$basePath}/new-design/hp-flying-money.png" alt="money">
					</span>
					{_newFront.homepage.header.titleSuffix}
				</h1>
				<div class="text-white lg:text-dark-1 leading-[35px] text-[26px] lg:text-[45px] lg:leading-[60px]">
					{_newFront.homepage.header.subTitle}
				</div>
			</div>

			<div class="mt-4 lg:mt-[19px] mb-[23px]">
				<img src="{$basePath}/new-design/hp-icons/waves.svg" alt="divider">
			</div>

			<div>
				<div
					class="flex items-start ld:items-center gap-3 text-sm mb-3.5 shrink-0 text-white leading-6 lg:text-dark-1 lg:text-lg lg:leading-[31.5px]">
					<svg class="hidden lg:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<circle cx="12" cy="12" r="11" stroke="#66B940" stroke-width="2" />
						<path d="M8 12.6071L10.5524 15L16 10" stroke="#66B940" stroke-width="2"
							stroke-linecap="round" />
					</svg>

					<svg class="lg:hidden flex shrink-0" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
						viewBox="0 0 20 20" fill="none">
						<circle cx="10" cy="10" r="10" fill="white" />
						<path d="M6.66699 10.5061L8.79397 12.5002L13.3337 8.3335" stroke="url(#paint0_linear_456_3504)"
							stroke-width="2" stroke-linecap="round" />
						<defs>
							<linearGradient id="paint0_linear_456_3504" x1="6.66699" y1="12.5002" x2="10.402"
								y2="7.65104" gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A" />
								<stop offset="1" stop-color="#FFA439" />
							</linearGradient>
						</defs>
					</svg>
					<div>
						{_newFront.homepage.header.block1, ['shopCount' => ($countOfCashbackShops|amount), 'percent' =>
						'26,9']|noescape}
					</div>
				</div>

				<div
					class="flex items-start ld:items-center gap-3 text-sm mb-3.5 shrink-0 text-white leading-6 lg:text-dark-1 lg:text-lg lg:leading-[31.5px]">
					<svg class="hidden lg:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<circle cx="12" cy="12" r="11" stroke="#66B940" stroke-width="2" />
						<path d="M8 12.6071L10.5524 15L16 10" stroke="#66B940" stroke-width="2"
							stroke-linecap="round" />
					</svg>

					<svg class="lg:hidden flex shrink-0" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
						viewBox="0 0 20 20" fill="none">
						<circle cx="10" cy="10" r="10" fill="white" />
						<path d="M6.66699 10.5061L8.79397 12.5002L13.3337 8.3335" stroke="url(#paint0_linear_456_3504)"
							stroke-width="2" stroke-linecap="round" />
						<defs>
							<linearGradient id="paint0_linear_456_3504" x1="6.66699" y1="12.5002" x2="10.402"
								y2="7.65104" gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A" />
								<stop offset="1" stop-color="#FFA439" />
							</linearGradient>
						</defs>
					</svg>
					{_newFront.homepage.header.block2}
				</div>

				<div
					class="flex items-center gap-3 text-sm mb-3.5 shrink-0 text-white leading-6 lg:text-dark-1 lg:text-lg lg:leading-[31.5px]">
					<svg class="hidden lg:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<circle cx="12" cy="12" r="11" stroke="#66B940" stroke-width="2" />
						<path d="M8 12.6071L10.5524 15L16 10" stroke="#66B940" stroke-width="2"
							stroke-linecap="round" />
					</svg>

					<svg class="lg:hidden flex shrink-0" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
						viewBox="0 0 20 20" fill="none">
						<circle cx="10" cy="10" r="10" fill="white" />
						<path d="M6.66699 10.5061L8.79397 12.5002L13.3337 8.3335" stroke="url(#paint0_linear_456_3504)"
							stroke-width="2" stroke-linecap="round" />
						<defs>
							<linearGradient id="paint0_linear_456_3504" x1="6.66699" y1="12.5002" x2="10.402"
								y2="7.65104" gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A" />
								<stop offset="1" stop-color="#FFA439" />
							</linearGradient>
						</defs>
					</svg>
					{_newFront.homepage.header.block3}
				</div>
			</div>

			<div class="hidden lg:flex gap-3 items-start mt-[70px]">
				<img width="32px" height="32px" src="{$basePath}/new-design/hp-icons/facebook.svg" loading="lazy" alt="facebook">
				<div class="flex-column mt-1">
					<img src="{$basePath}/new-design/hp-icons/stars.svg" loading="lazy" alt="stars">
					<div class="text-sm text-dark-4 mt-1">
						<span class="font-bold text-dark-1">{_newFront.homepage.header.rating} </span>({_newFront.homepage.header.reviewCount})
					</div>
				</div>
			</div>
		</div>


		<div class="text-center z-10 g:bg-white lg:w-[542px]">
			<div>
				<img class="hidden lg:block absolute -z-10 max-w-none" width="1470px" height="609px"
					src="{$basePath}/new-design/hp-bg-orange.svg" alt="bg-orange" loading="lazy">
			</div>

			<div
				class="bg-white max-w-[415px] px-5 rounded-2xl pb-[37px] pt-[41px] shadow-custom relative mt-[64px] lg:ml-[87px] lg:mt-[88px] md:px-[55px]">
				<img class="absolute right-[-103px] bottom-[-7px]" src="{$basePath}/new-design/hp-register-donkey.png" loading="lazy"
					alt="donkey">
				<div n:if="!isset($registrationBonusVariant) || $registrationBonusVariant === 0" class="flex absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 whitespace-nowrap">
					<div
						class="flex items-center text-sm bg-secondary-green rounded-full text-white font-bold leading-[24.5px] uppercase py-1 pl-1 pr-[15px]">
						<div class="bg-white text-secondary-green rounded-full px-3 leading-[28px] mr-1.5">
							+{_newFront.homepage.header.bonusAmount, ['bonus' => $getCampaignBonusAmount()]}
						</div>
						{_newFront.homepage.header.bonus}
					</div>
				</div>
				<div class="text-xl font-medium leading-[35px]">
					{_newFront.homepage.header.form.title}
				</div>

				{form hpEmailSignUpControl-form}

				<div class="bg-secondary-red text-white mt-3 rounded p-2" n:foreach="$form->errors as $error">
					{$error |noescape}
				</div>

				<div class="mt-[22px]">
					<input n:name="email"
						class="w-full bg-light-6 border border-solid border-light-4 pl-5 py-4 leading-[28px] rounded-xl js-form-input"
						placeholder="{_newFront.homepage.header.form.input}">
				</div>

				<div class="form-group" n:ifset="$form[recaptcha]">
					<div n:name="recaptcha"></div>
				</div>

				<input n:name="submit"
					class="w-full py-4 rounded-xl bg-orange-gradient text-white font-medium mt-2 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover"
					value="{_newFront.homepage.header.form.submit}">
				{/form}

				<div class="flex items-center justify-center py-5">
					<div class="flex-grow border-t border-light-4"></div>
					<div class="px-2 text-dark-2 text-sm leading-[24.5px]">{_newFront.homepage.header.form.or}</div>
					<div class="flex-grow border-t border-light-4"></div>
				</div>

				<div class="text-sm text-dark-1">
					{control socialLoginButtons}
				</div>

				<div class="mt-5 text-xs text-dark-3 font-normal leading-[21px] px-[22px]">
					{_'front.aqPopup.form.acceptAllConditions', [condition =>
					$presenter->link(':NewFront:Static:conditions'), privacy =>
					$presenter->link(':NewFront:Static:privacyPolicy')]|noescape}
				</div>

				<div class="text-xs mt-2.5 text-dark-2 leading-[21px]">
					{_newFront.homepage.sign.signTitle} <a n:href=":NewFront:Sign:in"
						class="text-primary-orange cursor-pointer hover:underline">{_newFront.homepage.sign.signIn}</a>
				</div>
			</div>
		</div>

		<div class="flex gap-3 items-start mt-[35px] lg:hidden">
			<img width="32px" height="32px" src="{$basePath}/new-design/hp-icons/facebook.svg" loading="lazy" alt="facebook">
			<div class="flex-column mt-1">
				<img src="{$basePath}/new-design/hp-icons/stars.svg" loading="lazy" alt="stars">
				<div class="text-sm text-dark-4 mt-1">
					<span class="font-bold text-dark-1">4,8/5 </span>(1 tis. hodnocení)
				</div>
			</div>
		</div>
	</section>

	<div class="h-px bg-light-4 mt-10 lg:hidden"></div>

    {cache 'homepage-TopShops-' . $localization->getId(), 'expiration' => '1 hour'}
        <section>
            <div class="text-center md:text-start mt-10 text-dark-1 leading-[28px] mb-5 md:mt-[50px] md:mb-3">
                {_'newFront.homepage.partners.text'}
            </div>

            <div class="grid grid-cols-3 sm:grid-cols-5 md:gap-5 lg:grid-cols-7 xl:gap-0 xl:grid-cols-9">
                {foreach $topShops(8) as $topShop}
                <a n:href=":NewFront:Shops:Shop:, $topShop"
                    class="w-[120px] h-[77px] md:border md:border-light-4 rounded-xl p-4 flex items-center justify-center shadow-hover">
                    <img class="max-h-[30px] max-w-[80px]" alt="{$topShop->getName()}"
                        src="{$topShop->getCurrentLogo() |image:160,0,'fit',false,$topShop->getName()}" />
                </a>
                {/foreach}

                <div class="bg-light-6 rounded-xl p-4 flex items-center justify-center">
                    <a class="text-sm text-center text-dark-1 leading-[24.5px] hover:cursor-pointer hover:underline"
                        n:href=":NewFront:Shops:Shops:default">
                        {_'newFront.homepage.partners.rest', ['rest' => ($countOfCashbackShops)]}
                    </a>
                </div>
            </div>
        </section>
	{/cache}

	<div class="h-px bg-light-4 mt-10 lg:hidden"></div>

	<section>
		<div
			class="leading-[49.5px] text-[33px] text-dark-1 font-bold text-center mt-10 md:mt-[100px] mb-[34px] md:mb-[70px]">
			{_newFront.homepage.howDoesItWork.title}
		</div>
		<div class="flex flex-col md:flex-row md:items-center md:justify-center">
			<div class="flex md:flex-col items-center md:text-center">
				<div>
					<img class="m-auto w-[112px] md:w-[143px]" src="{$basePath}/new-design/hp-first-step.svg"
						alt="first-step" loading="lazy">
				</div>
				<div class="flex flex-col md:block ml-4 md:ml-0">
					<div class="md:text-lg font-medium leading-[28px] md:leading-[31.5px] mb-1 md:mt-10 md:mb-2">
						{_'newFront.homepage.howDoesItWork.step1.title', ['link' => $presenter->link('this')]|noescape}
					</div>
					<div class="text-sm leading-[24.5px] md:text-base md:leading-[28px] text-dark-1">
						{_newFront.homepage.howDoesItWork.step1.description |noescape}
					</div>
				</div>
			</div>

			<div class="md:ml-[51px] md:mr-[62px] md:mt-[-129px]">
				<svg class="hidden md:block" xmlns="http://www.w3.org/2000/svg" width="102" height="17"
					viewBox="0 0 102 17" fill="none">
					<path
						d="M101.898 5.44016C102.141 4.94425 101.936 4.34517 101.44 4.10208L93.3589 0.140656C92.863 -0.102436 92.2639 0.102511 92.0208 0.598419C91.7777 1.09433 91.9827 1.6934 92.4786 1.9365L99.6619 5.45776L96.1407 12.6411C95.8976 13.137 96.1025 13.7361 96.5984 13.9792C97.0943 14.2223 97.6934 14.0173 97.9365 13.5214L101.898 5.44016ZM0.667928 5.94325C19.7673 12.6672 34.8794 16.1289 50.2717 16.1647C65.6622 16.2005 81.2572 12.811 101.324 5.94616L100.676 4.05384C80.7122 10.8836 65.3545 14.1997 50.2763 14.1647C35.1999 14.1296 20.3272 10.744 1.33207 4.05675L0.667928 5.94325Z"
						fill="#CCD0D7" />
				</svg>
				<svg class="md:hidden my-[9px] ml-[62px]" xmlns="http://www.w3.org/2000/svg" width="8" height="32"
					viewBox="0 0 8 32" fill="none">
					<path
						d="M5.45394 1.20962C5.56971 0.958918 5.46032 0.661833 5.20962 0.546062C4.95892 0.430292 4.66183 0.539677 4.54606 0.79038L5.45394 1.20962ZM4.82218 31.4673C5.08027 31.5655 5.3691 31.4359 5.46731 31.1778L7.06768 26.972C7.16588 26.7139 7.03627 26.4251 6.77818 26.3269C6.52009 26.2287 6.23126 26.3583 6.13305 26.6164L4.7105 30.3549L0.972008 28.9323C0.713918 28.8341 0.425083 28.9637 0.326877 29.2218C0.228671 29.4799 0.358282 29.7687 0.616371 29.8669L4.82218 31.4673ZM4.54606 0.79038C1.89412 6.53322 0.514371 11.1069 0.500112 15.7807C0.485857 20.4534 1.83661 25.1718 4.54382 31.2047L5.45617 30.7953C2.77443 24.8191 1.48648 20.2516 1.50011 15.7837C1.51374 11.3169 2.82847 6.89514 5.45394 1.20962L4.54606 0.79038Z"
						fill="#CCD0D7" />
				</svg>
			</div>

			<div class="flex md:flex-col md:text-center">
				<div>
					<img class="m-auto w-[112px] md:w-[143px]" src="{$basePath}/new-design/hp-second-step.svg"
						alt="second-step" loading="lazy">
				</div>

				<div class="flex flex-col md:block ml-4 md:ml-0">
					<div class="md:text-lg font-medium leading-[28px] md:leading-[31.5px] mb-1 md:mt-10 md:mb-2">
						{_newFront.homepage.howDoesItWork.step2.title |noescape}
					</div>
					<div class="text-sm leading-[24.5px] md:text-base md:leading-[28px] text-dark-1">
						{_newFront.homepage.howDoesItWork.step2.description |noescape}
					</div>
				</div>
			</div>

			<div class="md:ml-[62px] md:mr-[51px] md:mt-[-129px]">
				<svg class="hidden md:block" xmlns="http://www.w3.org/2000/svg" width="102" height="17"
					viewBox="0 0 102 17" fill="none">
					<path
						d="M101.898 10.7249C102.141 11.2208 101.936 11.8199 101.44 12.063L93.3589 16.0244C92.863 16.2675 92.2639 16.0625 92.0208 15.5666C91.7777 15.0707 91.9827 14.4716 92.4786 14.2285L99.6619 10.7073L96.1407 3.52391C95.8976 3.02801 96.1025 2.42893 96.5984 2.18584C97.0943 1.94274 97.6934 2.14769 97.9365 2.6436L101.898 10.7249ZM0.667928 10.2218C19.7673 3.49785 34.8794 0.0361719 50.2717 0.000372887C65.6622 -0.0354233 81.2572 3.35405 101.324 10.2189L100.676 12.1112C80.7122 5.28141 65.3545 1.9653 50.2763 2.00037C35.1999 2.03543 20.3272 5.42106 1.33207 12.1083L0.667928 10.2218Z"
						fill="#CCD0D7" />
				</svg>
				<svg class="md:hidden my-[9px] ml-[62px]" xmlns="http://www.w3.org/2000/svg" width="8" height="32"
					viewBox="0 0 8 32" fill="none">
					<path
						d="M2.54606 1.20962C2.43029 0.958917 2.53968 0.661832 2.79038 0.546062C3.04108 0.430292 3.33817 0.539677 3.45394 0.79038L2.54606 1.20962ZM3.17782 31.4673C2.91973 31.5655 2.63089 31.4359 2.53269 31.1778L0.932322 26.972C0.834116 26.7139 0.963726 26.4251 1.22182 26.3269C1.47991 26.2287 1.76874 26.3583 1.86695 26.6164L3.28949 30.3549L7.02799 28.9323C7.28608 28.8341 7.57491 28.9637 7.67312 29.2218C7.77133 29.4799 7.64172 29.7687 7.38363 29.8669L3.17782 31.4673ZM3.45394 0.79038C6.10588 6.53323 7.48563 11.1069 7.49989 15.7807C7.51414 20.4534 6.16338 25.1718 3.45617 31.2047L2.54382 30.7953C5.22557 24.8191 6.51352 20.2516 6.49989 15.7837C6.48626 11.3169 5.17153 6.89514 2.54606 1.20962L3.45394 0.79038Z"
						fill="#CCD0D7" />
				</svg>
			</div>

			<div class="flex md:flex-col md:text-center">
				<div>
					<img class="m-auto w-[112px] md:w-[143px]" src="{$basePath}/new-design/hp-third-step.svg"
						alt="third-step" loading="lazy">
				</div>

				<div class="flex flex-col md:block ml-4 md:ml-0">
					<div class="md:text-lg font-medium leading-[28px] md:leading-[31.5px] mb-1 md:mt-10 md:mb-2">
						{_newFront.homepage.howDoesItWork.step3.title |noescape}
					</div>
					<div class="text-sm leading-[24.5px] md:text-base md:leading-[28px] text-dark-1">
						{_newFront.homepage.howDoesItWork.step3.description |noescape}
					</div>
				</div>
			</div>
		</div>

		<div class="text-center mb-[60px] md:mb-[100px]">
			<button
				class="bg-orange-gradient rounded-xl leading-[28px] font-bold text-white px-[76px] mt-[34px] md:mt-[62px] py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover js-form-input-focus">
				{_newFront.homepage.howDoesItWork.beginButton |noescape}
			</button>
		</div>
	</section>
</div>

<div class="bg-light-6">
    <section class="container">
        <div class="pt-10 md:pt-[88px] mb-[34px] md:mb-[77px]">
            <div class="text-center text-[26px]  leading-[39px] md:text-[33px] md:leading-[49.5px] text-dark-1">
                {_newFront.homepage.shops.title |noescape}
            </div>
        </div>

        <div class="grid grid-cols-2 pb-12 gap-[15px] sm:grid-cols-4 md:gap-5 md:grid-cols-5 lg:grid-cols-6">
            {cache 'homepage-TopShops2-' . $localization->getId(), 'expiration' => '1 hour'}
                {foreach $topShops(18) as $topShop}
                    {include "../../../ShopsModule/Presenters/templates/Shops/shopItem.latte", shop: $topShop}
                {/foreach}
           {/cache}
        </div>

        <div class="flex justify-center pb-[188px]">
            <a n:href=":NewFront:Shops:Shops:default"
                class="border text-center w-full md:w-auto border-light-2 leading-[28px] font-bold px-20 rounded-xl pt-[15px] pb-[13px] xl:hover:bg-light-4">{_'newFront.homepage.shops.rest',
                ['rest' => $countOfCashbackShops]}</a>
        </div>

        <div class="w-full max-w-[564px] mx-auto flex flex-col items-center relative bottom-[-51px] md:bottom-[-74px]">
            <img src="{$basePath}/new-design/donkey-with-mark-hp.png" alt="oslík" class="absolute top-[-200px]" loading="lazy">
            {cache 'homepage-countOfTransactions-' . $localization->getId(), 'expiration' => '1 hour'}
                <div
                    class="total-reward-value text-lg md:text-[26px] leading-7 md:leading-[39px] text-white bg-secondary-green py-[26px] px-[27px] md:px-[56px] md:py-[39px] rounded-2xl text-center">
                    {_'newFront.homepage.shops.moneyBackCount', ['count' => ($countOfTransactions()|amount)]|noescape}
                </div>
            {/cache}
        </div>
    </section>
</div>


<section class="container">
	<div
		class="text-center leading-[39px] md:leading-[49.5px] text-[26px] md:text-[33px] text-dark-1 font-bold mt-[144px] mb-[34px] md:mb-[70px]">
		{_'newFront.homepage.review.title'}
	</div>

	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-[67px] relative">
		<svg class="hidden md:block absolute right-[-60px] bottom-[-40px] z-10" xmlns="http://www.w3.org/2000/svg"
			width="298" height="223" viewBox="0 0 298 223" fill="none">
			<path
				d="M77.1479 223C62.9429 223 49.9624 219.315 38.2066 211.945C26.4507 204.083 17.144 193.52 10.2864 180.253C3.4288 166.496 0 150.527 0 132.347C0 118.098 2.93897 104.095 8.8169 90.3377C14.6948 76.0888 22.777 62.8225 33.0634 50.539C43.8396 37.7641 56.3302 26.7089 70.5352 17.3734C79.9529 11.1841 89.9088 5.96672 100.403 1.72115C110.149 -2.22175 121.103 1.39038 127.64 9.62464C138.147 22.8606 133.124 42.3088 119.049 51.6625C111.656 56.5761 105.036 61.852 99.1901 67.4903C86.9445 79.7738 80.8216 90.3377 80.8216 99.1818C80.8216 105.078 83.0258 110.483 87.4343 115.396C91.8427 120.31 96.741 125.223 102.129 130.136C113.885 138.489 121.722 146.351 125.641 153.721C130.049 160.6 132.254 168.215 132.254 176.568C132.254 190.817 127.11 202.118 116.824 210.471C106.538 218.824 93.3122 223 77.1479 223ZM241.73 223C227.525 223 214.545 219.315 202.789 211.945C191.033 204.083 181.726 193.52 174.869 180.253C168.011 166.496 164.582 150.527 164.582 132.347C164.582 118.098 167.521 104.095 173.399 90.3377C179.277 76.0888 187.359 62.8225 197.646 50.539C208.422 37.7641 220.912 26.7089 235.117 17.3734C244.535 11.1841 254.491 5.96672 264.985 1.72115C274.731 -2.22175 285.685 1.39038 292.222 9.62464C302.729 22.8606 297.706 42.3088 283.631 51.6625C276.238 56.5761 269.618 61.852 263.772 67.4903C251.527 79.7738 245.404 90.3377 245.404 99.1818C245.404 105.078 247.608 110.483 252.016 115.396C256.425 120.31 261.323 125.223 266.711 130.136C278.467 138.489 286.304 146.351 290.223 153.721C294.631 160.6 296.836 168.215 296.836 176.568C296.836 190.817 291.693 202.118 281.406 210.471C271.12 218.824 257.894 223 241.73 223Z"
				fill="#F0F8EC" />
		</svg>
		<svg class="md:hidden absolute right-[-17px] bottom-[-40px] z-10" xmlns="http://www.w3.org/2000/svg" width="132"
			height="100" viewBox="0 0 132 100" fill="none">
			<path
				d="M34.0141 100C27.7512 100 22.0282 98.3604 16.8451 95.0812C11.662 91.5833 7.55869 86.8831 4.53521 80.9805C1.51174 74.8593 0 67.7543 0 59.6656C0 53.3258 1.29577 47.0952 3.88732 40.974C6.47887 34.6342 10.0423 28.7316 14.5775 23.2662C19.3286 17.5823 24.8357 12.6634 31.0986 8.50975C35.2191 5.77695 39.5733 3.46998 44.1613 1.58884C48.5128 -0.195347 53.4167 1.42764 56.3245 5.12409C60.9468 11.0002 58.7212 19.5893 52.5104 23.7512C49.2416 25.9417 46.3156 28.2941 43.7324 30.8084C38.3333 36.2738 35.6338 40.974 35.6338 44.9091C35.6338 47.5325 36.6056 49.9372 38.5493 52.1234C40.493 54.3095 42.6526 56.4957 45.0282 58.6818C50.2113 62.3983 53.6667 65.8961 55.3944 69.1753C57.338 72.2359 58.3099 75.6245 58.3099 79.3409C58.3099 85.6807 56.0423 90.7089 51.507 94.4253C46.9718 98.1418 41.1408 100 34.0141 100ZM106.577 100C100.315 100 94.5916 98.3604 89.4085 95.0812C84.2253 91.5833 80.1221 86.8831 77.0986 80.9805C74.0751 74.8593 72.5634 67.7543 72.5634 59.6656C72.5634 53.3258 73.8592 47.0952 76.4507 40.974C79.0423 34.6342 82.6056 28.7316 87.1409 23.2662C91.892 17.5823 97.3991 12.6634 103.662 8.50975C107.782 5.77695 112.137 3.46998 116.725 1.58884C121.076 -0.195347 125.98 1.42764 128.888 5.12409C133.51 11.0002 131.285 19.5893 125.074 23.7512C121.805 25.9417 118.879 28.2941 116.296 30.8084C110.897 36.2738 108.197 40.974 108.197 44.9091C108.197 47.5325 109.169 49.9372 111.113 52.1234C113.056 54.3095 115.216 56.4957 117.592 58.6818C122.775 62.3983 126.23 65.8961 127.958 69.1753C129.901 72.2359 130.873 75.6245 130.873 79.3409C130.873 85.6807 128.606 90.7089 124.07 94.4253C119.535 98.1418 113.704 100 106.577 100Z"
				fill="#F0F8EC" />
		</svg>
		<div class="bg-secondary-light-green px-[30px] pb-6 pt-[30px] rounded-2xl border border-light-4">
			<div class="flex justify-between mb-4">
				<div class="flex gap-3">
					<img src="{$basePath}/new-design/hp-icons/account.svg" alt="account" loading="lazy">
					<div class="text-dark-1 font-medium leading-[28px]">{_'newFront.homepage.review.facebook.name'}
					</div>
				</div>
				<svg xmlns="http://www.w3.org/2000/svg" width="96" height="18" viewBox="0 0 96 18" fill="none">
					<path
						d="M84.4398 16.9553H87.6341V11.9972L92.0914 16.9553H96L90.8109 11.2411L95.2436 6.28641H91.6869L87.6341 10.8531V0.771484L84.4398 1.17316V16.9553ZM77.073 6.01312C73.5268 6.01312 71.0642 8.30144 71.0642 11.6192C71.0642 14.937 73.5268 17.2253 77.073 17.2253C80.6191 17.2253 83.0818 14.937 83.0818 11.6192C83.0818 8.30144 80.6191 6.01312 77.073 6.01312ZM77.073 14.613C75.43 14.613 74.3043 13.3945 74.3043 11.6192C74.3043 9.8439 75.43 8.62546 77.073 8.62546C78.7159 8.62546 79.8417 9.8439 79.8417 11.6192C79.8417 13.3945 78.7159 14.613 77.073 14.613ZM64.0914 6.01312C60.5453 6.01312 58.0826 8.30144 58.0826 11.6192C58.0826 14.937 60.5453 17.2253 64.0914 17.2253C67.6376 17.2253 70.1002 14.937 70.1002 11.6192C70.1002 8.30144 67.6376 6.01312 64.0914 6.01312ZM64.0914 14.613C62.4486 14.613 61.3228 13.3945 61.3228 11.6192C61.3228 9.8439 62.4486 8.62546 64.0914 8.62546C65.7344 8.62546 66.8601 9.8439 66.8601 11.6192C66.8601 13.3945 65.7344 14.613 64.0914 14.613ZM51.8662 6.01312C50.3887 6.01312 49.1644 6.56999 48.3975 7.58926V0.771484L45.1996 1.16973V16.9519H48.0352L48.0914 15.3049C48.8478 16.5334 50.2022 17.2253 51.8662 17.2253C54.8989 17.2253 57.1187 14.8627 57.1187 11.6192C57.1187 8.37566 54.9094 6.01312 51.8662 6.01312ZM51.1099 14.613C49.467 14.613 48.3413 13.3945 48.3413 11.6192C48.3413 9.8439 49.467 8.62546 51.1099 8.62546C52.7528 8.62546 53.8786 9.8439 53.8786 11.6192C53.8786 13.3945 52.7528 14.613 51.1099 14.613ZM38.7968 14.7513C37.1011 14.7513 35.8839 13.9953 35.4688 12.6925H43.3386C43.416 12.2943 43.4688 11.8217 43.4688 11.5044C43.4688 8.07191 41.4669 6.01312 38.1037 6.01312C34.7089 6.01312 32.3588 8.30144 32.3588 11.6192C32.3588 14.9808 34.8215 17.2253 38.4767 17.2253C40.3693 17.2253 42.2093 16.6178 43.3597 15.6086L42.2093 13.6646C41.0941 14.4037 39.9999 14.7513 38.7968 14.7513ZM38.0826 8.39593C39.5602 8.39593 40.503 9.25656 40.503 10.5898V10.5999H35.4019C35.708 9.18577 36.6613 8.39593 38.0826 8.39593ZM27.3562 17.2286C29.0097 17.2286 30.6279 16.6211 31.7677 15.5681L30.5188 13.5532C29.6323 14.2147 28.6262 14.5724 27.6728 14.5724C25.8997 14.5724 24.7177 13.3843 24.7177 11.6225C24.7177 9.86074 25.8997 8.67266 27.6728 8.67266C28.5487 8.67266 29.5021 8.97641 30.2761 9.51312L31.5461 7.45422C30.5188 6.56324 28.9287 6.01644 27.3526 6.01644C23.8839 6.01644 21.4318 8.33855 21.4318 11.6225C21.4353 14.8964 23.8873 17.2286 27.3562 17.2286ZM17.1996 6.28641L17.1434 7.94706C16.387 6.70836 15.0325 6.01644 13.3685 6.01644C10.3254 6.01644 8.11606 8.37909 8.11606 11.6225C8.11606 14.8661 10.3395 17.2286 13.3685 17.2286C15.0325 17.2286 16.3904 16.5367 17.1434 15.3082L17.1996 16.9552H20.0351V6.28641H17.1996ZM14.1248 14.613C12.482 14.613 11.3562 13.3945 11.3562 11.6192C11.3562 9.8439 12.482 8.62546 14.1248 8.62546C15.7678 8.62546 16.8935 9.8439 16.8935 11.6192C16.8935 13.3945 15.7642 14.613 14.1248 14.613ZM7.87681 6.30647H5.00261V5.39511C5.00261 4.03156 5.55846 3.50847 7.00087 3.50847C7.44762 3.50847 7.80996 3.51855 8.01756 3.53882V1.19988C7.62348 1.09519 6.66313 0.990618 6.10728 0.990618C3.16969 0.990618 1.81526 2.32039 1.81526 5.19261V6.30304H0V8.88171H1.81526V16.9517H5.00607V8.88503H7.38434L7.87681 6.30647Z"
						fill="#1877F2" />
				</svg>
			</div>
			<div class="mb-5">
				<img src="{$basePath}/new-design/hp-icons/full-stars.svg" alt="stars" loading="lazy">
			</div>

			<div class="leading-[28px] text-sm md:text-base">
			    {_'newFront.homepage.review.facebook.description', ['bonus' => $getCampaignBonusAmount()] |noescape}
			</div>
		</div>

		<div class="bg-secondary-light-green px-[30px] pb-6 pt-[30px] rounded-2xl border border-light-4">
			<div class="flex justify-between mb-4">
				<div class="flex gap-3">
					<img src="{$basePath}/new-design/hp-icons/account.svg" alt="account" loading="lazy">
					<div class="text-dark-1 font-medium leading-[28px]">{_'newFront.homepage.review.google.name'}</div>
				</div>
				<svg xmlns="http://www.w3.org/2000/svg" width="96" height="22" viewBox="0 0 96 22" fill="none">
					<path
						d="M48.4946 9.0143C46.5154 9.0143 44.9485 10.4971 44.9485 12.5566C44.9485 14.5336 46.5154 16.0988 48.4946 16.0988C50.4739 16.0988 52.0412 14.616 52.0412 12.5566C52.0412 10.4971 50.4739 9.0143 48.4946 9.0143ZM48.4946 14.616C47.4224 14.616 46.5154 13.7098 46.5154 12.4742C46.5154 11.2385 47.4224 10.3324 48.4946 10.3324C49.5669 10.3324 50.4739 11.1561 50.4739 12.4742C50.4739 13.7922 49.5669 14.616 48.4946 14.616ZM40.8243 9.0143C38.8451 9.0143 37.2782 10.4971 37.2782 12.5566C37.2782 14.5336 38.8451 16.0988 40.8243 16.0988C42.8041 16.0988 44.371 14.616 44.371 12.5566C44.371 10.4971 42.8041 9.0143 40.8243 9.0143ZM40.8243 14.616C39.7526 14.616 38.8451 13.7098 38.8451 12.4742C38.8451 11.2385 39.7526 10.3324 40.8243 10.3324C41.8966 10.3324 42.8041 11.1561 42.8041 12.4742C42.8041 13.7922 41.8966 14.616 40.8243 14.616ZM31.6699 10.0852V11.568H35.2163C35.1338 12.3918 34.8039 13.0508 34.3915 13.4627C33.8967 13.9569 33.0719 14.5336 31.6699 14.5336C29.4431 14.5336 27.7936 12.8037 27.7936 10.5795C27.7936 8.35532 29.5255 6.62537 31.6699 6.62537C32.8245 6.62537 33.7317 7.11962 34.3915 7.69629L35.4637 6.62537C34.5565 5.80161 33.4018 5.14258 31.7524 5.14258C28.7833 5.14258 26.2266 7.61392 26.2266 10.5795C26.2266 13.5451 28.7833 16.0164 31.7524 16.0164C33.4018 16.0164 34.5565 15.5222 35.5462 14.4512C36.5359 13.4627 36.8657 12.0623 36.8657 10.9914C36.8657 10.6619 36.8657 10.3324 36.7833 10.0852H31.6699ZM69.1135 11.2385C68.7835 10.4147 67.9584 9.0143 66.1444 9.0143C64.3299 9.0143 62.8454 10.4147 62.8454 12.5566C62.8454 14.5336 64.3299 16.0988 66.3091 16.0988C67.876 16.0988 68.8659 15.1103 69.1958 14.5336L68.0412 13.7098C67.6289 14.2865 67.1338 14.6983 66.3091 14.6983C65.4845 14.6983 64.9893 14.3689 64.577 13.6275L69.2782 11.6504L69.1135 11.2385ZM64.3299 12.3918C64.3299 11.0738 65.4021 10.3324 66.1444 10.3324C66.7214 10.3324 67.299 10.6619 67.4637 11.0738L64.3299 12.3918ZM60.4533 15.7693H62.0202V5.47209H60.4533V15.7693ZM57.9794 9.7557C57.5666 9.34382 56.9072 8.93194 56.0821 8.93194C54.3504 8.93194 52.7007 10.4971 52.7007 12.4742C52.7007 14.4512 54.2681 15.934 56.0821 15.934C56.9072 15.934 57.5666 15.5222 57.8966 15.1103H57.9794V15.6045C57.9794 16.9225 57.2371 17.6639 56.0821 17.6639C55.1751 17.6639 54.5152 17.005 54.3504 16.4283L53.0306 17.005C53.4429 17.9111 54.4328 19.0644 56.1649 19.0644C57.9794 19.0644 59.464 17.9935 59.464 15.4398V9.17909H57.9794V9.7557ZM56.1649 14.616C55.0927 14.616 54.1852 13.7098 54.1852 12.4742C54.1852 11.2385 55.0927 10.3324 56.1649 10.3324C57.2371 10.3324 58.0618 11.2385 58.0618 12.4742C58.0618 13.7098 57.2371 14.616 56.1649 14.616ZM76.2886 5.47209H72.5772V15.7693H74.1441V11.8975H76.2886C78.0202 11.8975 79.67 10.6619 79.67 8.68484C79.67 6.70774 78.0202 5.47209 76.2886 5.47209ZM76.371 10.4971H74.1441V6.95489H76.371C77.5256 6.95489 78.1855 7.94344 78.1855 8.68484C78.1031 9.5086 77.4432 10.4971 76.371 10.4971ZM85.8557 9.0143C84.7007 9.0143 83.5461 9.5086 83.1338 10.5795L84.5359 11.1561C84.8659 10.5795 85.3606 10.4147 85.9381 10.4147C86.7628 10.4147 87.505 10.909 87.5874 11.7328V11.8152C87.3403 11.6504 86.6804 11.4033 86.0205 11.4033C84.5359 11.4033 83.0514 12.227 83.0514 13.7098C83.0514 15.1103 84.2884 16.0164 85.6082 16.0164C86.6804 16.0164 87.1751 15.5222 87.5874 15.0279H87.6698V15.8517H89.1543V11.8975C88.9896 10.0029 87.5874 9.0143 85.8557 9.0143ZM85.6905 14.616C85.1958 14.616 84.4536 14.3689 84.4536 13.7098C84.4536 12.8861 85.3606 12.6389 86.1028 12.6389C86.7627 12.6389 87.0927 12.8037 87.505 12.9684C87.3403 13.957 86.5152 14.616 85.6905 14.616ZM94.3502 9.17909L92.6185 13.6275H92.5362L90.7217 9.17909H89.0719L91.7939 15.4398L90.2265 18.8996H91.7939L96 9.17909H94.3502ZM80.4946 15.7693H82.0615V5.47209H80.4946V15.7693Z"
						fill="#606368" />
					<path
						d="M0.494831 21.0889C0.164928 20.9242 0 20.5123 0 20.1004C0 20.018 0 19.8532 0 19.7709C0 13.6749 0 7.57898 0 1.56542C0 1.23591 0.0824641 0.988805 0.164928 0.659289C0.24744 0.494507 0.412368 0.329773 0.577343 0.165039C4.12372 3.70725 7.58763 7.24946 11.0515 10.7917C7.50517 14.0868 4.04126 17.5467 0.494831 21.0889Z"
						fill="#5286ED" />
					<path
						d="M14.5153 7.16684C13.5256 8.15539 12.4535 9.14388 11.4638 10.2148C8.16481 6.83732 4.78336 3.45984 1.48438 0.0823671C1.48438 0.0823671 1.48437 0 1.56684 0C5.85553 2.38893 10.1442 4.77791 14.5153 7.16684Z"
						fill="#58A55D" />
					<path
						d="M1.48438 21.0884C4.78336 17.7933 8.16481 14.4982 11.4638 11.2031C12.371 12.1093 13.3607 13.0154 14.4329 14.0039C10.1442 16.3929 5.85553 18.7818 1.56684 21.0884H1.48438Z"
						fill="#D85040" />
					<path
						d="M15.0105 13.6744C13.9383 12.6859 12.9486 11.6973 11.8765 10.7088C12.9486 9.63792 14.0208 8.56701 15.0929 7.49609C15.4228 7.66083 15.7527 7.82561 16.0826 8.07271C16.9074 8.56701 17.8146 8.97889 18.6394 9.47314C18.8868 9.55551 19.0517 9.72029 19.2167 9.96739C19.5466 10.3793 19.5466 10.7912 19.2167 11.2031C19.0517 11.3678 18.8868 11.5326 18.6394 11.615C17.4847 12.3564 16.2476 13.0154 15.0105 13.6744Z"
						fill="#F2BE42" />
				</svg>
			</div>
			<div class="mb-5">
				<img src="{$basePath}/new-design/hp-icons/full-stars.svg" alt="stars" loading="lazy">
			</div>

			<div class="leading-[28px] text-sm md:text-base">
				{_'newFront.homepage.review.google.description'}
			</div>
		</div>

		<div
			class="bg-secondary-light-green px-[30px] pb-6 pt-[30px] rounded-2xl border border-light-4 relative z-20 backdrop-blur-sm">
			<div class="flex justify-between mb-4">
				<div class="flex gap-3">
					<img src="{$basePath}/new-design/hp-icons/account.svg" alt="account" loading="lazy">
					<div class="text-dark-1 font-medium leading-[28px]">{_'newFront.homepage.review.apple.name'}</div>
				</div>
				<svg xmlns="http://www.w3.org/2000/svg" width="96" height="23" viewBox="0 0 96 23" fill="none">
					<path
						d="M15.2196 11.8927C15.1959 9.06853 17.5356 7.69194 17.6445 7.63043C16.3184 5.70034 14.2629 5.43543 13.5382 5.4165C11.8096 5.23677 10.1377 6.4478 9.25679 6.4478C8.36164 6.4478 7.00707 5.43543 5.54364 5.46384C3.66336 5.49219 1.90626 6.58026 0.944828 8.26435C-1.04436 11.7035 0.438071 16.7557 2.34673 19.5373C3.29867 20.8997 4.4117 22.4183 5.87044 22.3662C7.29599 22.3094 7.83119 21.4579 9.55041 21.4579C11.2554 21.4579 11.7574 22.3662 13.2446 22.3331C14.7744 22.3094 15.7406 20.9659 16.6594 19.5941C17.7629 18.033 18.2034 16.4956 18.2223 16.4199C18.1844 16.3962 15.248 15.2751 15.2196 11.8927ZM12.4111 3.58577C13.1783 2.62547 13.704 1.32457 13.5572 0C12.4489 0.0472805 11.0565 0.766332 10.2608 1.70302C9.55516 2.53086 8.92048 3.88379 9.08629 5.15633C10.3271 5.2462 11.6106 4.52715 12.4111 3.58577Z"
						fill="#080B10" />
					<path
						d="M35.8781 18.3398H33.8842L32.7901 14.9148H28.9965L27.9545 18.3398H26.0127L29.7732 6.67416H32.0939L35.8781 18.3398ZM32.4633 13.4767L31.4735 10.4303C31.3693 10.118 31.1703 9.38481 30.8862 8.23054H30.853C30.7394 8.72726 30.5499 9.4605 30.2989 10.4303L29.328 13.4767H32.4633ZM45.5306 14.0302C45.5306 15.4589 45.142 16.5895 44.3653 17.4221C43.6688 18.1601 42.8023 18.529 41.7698 18.529C40.652 18.529 39.8518 18.1317 39.3641 17.3369V21.7506H37.4932V12.6915C37.4932 11.7927 37.4697 10.8702 37.4222 9.92881H39.07L39.1747 11.2629H39.2074C39.8329 10.26 40.7801 9.75377 42.0491 9.75377C43.0438 9.75377 43.8726 10.1464 44.5359 10.9317C45.1987 11.717 45.5306 12.753 45.5306 14.0302ZM43.6264 14.1012C43.6264 13.2828 43.4416 12.6064 43.0724 12.0765C42.6695 11.523 42.1252 11.2486 41.443 11.2486C40.9839 11.2486 40.5621 11.4048 40.1878 11.7075C39.814 12.015 39.5674 12.4171 39.454 12.9091C39.4019 13.0936 39.3733 13.2875 39.3687 13.4767V14.8817C39.3687 15.492 39.5582 16.0076 39.9325 16.4334C40.3063 16.8544 40.7944 17.0673 41.3955 17.0673C42.1012 17.0673 42.6506 16.7929 43.0438 16.2536C43.4273 15.7049 43.6264 14.9905 43.6264 14.1012ZM55.2205 14.0302C55.2205 15.4589 54.8324 16.5895 54.0552 17.4221C53.3592 18.1601 52.4927 18.529 51.4601 18.529C50.3424 18.529 49.5417 18.1317 49.054 17.3369V21.7506H47.183V12.6915C47.183 11.7927 47.1596 10.8702 47.1121 9.92881H48.7604L48.8646 11.2629H48.8978C49.5228 10.26 50.47 9.75377 51.7395 9.75377C52.7342 9.75377 53.563 10.1464 54.2258 10.9317C54.8845 11.717 55.2205 12.753 55.2205 14.0302ZM53.3117 14.1012C53.3117 13.2828 53.1274 12.6064 52.7577 12.0765C52.3553 11.523 51.8104 11.2486 51.1287 11.2486C50.6692 11.2486 50.2474 11.4048 49.8685 11.7075C49.4947 12.015 49.2481 12.4171 49.1347 12.9091C49.0775 13.1409 49.0448 13.3301 49.0448 13.4767V14.8817C49.0448 15.492 49.2343 16.0076 49.6081 16.4334C49.9824 16.8544 50.47 17.0673 51.0767 17.0673C51.7818 17.0673 52.3313 16.7929 52.7245 16.2536C53.1177 15.7049 53.3117 14.9905 53.3117 14.1012ZM66.0521 15.071C66.0521 16.0644 65.7064 16.8733 65.015 17.493C64.2573 18.1742 63.1916 18.5148 61.8322 18.5148C60.5771 18.5148 59.5686 18.2736 58.8057 17.7863L59.2367 16.23C60.0562 16.7172 60.9606 16.9585 61.941 16.9585C62.6467 16.9585 63.1961 16.7977 63.5893 16.4807C63.9825 16.1637 64.1812 15.738 64.1812 15.2082C64.1812 14.7351 64.0157 14.3377 63.6935 14.0113C63.3713 13.6896 62.8361 13.3869 62.0835 13.1125C60.0373 12.3509 59.014 11.2345 59.014 9.77274C59.014 8.81713 59.374 8.03188 60.0986 7.42162C60.8186 6.81136 61.7755 6.50391 62.9689 6.50391C64.03 6.50391 64.9155 6.68839 65.6212 7.05735L65.1524 8.58063C64.4891 8.2211 63.7456 8.04606 62.9071 8.04606C62.2489 8.04606 61.7281 8.20687 61.3584 8.52855C61.0458 8.81713 60.8896 9.16723 60.8896 9.58352C60.8896 10.0423 61.0698 10.4255 61.4298 10.7236C61.7424 10.998 62.3056 11.3007 63.1252 11.6224C64.1291 12.0292 64.868 12.4975 65.3418 13.0416C65.8152 13.5856 66.0521 14.262 66.0521 15.071ZM72.2564 11.3338H70.1914V15.4163C70.1914 16.457 70.556 16.9726 71.2857 16.9726C71.6217 16.9726 71.8964 16.9443 72.119 16.8875L72.1711 18.3067C71.8019 18.4439 71.3189 18.5148 70.7173 18.5148C69.9784 18.5148 69.4004 18.2878 68.9837 17.8384C68.567 17.3889 68.3587 16.632 68.3587 15.5724V11.3338H67.1321V9.93356H68.3587V8.39141L70.1965 7.83791V9.93356H72.261L72.2564 11.3338ZM81.5439 14.0681C81.5439 15.3595 81.1747 16.4239 80.4358 17.2518C79.6637 18.108 78.6363 18.5338 77.3526 18.5338C76.1163 18.5338 75.1313 18.1222 74.397 17.3086C73.6632 16.4902 73.2986 15.4542 73.2986 14.21C73.2986 12.9044 73.6775 11.84 74.4353 11.0074C75.1931 10.1748 76.2113 9.76325 77.4945 9.76325C78.7308 9.76325 79.7255 10.1748 80.4736 10.9932C81.1839 11.7832 81.5439 12.8098 81.5439 14.0681ZM79.6024 14.1107C79.6024 13.3396 79.4365 12.6773 79.1 12.1286C78.7073 11.4615 78.1481 11.1257 77.419 11.1257C76.6704 11.1257 76.0928 11.4615 75.7042 12.1286C75.3682 12.682 75.2023 13.3538 75.2023 14.1485C75.2023 14.9196 75.3682 15.5819 75.7042 16.1354C76.1071 16.8023 76.6755 17.1383 77.4047 17.1383C78.1201 17.1383 78.6787 16.7977 79.0862 16.1212C79.4319 15.5488 79.6024 14.8817 79.6024 14.1107ZM87.6302 11.5751C87.4356 11.542 87.237 11.523 87.0378 11.523C86.3796 11.523 85.8731 11.769 85.5131 12.2658C85.2006 12.7057 85.0443 13.2592 85.0443 13.9262V18.3398H83.1733V12.578C83.1733 11.6933 83.159 10.8134 83.1212 9.92881H84.7507L84.8166 11.5372H84.8686C85.0678 10.9837 85.3757 10.5391 85.8067 10.2032C86.1948 9.90989 86.6732 9.75377 87.1609 9.75377C87.3366 9.75377 87.4877 9.768 87.6302 9.78692V11.5751ZM95.999 13.737C96.0036 14.0208 95.9796 14.3046 95.9326 14.5837H90.3156C90.3345 15.4163 90.6092 16.0502 91.1301 16.4901C91.6035 16.8828 92.2147 17.0767 92.9679 17.0767C93.8012 17.0767 94.559 16.9443 95.2412 16.6794L95.5349 17.9755C94.7388 18.3209 93.7966 18.4959 92.7121 18.4959C91.4048 18.4959 90.3815 18.1127 89.6334 17.3464C88.8899 16.58 88.5156 15.5488 88.5156 14.2573C88.5156 12.9895 88.8613 11.9346 89.5578 11.0926C90.2824 10.1937 91.2675 9.74433 92.5037 9.74433C93.716 9.74433 94.6346 10.1937 95.2601 11.0926C95.7478 11.8069 95.999 12.6867 95.999 13.737ZM94.2133 13.2544C94.2276 12.701 94.1046 12.2232 93.8487 11.8163C93.5265 11.296 93.0246 11.0358 92.3566 11.0358C91.7459 11.0358 91.2486 11.2912 90.8646 11.7974C90.552 12.1995 90.3677 12.6867 90.3105 13.2497L94.2133 13.2544Z"
						fill="#080B10" />
				</svg>
			</div>
			<div class="mb-5">
				<img src="{$basePath}/new-design/hp-icons/full-stars.svg" alt="stars" loading="lazy">
			</div>

			<div class="leading-[28px] text-sm md:text-base">
				{_'newFront.homepage.review.apple.description'}
			</div>
		</div>
	</div>

	<div class="flex justify-center gap-6 md:gap-[81px] mb-[60px] md:mb-[130px] flex-wrap">
		<div class="flex flex-col items-center">
			<div class="mb-2">
				<img src="{$basePath}/new-design/hp-icons/green-stars.svg" alt="green-stars" loading="lazy">
			</div>
			<div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
				<span class="font-medium text-sm">4,8/5</span> ({_'newFront.homepage.review.apple.count'})
			</div>
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="108" height="26" viewBox="0 0 108 26" fill="none">
					<path
						d="M17.0515 14.2646C17.025 11.1005 19.6462 9.5582 19.7682 9.48929C18.2825 7.32689 15.9796 7.03009 15.1678 7.00889C13.231 6.80752 11.3579 8.16431 10.371 8.16431C9.36808 8.16431 7.85047 7.03009 6.21089 7.06191C4.1043 7.09369 2.1357 8.31271 1.05855 10.1995C-1.17007 14.0526 0.490798 19.7129 2.6292 22.8293C3.69571 24.3557 4.94271 26.0571 6.57703 25.9987C8.17417 25.9351 8.77378 24.9811 10.6999 24.9811C12.6102 24.9811 13.1726 25.9987 14.8388 25.9616C16.5527 25.9351 17.6352 24.4299 18.6645 22.8929C19.9009 21.144 20.3944 19.4215 20.4156 19.3367C20.3732 19.3101 17.0833 18.0541 17.0515 14.2646ZM13.9049 4.9578C14.7645 3.88191 15.3535 2.42443 15.189 0.94043C13.9473 0.993401 12.3873 1.799 11.4958 2.84843C10.7053 3.77591 9.99419 5.29169 10.18 6.7174C11.5702 6.81809 13.0081 6.01249 13.9049 4.9578Z"
						fill="#BDC2CC" />
					<path
						d="M40.197 21.4867H37.9631L36.7374 17.6495H32.4871L31.3197 21.4867H29.1442L33.3573 8.41696H35.9573L40.197 21.4867ZM36.3712 16.0383L35.2622 12.6252C35.1455 12.2754 34.9226 11.4539 34.6042 10.1607H34.5671C34.4397 10.7172 34.2275 11.5387 33.9463 12.6252L32.8585 16.0383H36.3712ZM51.0113 16.6584C51.0113 18.2591 50.576 19.5258 49.7058 20.4586C48.9254 21.2854 47.9546 21.6987 46.7978 21.6987C45.5455 21.6987 44.649 21.2535 44.1026 20.3631V25.308H42.0065V15.1586C42.0065 14.1516 41.9801 13.118 41.9269 12.0634H43.7731L43.8904 13.558H43.927C44.6278 12.4344 45.6891 11.8672 47.1107 11.8672C48.2252 11.8672 49.1537 12.3072 49.8969 13.187C50.6395 14.0668 51.0113 15.2275 51.0113 16.6584ZM48.878 16.7379C48.878 15.8211 48.6709 15.0632 48.2572 14.4696C47.8059 13.8495 47.196 13.542 46.4317 13.542C45.9173 13.542 45.4448 13.717 45.0254 14.0562C44.6067 14.4007 44.3303 14.8512 44.2033 15.4023C44.145 15.6091 44.1129 15.8263 44.1078 16.0383V17.6124C44.1078 18.2962 44.32 18.8738 44.7394 19.3508C45.1582 19.8226 45.7051 20.061 46.3785 20.061C47.1691 20.061 47.7847 19.7536 48.2252 19.1495C48.6548 18.5347 48.878 17.7343 48.878 16.7379ZM61.8675 16.6584C61.8675 18.2591 61.4327 19.5258 60.562 20.4586C59.7822 21.2854 58.8113 21.6987 57.6546 21.6987C56.4022 21.6987 55.5052 21.2535 54.9588 20.3631V25.308H52.8626V15.1586C52.8626 14.1516 52.8363 13.118 52.7831 12.0634H54.6299L54.7466 13.558H54.7838C55.484 12.4344 56.5453 11.8672 57.9675 11.8672C59.082 11.8672 60.0105 12.3072 60.7531 13.187C61.4911 14.0668 61.8675 15.2275 61.8675 16.6584ZM59.729 16.7379C59.729 15.8211 59.5225 15.0632 59.1083 14.4696C58.6574 13.8495 58.047 13.542 57.2833 13.542C56.7684 13.542 56.2958 13.717 55.8713 14.0562C55.4525 14.4007 55.1762 14.8512 55.0492 15.4023C54.9851 15.662 54.9485 15.874 54.9485 16.0383V17.6124C54.9485 18.2962 55.1608 18.8738 55.5796 19.3508C55.9989 19.8226 56.5453 20.061 57.2249 20.061C58.015 20.061 58.6306 19.7536 59.0711 19.1495C59.5116 18.5347 59.729 17.7343 59.729 16.7379ZM74.0029 17.8244C74.0029 18.9375 73.6156 19.8438 72.841 20.538C71.992 21.3012 70.798 21.6828 69.2751 21.6828C67.8689 21.6828 66.739 21.4126 65.8842 20.8666L66.3671 19.123C67.2853 19.6688 68.2985 19.9391 69.3969 19.9391C70.1876 19.9391 70.8031 19.759 71.2437 19.4038C71.6842 19.0487 71.9067 18.5718 71.9067 17.9782C71.9067 17.4482 71.7214 17.003 71.3604 16.6372C70.9994 16.2768 70.3998 15.9376 69.5565 15.6303C67.2641 14.777 66.1177 13.5262 66.1177 11.8885C66.1177 10.8179 66.521 9.93811 67.3328 9.25439C68.1395 8.57068 69.2116 8.22622 70.5486 8.22622C71.7374 8.22622 72.7294 8.43291 73.5201 8.84628L72.9949 10.5529C72.2517 10.1501 71.4187 9.95399 70.4793 9.95399C69.7419 9.95399 69.1584 10.1342 68.7442 10.4946C68.394 10.8179 68.219 11.2101 68.219 11.6765C68.219 12.1906 68.4209 12.6199 68.8242 12.9538C69.1744 13.2612 69.8054 13.6004 70.7236 13.9608C71.8484 14.4166 72.6762 14.9412 73.2071 15.5508C73.7375 16.1603 74.0029 16.9182 74.0029 17.8244ZM80.9539 13.6375H78.6404V18.2114C78.6404 19.3774 79.0488 19.955 79.8664 19.955C80.2428 19.955 80.5506 19.9233 80.8 19.8596L80.8584 21.4496C80.4448 21.6034 79.9036 21.6828 79.2296 21.6828C78.4018 21.6828 77.7542 21.4284 77.2873 20.925C76.8205 20.4214 76.5871 19.5734 76.5871 18.3863V13.6375H75.2129V12.0687H76.5871V10.3409L78.6461 9.72079V12.0687H80.9591L80.9539 13.6375ZM91.3593 16.7008C91.3593 18.1478 90.9457 19.3403 90.1178 20.2678C89.2528 21.227 88.1018 21.704 86.6635 21.704C85.2784 21.704 84.1749 21.243 83.3522 20.3314C82.5301 19.4144 82.1216 18.2538 82.1216 16.8599C82.1216 15.3971 82.5461 14.2046 83.3951 13.2718C84.2441 12.339 85.3848 11.8779 86.8225 11.8779C88.2076 11.8779 89.3221 12.339 90.1602 13.2559C90.956 14.141 91.3593 15.2911 91.3593 16.7008ZM89.1842 16.7486C89.1842 15.8847 88.9982 15.1427 88.6212 14.5279C88.1813 13.7806 87.5548 13.4043 86.7379 13.4043C85.8992 13.4043 85.2521 13.7806 84.8168 14.5279C84.4403 15.1479 84.2544 15.9006 84.2544 16.791C84.2544 17.6548 84.4403 18.3968 84.8168 19.017C85.2681 19.7642 85.9049 20.1406 86.7219 20.1406C87.5234 20.1406 88.1492 19.759 88.6058 19.0011C88.9931 18.3598 89.1842 17.6124 89.1842 16.7486ZM98.1782 13.9078C97.9602 13.8707 97.7376 13.8495 97.5145 13.8495C96.7771 13.8495 96.2096 14.1251 95.8062 14.6816C95.4561 15.1744 95.2811 15.7946 95.2811 16.5419V21.4867H93.1849V15.0314C93.1849 14.0403 93.1689 13.0544 93.1265 12.0634H94.9521L95.0259 13.8654H95.0842C95.3074 13.2452 95.6523 12.7471 96.1352 12.3708C96.57 12.0422 97.106 11.8672 97.6524 11.8672C97.8492 11.8672 98.0186 11.8832 98.1782 11.9044V13.9078ZM107.554 16.3299C107.559 16.6479 107.533 16.9659 107.48 17.2786H101.187C101.208 18.2114 101.516 18.9215 102.099 19.4144C102.63 19.8543 103.314 20.0716 104.158 20.0716C105.092 20.0716 105.941 19.9233 106.705 19.6264L107.034 21.0786C106.142 21.4655 105.087 21.6616 103.872 21.6616C102.407 21.6616 101.261 21.2323 100.423 20.3738C99.5895 19.5151 99.1702 18.3598 99.1702 16.9128C99.1702 15.4924 99.5575 14.3106 100.338 13.3672C101.15 12.3602 102.253 11.8567 103.638 11.8567C104.996 11.8567 106.026 12.3602 106.726 13.3672C107.273 14.1675 107.554 15.1532 107.554 16.3299ZM105.554 15.7892C105.57 15.1692 105.432 14.6339 105.145 14.178C104.784 13.5951 104.222 13.3036 103.474 13.3036C102.789 13.3036 102.232 13.5898 101.802 14.1568C101.452 14.6074 101.245 15.1532 101.181 15.784L105.554 15.7892Z"
						fill="#BDC2CC" />
				</svg>
			</div>
		</div>

		<div class="flex flex-col items-center">
			<div class="mb-2">
				<img src="{$basePath}/new-design/hp-icons/green-stars.svg" alt="green-stars" loading="lazy">
			</div>
			<div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
				<span class="font-medium text-sm">4,8/5</span> ({_'newFront.homepage.review.facebook.count'})
			</div>
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="105" height="26" viewBox="0 0 105 18" fill="none">
					<path
						d="M92.356 17.7011H95.8498V12.2781L100.725 17.7011H105L99.3244 11.4512L104.173 6.03195H100.283L95.8498 11.0267V0L92.356 0.439333V17.7011ZM84.2986 5.73304C80.4199 5.73304 77.7265 8.23589 77.7265 11.8647C77.7265 15.4935 80.4199 17.9964 84.2986 17.9964C88.1772 17.9964 90.8707 15.4935 90.8707 11.8647C90.8707 8.23589 88.1772 5.73304 84.2986 5.73304ZM84.2986 15.1391C82.5016 15.1391 81.2704 13.8064 81.2704 11.8647C81.2704 9.92295 82.5016 8.59029 84.2986 8.59029C86.0955 8.59029 87.3268 9.92295 87.3268 11.8647C87.3268 13.8064 86.0955 15.1391 84.2986 15.1391ZM70.1 5.73304C66.2214 5.73304 63.5279 8.23589 63.5279 11.8647C63.5279 15.4935 66.2214 17.9964 70.1 17.9964C73.9787 17.9964 76.6721 15.4935 76.6721 11.8647C76.6721 8.23589 73.9787 5.73304 70.1 5.73304ZM70.1 15.1391C68.3031 15.1391 67.0718 13.8064 67.0718 11.8647C67.0718 9.92295 68.3031 8.59029 70.1 8.59029C71.897 8.59029 73.1282 9.92295 73.1282 11.8647C73.1282 13.8064 71.897 15.1391 70.1 15.1391ZM56.7287 5.73304C55.1127 5.73304 53.7736 6.34212 52.9348 7.45694V0L49.4371 0.43558V17.6973H52.5385L52.6 15.8959C53.4273 17.2396 54.9087 17.9964 56.7287 17.9964C60.0456 17.9964 62.4736 15.4122 62.4736 11.8647C62.4736 8.31707 60.0571 5.73304 56.7287 5.73304ZM55.9015 15.1391C54.1045 15.1391 52.8733 13.8064 52.8733 11.8647C52.8733 9.92295 54.1045 8.59029 55.9015 8.59029C57.6984 8.59029 58.9297 9.92295 58.9297 11.8647C58.9297 13.8064 57.6984 15.1391 55.9015 15.1391ZM42.434 15.2904C40.5794 15.2904 39.248 14.4635 38.794 13.0386H47.4015C47.4863 12.6031 47.544 12.0862 47.544 11.7392C47.544 7.98484 45.3545 5.73304 41.676 5.73304C37.9629 5.73304 35.3924 8.23589 35.3924 11.8647C35.3924 15.5415 38.086 17.9964 42.0839 17.9964C44.1539 17.9964 46.1664 17.3319 47.4246 16.2281L46.1664 14.1018C44.9467 14.9102 43.7499 15.2904 42.434 15.2904ZM41.6529 8.33924C43.269 8.33924 44.3002 9.28055 44.3002 10.7387V10.7499H38.7209C39.0557 9.20312 40.0983 8.33924 41.6529 8.33924ZM29.9208 18C31.7293 18 33.4993 17.3355 34.7459 16.1838L33.38 13.98C32.4103 14.7035 31.3099 15.0948 30.2671 15.0948C28.3278 15.0948 27.0349 13.7953 27.0349 11.8683C27.0349 9.94137 28.3278 8.64191 30.2671 8.64191C31.2252 8.64191 32.268 8.97413 33.1145 9.56116L34.5036 7.30924C33.38 6.33473 31.6408 5.73667 29.9169 5.73667C26.123 5.73667 23.441 8.27648 23.441 11.8683C23.4448 15.4492 26.1268 18 29.9208 18ZM18.8121 6.03195L18.7506 7.84829C17.9232 6.49345 16.4418 5.73667 14.6218 5.73667C11.2934 5.73667 8.87694 8.32082 8.87694 11.8683C8.87694 15.416 11.3088 18 14.6218 18C16.4418 18 17.927 17.2432 18.7506 15.8995L18.8121 17.701H21.9134V6.03195H18.8121ZM15.449 15.1391C13.6522 15.1391 12.4208 13.8064 12.4208 11.8647C12.4208 9.92295 13.6522 8.59029 15.449 8.59029C17.246 8.59029 18.4773 9.92295 18.4773 11.8647C18.4773 13.8064 17.2421 15.1391 15.449 15.1391ZM8.61526 6.05389H5.4716V5.0571C5.4716 3.56571 6.07957 2.99358 7.65721 2.99358C8.14583 2.99358 8.54214 3.00461 8.76921 3.02678V0.468553C8.33818 0.354058 7.2878 0.239677 6.67983 0.239677C3.46684 0.239677 1.98544 1.69411 1.98544 4.83561V6.05014H0V8.87055H1.98544V17.6971H5.47539V8.87419H8.07662L8.61526 6.05389Z"
						fill="#BDC2CC" />
				</svg>
			</div>
		</div>

		<div class="flex flex-col items-center">
			<div class="mb-2">
				<img src="{$basePath}/new-design/hp-icons/green-stars.svg" alt="green-stars" loading="lazy">
			</div>
			<div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
				<span class="font-medium text-sm">4,8/5</span> ({_'newFront.homepage.review.google.count'})
			</div>
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="113" height="25" viewBox="0 0 113 25" fill="none">
					<path
						d="M57.0823 10.6854C54.7526 10.6854 52.9082 12.4432 52.9082 14.8846C52.9082 17.2283 54.7526 19.0837 57.0823 19.0837C59.412 19.0837 61.2569 17.3259 61.2569 14.8846C61.2569 12.4432 59.412 10.6854 57.0823 10.6854ZM57.0823 17.3259C55.8202 17.3259 54.7526 16.2517 54.7526 14.7869C54.7526 13.3221 55.8202 12.2479 57.0823 12.2479C58.3444 12.2479 59.412 13.2245 59.412 14.7869C59.412 16.3494 58.3444 17.3259 57.0823 17.3259ZM48.0537 10.6854C45.724 10.6854 43.8796 12.4432 43.8796 14.8846C43.8796 17.2283 45.724 19.0837 48.0537 19.0837C50.384 19.0837 52.2284 17.3259 52.2284 14.8846C52.2284 12.4432 50.384 10.6854 48.0537 10.6854ZM48.0537 17.3259C46.7922 17.3259 45.724 16.2517 45.724 14.7869C45.724 13.3221 46.7922 12.2479 48.0537 12.2479C49.3158 12.2479 50.384 13.2245 50.384 14.7869C50.384 16.3494 49.3158 17.3259 48.0537 17.3259ZM37.2782 11.9549V13.7127H41.4525C41.3555 14.6893 40.9672 15.4705 40.4818 15.9588C39.8993 16.5447 38.9285 17.2283 37.2782 17.2283C34.657 17.2283 32.7154 15.1775 32.7154 12.5409C32.7154 9.90425 34.7541 7.8535 37.2782 7.8535C38.6373 7.8535 39.7051 8.4394 40.4818 9.123L41.7438 7.8535C40.6759 6.87697 39.3168 6.09573 37.3752 6.09573C33.8804 6.09573 30.8709 9.02536 30.8709 12.5409C30.8709 16.0564 33.8804 18.986 37.3752 18.986C39.3168 18.986 40.6759 18.4001 41.8409 17.1306C43.0058 15.9588 43.3941 14.2986 43.3941 13.0292C43.3941 12.6385 43.3941 12.2479 43.297 11.9549H37.2782ZM81.3524 13.3221C80.964 12.3456 79.9928 10.6854 77.8575 10.6854C75.7217 10.6854 73.9743 12.3456 73.9743 14.8846C73.9743 17.2283 75.7217 19.0837 78.0514 19.0837C79.8958 19.0837 81.061 17.9119 81.4493 17.2283L80.0903 16.2517C79.6049 16.9353 79.0221 17.4235 78.0514 17.4235C77.0808 17.4235 76.4979 17.033 76.0126 16.1541L81.5463 13.8104L81.3524 13.3221ZM75.7217 14.6893C75.7217 13.1268 76.9838 12.2479 77.8575 12.2479C78.5368 12.2479 79.2166 12.6385 79.4105 13.1268L75.7217 14.6893ZM71.1587 18.6931H73.003V6.48635H71.1587V18.6931ZM68.2467 11.5643C67.7608 11.0761 66.9846 10.5878 66.0133 10.5878C63.975 10.5878 62.0331 12.4432 62.0331 14.7869C62.0331 17.1306 63.8781 18.8884 66.0133 18.8884C66.9846 18.8884 67.7608 18.4001 68.1491 17.9119H68.2467V18.4978C68.2467 20.0602 67.3729 20.9391 66.0133 20.9391C64.9457 20.9391 64.1689 20.1579 63.975 19.4743L62.4215 20.1579C62.9069 21.2321 64.072 22.5992 66.1109 22.5992C68.2467 22.5992 69.9941 21.3297 69.9941 18.3025V10.8808H68.2467V11.5643ZM66.1109 17.3259C64.8488 17.3259 63.7806 16.2517 63.7806 14.7869C63.7806 13.3221 64.8488 12.2479 66.1109 12.2479C67.3729 12.2479 68.3436 13.3221 68.3436 14.7869C68.3436 16.2517 67.3729 17.3259 66.1109 17.3259ZM89.7981 6.48635H85.4295V18.6931H87.2739V14.1033H89.7981C91.8364 14.1033 93.7783 12.6385 93.7783 10.2949C93.7783 7.95114 91.8364 6.48635 89.7981 6.48635ZM89.8951 12.4432H87.2739V8.24412H89.8951C91.2541 8.24412 92.0309 9.41599 92.0309 10.2949C91.9339 11.2714 91.1571 12.4432 89.8951 12.4432ZM101.059 10.6854C99.6998 10.6854 98.3408 11.2714 97.8554 12.5409L99.5059 13.2245C99.8943 12.5409 100.477 12.3456 101.156 12.3456C102.127 12.3456 103.001 12.9315 103.098 13.9081V14.0057C102.807 13.8104 102.03 13.5174 101.253 13.5174C99.5059 13.5174 97.7585 14.494 97.7585 16.2517C97.7585 17.9119 99.2145 18.986 100.768 18.986C102.03 18.986 102.612 18.4001 103.098 17.8142H103.195V18.7908H104.942V14.1033C104.748 11.8573 103.098 10.6854 101.059 10.6854ZM100.865 17.3259C100.283 17.3259 99.4089 17.033 99.4089 16.2517C99.4089 15.2752 100.477 14.9822 101.35 14.9822C102.127 14.9822 102.515 15.1776 103.001 15.3728C102.807 16.5447 101.836 17.3259 100.865 17.3259ZM111.058 10.8808L109.02 16.1541H108.923L106.787 10.8808H104.845L108.049 18.3025L106.204 22.4039H108.049L113 10.8808H111.058ZM94.7489 18.6931H96.5933V6.48635H94.7489V18.6931Z"
						fill="#BDC2CC" />
					<path
						d="M0.582458 25C0.194134 24.8047 0 24.3165 0 23.8282C0 23.7305 0 23.5352 0 23.4376C0 16.2112 0 8.98477 0 1.85604C0 1.46542 0.0970671 1.17249 0.194134 0.781871C0.291257 0.586531 0.485391 0.391248 0.679581 0.195965C4.85396 4.39506 8.93128 8.59415 13.0086 12.7933C8.83421 16.6994 4.7569 20.8009 0.582458 25Z"
						fill="#BDC2CC" />
					<path
						d="M17.0856 8.49588C15.9206 9.66775 14.6586 10.8396 13.4937 12.1091C9.61049 8.10526 5.63024 4.10145 1.74706 0.0976415C1.74706 0.0976415 1.74706 0 1.84412 0C6.89227 2.83194 11.9404 5.66394 17.0856 8.49588Z"
						fill="#BDC2CC" />
					<path
						d="M1.74706 24.9995C5.63024 21.0933 9.61049 17.1872 13.4937 13.281C14.5615 14.3552 15.7265 15.4294 16.9885 16.6013C11.9404 19.4333 6.89227 22.2652 1.84412 24.9995H1.74706Z"
						fill="#BDC2CC" />
					<path
						d="M17.6685 16.2107C16.4064 15.0389 15.2415 13.867 13.9795 12.6952C15.2415 11.4257 16.5036 10.1562 17.7655 8.88669C18.1539 9.08198 18.5422 9.27732 18.9305 9.57024C19.9013 10.1562 20.9692 10.6445 21.94 11.2304C22.2312 11.328 22.4254 11.5234 22.6195 11.8163C23.0078 12.3045 23.0078 12.7929 22.6195 13.2811C22.4254 13.4764 22.2312 13.6717 21.94 13.7694C20.5809 14.6483 19.1247 15.4295 17.6685 16.2107Z"
						fill="#BDC2CC" />
				</svg>
			</div>
		</div>
	</div>

	<div n:if="!isset($registrationBonusVariant) || $registrationBonusVariant === 0" class="flex flex-col lg:flex-row items-center justify-between border-t border-light-4">
		<div class="mt-[60px] text-center">
			<div class="leading-[39px] text-[26px] text-dark-1 font-bold mb-[30px] md:text-[33px] md:leading-[49.5px] ">
				<div class="flex items-center gap-2">
					{_'newFront.homepage.cashback.title'}
					<span class="hidden lg:block">
						<img src="{$basePath}/new-design/hp-flying-money.svg" alt="money" loading="lazy">
					</span>
					{_'newFront.homepage.cashback.titleSuffix'}
				</div>
				<div class="font-normal">
				    {_'newFront.homepage.cashback.description', ['bonus' => $getCampaignBonusAmount()] |noescape}
				</div>
			</div>

			<div class="mb-[60px]">
				<div class="lg:hidden">
					<img class="w-[208px] h-[186px] m-auto" src="{$basePath}/new-design/donkey-with-glasses.png"
						alt="money" loading="lazy">
				</div>
				<button
					class="flex justify-center items-center w-full lg:w-auto gap-3 px-7 py-3 rounded-xl bg-orange-gradient text-white font-bold leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover js-form-input-focus">
					{_'newFront.homepage.cashback.beginButton'}
					<svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
						<path d="M4.19576 15.0328L14.0938 5.13477" stroke="white" stroke-width="2"
							stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
						<path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="white" stroke-width="2"
							stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
				</button>
			</div>
		</div>

		<div class="hidden lg:block">
			<img src="{$basePath}/new-design/donkey-with-glasses.png" alt="money" loading="lazy">
		</div>
	</div>
</section>

{cache 'homepage-shopsWithCoupons-' . $localization->getId(), 'expiration' => '1 hour'}
    <div class="bg-light-6 pb-10 md:pb-36">
        <section class="container text-center pt-10 md:pt-[88px]">
            <div class="font-bold text-dark-1 leading-[39px] text-[26px] md:leading-[49.5px] md:text-[33px] mb-2">
                {_'newFront.homepage.favoriteShops.title'}
            </div>
            <div class="text-sm leading-[31.5px] text-dark-1 mb-[34px] md:mb-[70px] md:text-lg ">
                {_'newFront.homepage.favoriteShops.description'}
            </div>

            <div class="grid grid-cols-2 pb-12 gap-[15px] sm:grid-cols-4 md:gap-5 md:grid-cols-5 lg:grid-cols-6">
                {var $shopsWithCoupon = $getShopsWithCoupon()}

                {foreach $shopsWithCoupon as $couponShop}
                    <a n:href=":NewFront:Shops:Shop:, $couponShop"
                            class="{isset($class) ? $class} flex flex-col items-center justify-center bg-white rounded-xl cursor-pointer shadow-hover p-2">
                        <div class="w-full h-[92px] px-4 py-4 flex items-center justify-center">
                            <img class="max-w-[100px] max-h-[60px] w-full" loading="lazy" alt="{$couponShop->getName()}"
                                 src="{$couponShop->getCurrentLogo() |image:282,0,'fit',false,$couponShop->getName()}" />
                        </div>
                        <img class="m-auto" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">

                        <div class="text-center text-zinc-950 text-base font-normal leading-[25px] mt-3 mb-3">
                            {$couponShop->getName()}
                        </div>
                    </a>
                {/foreach}
            </div>

            <div class="flex justify-center pb-16">
                <a n:href=":NewFront:Shops:Shops:default"
                    class="border border-light-2 leading-[28px] font-bold px-20 rounded-xl pt-[15px] pb-[13px] xl:hover:bg-light-4">{_'newFront.homepage.shops.allShops'}</a>
            </div>
        </section>
    </div>
{/cache}
