{block styles}
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

	<style>
		.bg-img {
			background-image: url('{$basePath}/new-design/wp-mobile-bg-orange.svg');
			background-repeat: no-repeat;
			background-size: cover;
			background-position: bottom;
		}

		@media only screen and (min-width: 768px) {
			.bg-img {
				background-image: url('{$basePath}/new-design/hp-bg-orange-donkey.png');
				background-repeat: no-repeat;
				background-position: top;
				background-size: auto;
			}
		}

		.swiper {
			width: 100%;
			height: 100%;
			margin-left: auto;
			margin-right: auto;
			position: relative;
			overflow: hidden;
			list-style: none;
			padding: 0;
			z-index: 1;
			display: block;
		}

		.swiper-slide {
			text-align: center;
			justify-content: center;
			align-items: center;
		}

		.swiper-button-prev {
			top: 41%;
			left: -79px;
		}

		.swiper-button-next {
			top: 41%;
			right: -79px;
		}

		.swiper-button-prev.swiper-tipli-extra-button-prev,
		.swiper-button-next.swiper-tipli-extra-button-next {
			top: 55%;
		}

		.swiper-coupon-button-next {
			top: 46%;
			right: -80px;
		}

		.swiper-coupon-button-prev {
			top: 50%;
			left: -80px;
		}

		.swiper-button-next,
		.swiper-button-prev {
			display: none;
			width: 60px;
			height: 60px;
			padding: 8px;
			border-radius: 16px;
			background: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
		}

		.swiper-button-next:after,
		.swiper-button-prev:after {
			font-weight: bold;
			font-size: 20px;
			color: white;
		}

		.swiper-pagination.swiper-pagination-bullets {
			display: none;
		}

		.swiper-pagination-bullet-active {
			border-radius: 4px;
			width: 30px;
			background: linear-gradient(51deg, #EF7F1A 13.11%, #FFA439 96.21%);
		}

		@media (min-width: 768px) {

			.swiper-button-next,
			.swiper-button-prev {
				display: flex;
			}

			.swiper-pagination.swiper-pagination-bullets {
				display: block;
			}
		}
	</style>

{/block}

{block scripts}

	<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

	<script>
		const swiper = new Swiper('.swiper', {
			direction: 'horizontal',
			loop: true,
			spaceBetween: 20,
			breakpoints: {
				0: {
					slidesPerView: 1.2,
				},
				768: {
					slidesPerView: 2.2,
				},
				1024: {
					slidesPerView: 3,
				}
			},
		});

		const swiperTipliExtra = new Swiper('.swiper-tipli-extra', {
			direction: 'horizontal',
			spaceBetween: 20,
			slidesPerView: 1.3,
			navigation: {
				nextEl: '.swiper-tipli-extra-button-next',
				prevEl: '.swiper-tipli-extra-button-prev',
			},
			breakpoints: {
				1024: { slidesPerView: 4 },
			},
		});
	</script>


{/block}

{define tellFriend}
	<div class="p-2.5 rounded-xl bg-light-6 min-w-[295px] md:min-w-[451px] max-w-full min-h-[114px] flex">
		<div class="flex gap-[11px]">
			<div class="self-start text-lg font-bold leading-[31.5px] py-[5px] pl-[15px] pr-4 bg-primary-blue-dark text-white rounded-[10px]">€</div>
			<div>
				<div class="font-bold leading-7 text-primary-blue-dark">
					{_'newFront.campaign300.tellFriend.title'}
				</div>
				<div class="text-xs leading-[21px] text-dark-1 -mt-1">
					{_'newFront.campaign300.tellFriend.text', ['amount' => ($recommendationBonusAmount |amount), 'upTo' => ($recommendationBonusAmount * 30 |amount)]}
				</div>
			</div>
			<a n:href=":NewFront:Account:User:tellFriend" class="flex items-center">
				<svg xmlns="http://www.w3.org/2000/svg" width="33" height="70" viewBox="0 0 33 70" fill="none">
					<rect x="0.5" y="0.5" width="32" height="69" rx="9.5" fill="white" fill-opacity="0.5" stroke="#CCD0D7"></rect>
					<path d="M14.0001 41L20 34.9998L14 29" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
				</svg>
			</a>
		</div>
	</div>
{/define}

{block footerBackgroundColor}bg-light-6{/block}

{block content}
{if $campaignBarEnabled && isset($presenter['campaignProgressBarControl'])}
	<div class="container hidden md:block">
		<div class="flex gap-5 md:items-center my-[30px] overflow-scroll md:overflow-visible">
			<div class="p-2.5 rounded-xl border border-light-4 min-w-[295px] md:min-w-[729px] max-w-full">
				{control campaignProgressBarControl}
			</div>

			{include tellFriend}
		</div>
	</div>
{/if}

<div style="background: linear-gradient(180deg, #FFF 0%, #F4F4F6 39.23%)" class="xl:mt-10">
	<div class="bg-img">
		<div class="container md:hidden px-0" n:if="$campaignBarEnabled">
			<div class="flex gap-5 md:items-center pt-5 overflow-x-scroll md:overflow-x-visible">
				<div class="p-2.5 ml-4 rounded-xl bg-white min-w-[295px] md:min-w-[729px] max-w-full">
					{control campaignProgressBarControl}
				</div>

				{include tellFriend}
			</div>
		</div>

		<div
				id="confirmation-popup"
				class="hidden p-10 bg-primary-blue-dark backdrop-blur-sm w-full max-w-[436px] absolute right-0 top-[162px] rounded-l-2xl z-20"
		>
				<span class="cursor-pointer absolute top-[15px] right-[15px]" onclick="closePopup('confirmation-popup')">
					<svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none">
						<path d="M1 10L9.99999 1M10 10L1.00001 1" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
					</svg>
				</span>
			<div class="flex items-center gap-[14px] text-[20xl] leading-[35px] font-bold text-white">
				<svg xmlns="http://www.w3.org/2000/svg" width="63" height="50" viewBox="0 0 63 50" fill="none">
					<path d="M1 6.7832H4.24305C6.25598 6.7832 7.84022 8.47737 7.67247 10.4266L6.1255 28.5706C5.86457 31.5399 8.26888 34.0903 11.3255 34.0903H31.1752C33.8591 34.0903 36.2075 31.9407 36.4125 29.3357L37.419 15.673C37.6427 12.649 35.2942 10.1897 32.1817 10.1897H8.1198" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					<path d="M27.5585 43.2168C28.8452 43.2168 29.8883 42.1973 29.8883 40.9397C29.8883 39.6821 28.8452 38.6626 27.5585 38.6626C26.2718 38.6626 25.2288 39.6821 25.2288 40.9397C25.2288 42.1973 26.2718 43.2168 27.5585 43.2168Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					<path d="M12.6491 43.2168C13.9358 43.2168 14.9789 42.1973 14.9789 40.9397C14.9789 39.6821 13.9358 38.6626 12.6491 38.6626C11.3624 38.6626 10.3193 39.6821 10.3193 40.9397C10.3193 42.1973 11.3624 43.2168 12.6491 43.2168Z" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					<path d="M14.0479 17.7139H36.4137" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					<g filter="url(#filter0_b_783_3039)">
						<rect x="13" width="50" height="50" rx="25" fill="#66B940" fill-opacity="0.8"/>
					</g>
					<path d="M31 25.6929L35.4667 30L45 21" stroke="white" stroke-width="3" stroke-linecap="round"/>
					<defs>
						<filter id="filter0_b_783_3039" x="10" y="-3" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix"/>
							<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.5"/>
							<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_783_3039"/>
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_783_3039" result="shape"/>
						</filter>
					</defs>
				</svg>
				{_newFront.homepageLogged.firstPurchasePopup.title}
			</div>
			<button id="open-welcome-popup" class="w-full mt-4 leading-7 font-bold text-white bg-orange-gradient py-[14px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
				{_newFront.homepageLogged.firstPurchasePopup.cta}
			</button>
		</div>

		<div id="welcome-popup" class="hidden fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
			<div class="bg-white m-auto w-[781px] max-w-full rounded-2xl">
				<div class="pt-[60px] pl-[73px] pb-[61px] rounded-t-2xl border-b-secondary-green relative">
					<div class="welcome-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
						<img src="{$basePath}/new-design/close-btn.svg" alt="close" loading="lazy">
					</div>

					<div class="text-dark-1 text-[26px] font-bold leading-[39px]">
						{_newFront.homepageLogged.firstPurchase.thanks.title}
					</div>
					<svg class="mt-[23px] mb-[36px]" xmlns="http://www.w3.org/2000/svg" width="91" height="7" viewBox="0 0 91 7" fill="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M38.2358 0.333673C37.9414 0.307544 37.6423 0.322802 37.3628 0.378193C37.0833 0.433585 36.8312 0.527566 36.6271 0.652498L30.8651 4.18154L24.7559 0.706279C24.3909 0.49931 23.9061 0.384348 23.4069 0.386399C22.9077 0.388452 22.4345 0.507369 22.0904 0.717253L16.3273 4.24537L10.2202 0.770597C9.85577 0.563033 9.37069 0.447655 8.87116 0.449708C8.37162 0.451762 7.89832 0.571087 7.55481 0.781561L0.501375 5.10205C0.163937 5.31398 -0.0158436 5.59819 0.00109731 5.89297C0.0180383 6.18775 0.230331 6.46931 0.59186 6.67657C0.953389 6.88382 1.43499 7.00005 1.93216 7C2.42933 6.99995 2.90193 6.88362 3.2474 6.67629L9.00846 3.14717L15.1178 6.62215C15.4828 6.82906 15.9677 6.94397 16.4668 6.94192C16.966 6.93987 17.4391 6.82101 17.7832 6.61119L23.5455 3.083L29.6548 6.55824C30.0199 6.76497 30.5046 6.87974 31.0037 6.87763C31.5027 6.87552 31.9757 6.75669 32.3198 6.54697L38.0803 3.01835L44.1896 6.49331C44.3697 6.59841 44.5823 6.68174 44.8152 6.73848C45.0481 6.79522 45.2966 6.82425 45.5464 6.82387C45.7962 6.82349 46.0422 6.7937 46.2703 6.73625C46.4983 6.6788 46.7038 6.59483 46.8748 6.4892C47.0458 6.38356 47.179 6.25837 47.2666 6.12088C47.3542 5.9834 47.3945 5.83635 47.3851 5.68826C47.3757 5.54016 47.3169 5.39399 47.212 5.25818C47.1071 5.12238 46.9582 4.99966 46.774 4.89714L39.2926 0.641538C39.0017 0.475853 38.6317 0.368038 38.2358 0.333673Z" fill="#E1E4E8"/>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M81.8494 0.0124119C81.555 -0.0137174 81.2559 0.00154108 80.9764 0.0569323C80.6969 0.112324 80.4448 0.206304 80.2407 0.331237L74.4788 3.86027L68.3695 0.385018C68.0045 0.178049 67.5197 0.0630866 67.0205 0.0651383C66.5213 0.0671905 66.0481 0.186108 65.704 0.395992L59.9409 3.92411L53.8338 0.449336C53.4694 0.241772 52.9843 0.126394 52.4848 0.128447C51.9852 0.1305 51.5119 0.249826 51.1684 0.4603L44.115 4.78079C43.7775 4.99272 43.5978 5.27693 43.6147 5.57171C43.6316 5.86648 43.8439 6.14805 44.2055 6.3553C44.567 6.56256 45.0486 6.67879 45.5458 6.67874C46.0429 6.67869 46.5155 6.56236 46.861 6.35503L52.6221 2.82591L58.7314 6.30089C59.0964 6.5078 59.5813 6.62271 60.0804 6.62066C60.5796 6.61861 61.0527 6.49975 61.3969 6.28993L67.1591 2.76174L73.2684 6.23698C73.6335 6.44371 74.1182 6.55848 74.6173 6.55637C75.1163 6.55426 75.5893 6.43543 75.9334 6.2257L81.6939 2.69709L87.8032 6.17205C87.9833 6.27715 88.1959 6.36048 88.4288 6.41722C88.6617 6.47396 88.9103 6.50299 89.16 6.50261C89.4098 6.50223 89.6559 6.47243 89.8839 6.41499C90.1119 6.35754 90.3174 6.27357 90.4884 6.16794C90.6594 6.0623 90.7926 5.93711 90.8802 5.79962C90.9678 5.66214 91.0081 5.51509 90.9987 5.36699C90.9893 5.2189 90.9305 5.07273 90.8256 4.93692C90.7207 4.80112 90.5718 4.6784 90.3876 4.57587L82.9062 0.320277C82.6153 0.154592 82.2453 0.0467765 81.8494 0.0124119Z" fill="#E1E4E8"/>
					</svg>

					<div class="text-lg leading-[31.5px]">
						<div class="flex gap-[14px] items-center text-secondary-green mb-[26px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
								<path d="M8 12.6071L10.5524 15L16 10M1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28472 21.8411 9.08262 23 12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28472 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28472 2.15893 4.22183 4.22183C2.15893 6.28472 1 9.08262 1 12Z" stroke="#66B940" stroke-width="1.5" stroke-linecap="round"/>
							</svg>
							{_newFront.homepageLogged.firstPurchase.thanks.text1}
						</div>
						<div class="flex gap-[14px] items-center mb-[26px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
								<path d="M19.7264 17.1333H17.1333V14.5403M3.2 12V2.46667C3.2 2.07769 3.35453 1.70463 3.62958 1.42958C3.90463 1.15452 4.27769 1 4.66667 1H19.3333C19.7223 1 20.0954 1.15452 20.3705 1.42958C20.6455 1.70463 20.8 2.07769 20.8 2.46667V8.33333M9.8 12H1C1 12.9725 1.38631 13.9051 2.07394 14.5928C2.76157 15.2803 3.69421 15.6667 4.66667 15.6667H8.33333M11.2667 17.1333C11.2667 18.6893 11.8847 20.1815 12.985 21.2817C14.0852 22.3819 15.5774 23 17.1333 23C18.6893 23 20.1815 22.3819 21.2817 21.2817C22.3819 20.1815 23 18.6893 23 17.1333C23 15.5774 22.3819 14.0852 21.2817 12.985C20.1815 11.8847 18.6893 11.2667 17.1333 11.2667C15.5774 11.2667 14.0852 11.8847 12.985 12.985C11.8847 14.0852 11.2667 15.5774 11.2667 17.1333Z" stroke="#80899C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							{_newFront.homepageLogged.firstPurchase.thanks.text2}
						</div>
						<div class="flex gap-[14px] items-center">
							<svg xmlns="http://www.w3.org/2000/svg" width="23" height="24" viewBox="0 0 23 24" fill="none">
								<path d="M1.77404 1.973L13.5542 5.34917C14.3525 5.61352 15.0496 6.12107 15.5496 6.80184C16.0496 7.4826 16.3278 8.30302 16.3458 9.14994V20.5631C16.3794 20.9234 16.3254 21.2866 16.1886 21.6211C16.0517 21.9557 15.8361 22.2517 15.5604 22.4834C15.2847 22.7151 14.9572 22.8756 14.6061 22.9511C14.2551 23.0265 13.8911 23.0146 13.5456 22.9163L3.80095 20.2449C3.00262 19.9945 2.30264 19.4972 1.80011 18.8235C1.29756 18.1497 1.01792 17.3337 1.00076 16.4909V4.05473C0.983064 3.26593 1.27547 2.50209 1.81417 1.92982C2.35287 1.35755 3.09417 1.0233 3.87634 1H18.769C19.626 1 20.4478 1.34323 21.0538 1.95419C21.6597 2.56515 22 3.39378 22 4.2578V13.9802C21.9962 14.4194 21.9065 14.8535 21.736 15.2577C21.5655 15.6619 21.3176 16.0281 21.0065 16.3356C20.6952 16.6429 20.327 16.8854 19.9227 17.049C19.5185 17.2126 19.0862 17.2942 18.6505 17.289H16.3458M22 9.14451H16.3458" stroke="#80899C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							{_newFront.homepageLogged.firstPurchase.thanks.text3}
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="container">
			<div class="flex items-end justify-between pt-[35px] md:pt-14">
				<div class="pl-5 md:pl-0 ">
					<h1 class="text-white font-bold text-[26px] leading-[35px] md:text-[40px] md:leading-[58px] ">
						{_newFront.homepageLogged.firstPurchase.title}
					</h1>
					<img class="mt-[19px] mb-[23px]" src="{$basePath}/new-design/welcome-icons/white-waves.svg"
						 alt="wave" loading="lazy">
					<div class="text-sm md:text-base leading-[28px] md:leading-[28px] text-white">
						{_newFront.homepageLogged.firstPurchase.text, ['bonus' => $getCampaignBonusAmount()] |noescape}
					</div>
				</div>

				{if $localization->isBulgarian() || $localization->isCroatian() || $localization->isSlovenian()}
				{else}
					<div id="open-how-it-work" class="hidden relative text-sm md:text-base cursor-pointer lg:block">
						<img class="" src="{$basePath}/new-design/frame1.png" alt="donkey" loading="lazy">
						<div class="text-white leading-[28px] uppercase text-center absolute top-[60px] left-[103px]">
							<div>{_newFront.homepageLogged.firstPurchase.how.title}</div>
							<div class="text-[33px] leading-[49.5px] font-bold">{_newFront.homepageLogged.firstPurchase.how.rewards}</div>
							<div>{_newFront.homepageLogged.firstPurchase.how.withTipli}</div>
						</div>
					</div>
				{/if}

				<div id="how-it-work" class="hidden fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
					<div class="bg-white m-auto w-[952px] max-w-full rounded-2xl">
						<div class="rounded-t-2xl relative">
							<div class="how-it-work-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
								<img src="{$basePath}/new-design/close-btn.svg" alt="close" loading="lazy">
							</div>

							<div>
								{var $iframe = null}

									{if $localization->isCzech()}
									{var $iframe = 'https://www.youtube.com/embed/s1_JU-_bO08'}
								{elseif $localization->isSlovak()}
									{var $iframe = 'https://www.youtube.com/embed/k_gK9_13mBo'}
								{elseif $localization->isPolish()}
									{var $iframe = 'https://www.youtube.com/embed/gRKTZKudWMw'}
								{elseif $localization->isRomanian()}
									{var $iframe = 'https://www.youtube.com/embed/d9vSnYZOzAE'}
								{elseif $localization->isHungarian()}
									{var $iframe = 'https://www.youtube.com/embed/Q6Unq_o2mI8'}
								{/if}
								<iframe class="rounded-2xl" width="952" height="536" n:if="$iframe" src="{$iframe}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div
					class="flex flex-col items-center md:flex-row md:items-stretch gap-[25px] mt-[34px] px-5 lg:gap-[70px] md:px-0 md:mt-[61px]">
				<div class="rounded-2xl bg-white text-center w-full shadow-xl pb-[27px] md:w-[353px]">
					<div
							class="flex items-center justify-center relative -top-[20px] -left-[20px] w-[42px] h-[42px] bg-secondary-green font-bold rounded-full text-white leading-[28px] md:leading-[31.5px] md:text-lg md:w-[54px] md:h-[54px] ">
						1.
					</div>
					<div>
						<img class="m-auto mb-[17px] md:mb-[22px]"
							 src="{$basePath}/new-design/welcome-icons/wp-e-shop.svg" alt="e-shop" loading="lazy">
					</div>

					<div
							class="text-dark-1 text-sm font-medium mb-2 leading-[24.5px] md:mb-3 md:text-lg md:leading-[28px]">
						{_newFront.homepageLogged.firstPurchase.steps.first.title}
					</div>
					<div class="text-dark-1 text-sm md:text-base px-[33px] md:px-[49px]">
						{_newFront.homepageLogged.firstPurchase.steps.first.text}
					</div>
				</div>

				<div class="bg-white rounded-2xl text-center w-full shadow-xl pb-[27px] md:w-[353px]">
					<div
							class="flex items-center justify-center relative -top-[20px] -left-[20px] w-[42px] h-[42px] bg-secondary-green font-bold rounded-full text-white leading-[28px] md:leading-[31.5px] md:text-lg md:w-[54px] md:h-[54px] ">
						2.
					</div>
					<div>
						<img class="m-auto mb-[17px] md:mb-[22px]"
							 src="{$basePath}/new-design/welcome-icons/wp-shop.svg" alt="shop" loading="lazy">
					</div>

					<div
							class="text-dark-1 text-sm font-medium mb-2 leading-[24.5px] md:mb-3 md:text-lg md:leading-[28px]">
						{_newFront.homepageLogged.firstPurchase.steps.second.title}
					</div>
					<div
							class="text-dark-1 text-sm md:text-base leading-[24.5px] px-[33px] md:leading-[28px] md:px-[49px]">
						{_newFront.homepageLogged.firstPurchase.steps.second.text}
					</div>
				</div>

				<div class="bg-white rounded-2xl text-center w-full shadow-xl pb-[27px] md:w-[353px]">
					<div
							class="flex items-center justify-center relative -top-[20px] -left-[20px] w-[42px] h-[42px] bg-secondary-green font-bold rounded-full text-white leading-[28px] md:leading-[31.5px] md:text-lg md:w-[54px] md:h-[54px] ">
						3.
					</div>
					<div>
						<img class="m-auto mb-[17px] md:mb-[22px]"
							 src="{$basePath}/new-design/welcome-icons/wp-reward.svg" alt="reward" loading="lazy">
					</div>

					<div
							class="text-dark-1 text-sm font-medium mb-2 leading-[24.5px] md:mb-3 md:text-lg md:leading-[28px]">
						{_newFront.homepageLogged.firstPurchase.steps.third.title}
					</div>
					<div
							class="text-dark-1 text-sm md:text-base px-[33px] leading-[24.5px] md:leading-[28px] md:px-[49px]">
						{_newFront.homepageLogged.firstPurchase.steps.third.text}
					</div>
				</div>
			</div>
		</div>
	</div>

	<section class="container relative mt-[51px] md:mt-[100px]">
		<h2
				class="text-lg text-dark-1 font-medium leading-[31.5px] mb-[23px] pl-5 md:pl-0 md:text-[26px] md:leading-[39px] md:mb-[38px]">
			{_newFront.homepageLogged.titleShops}
		</h2>

		<div class="grid grid-cols-2 pb-10 gap-[15px] sm:grid-cols-4 md:gap-5 md:grid-cols-5 lg:grid-cols-6 lg:pb-[100px]">
			{var $topShops = $topShops()}
				{foreach $topShops as $topShop}
				<a n:href=":NewFront:Shops:Shop:, $topShop" class="flex flex-col items-center justify-center bg-white rounded-xl cursor-pointer shadow-hover p-2">
					<div class="w-full h-[96px] flex items-center justify-center">
						<img
								class="max-h-[60px] max-w-[100px] w-full"
								alt="{$topShop->getName()}"
								src="{$topShop->getCurrentLogo() |image:200,0,'fit',false,$topShop->getName()}"
						/>
					</div>
					<img class="m-auto" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">
					{if $topShop->isCashbackAllowed() || $topShop->isCashbackActive()}
						<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
							{$topShop |reward:true,'extended'|noescape}
						</div>
					{else}
						<div class="text-center text-zinc-950 text-base font-normal leading-[25px] mt-3 mb-3">
							{$topShop->getName()}
						</div>
						<div class="flex w-full items-center justify-center py-2 bg-green-50 rounded-md gap-2 ">
							<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
								<path
										d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
										stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
								<path
										d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
										stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-lime-500 text-sm font-medium leading-normal">
								{_newFront.shops.nonCashbackShops.shopLabel}
							</div>
						</div>
					{/if}
				</a>
			{/foreach}
		</div>

		{if $user->isLoggedIn() && ($user->getIdentity()->isAdmin() || $localization->isCzech() || $localization->isSlovak())}
			{control voucherSlider}
		{/if}

		{if $showAddonPromo}
			{include 'addonPromo.latte'}
		{/if}
	</section>
</div>

{var $banners = $banners()}
<section class="container mt-[51px] mb-[60px] md:mb-[100px]" n:if="$banners">
	<div
			class="text-lg text-dark-1 font-medium leading-[31.5px] mb-[23px] pl-5 md:pl-0 md:text-[26px] md:leading-[39px] md:mb-[31px]">
		{_newFront.homepageLogged.titleBanners}
	</div>
	<div class="swiper">
		<div class="swiper-wrapper">
			{foreach $banners as $banner}
				{var $bannerLocation = $banner->getShop() ? $banner->getShop()->getName() : $banner->getLink()}
				{include '../../../DealsModule/Presenters/templates/Deals/bannerItem.latte', banner: $banner, source: $source, bannerLocation: $bannerLocation, bannerPosition: $iterator->counter}
			{/foreach}
		</div>
	</div>
</section>

<div class="bg-light-6 pb-10 md:pb-36 {if $banners}pt-10 md:pt-[100px]{/if}">
	<section class="container pb-16">
		<h2
				class="text-lg text-dark-1 font-medium leading-[31.5px] mb-[23px] pl-5 md:pl-0 md:text-[26px] md:leading-[39px] md:mb-[38px]">
			{_newFront.homepageLogged.titleDeals}
		</h2>

		<div class="grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4 md:gap-7">
			{var $coupons = $getCoupons()}
				{foreach $coupons as $coupon}
				{include '../../../DealsModule/Presenters/templates/Deals/couponItem.latte', coupon: $coupon}
			{/foreach}
		</div>

		<div class="text-center mt-10">
			<a n:href=":NewFront:Deals:Deals:"
					class="leading-[28px] font-bold text-dark-1 px-10 pt-[15px] pb-[13px] border border-light-2 rounded-xl xl:hover:bg-light-4">
				{_newFront.homepageLogged.otherDeals}
			</a>
		</div>
	</section>
</div>

{include 'snippets/emailVerificationModal.latte'}

<style>
	.dismiss::after {
		content: '';
		position: absolute;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 114px;
		background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 50%);
	}
</style>

<script>
	function closePopup(id) {
		var element = document.getElementById(id);
		element.classList.add('hidden');
	}

	// Welcome popup
	var welcomePopup = document.getElementById("welcome-popup");
	var welcomeBtn = document.getElementById("open-welcome-popup");
	var welcomeCloseBtn = document.querySelector(".welcome-popup-close");
	var confirmationPopup = document.getElementById("confirmation-popup");

	welcomeBtn.onclick = function() {
		$.nette.ajax({
			url: {link reportFirstPurchase!},
			type: 'POST',
		});

		welcomePopup.style.display = "flex";
		confirmationPopup.style.display = "none";
	}
	welcomeCloseBtn.onclick = function() {
		welcomePopup.style.display = "none";
	}

	window.onclick = function(event) {
		if (event.target == welcomePopup) {
			welcomePopup.style.display = "none";
		}
	}

	// HOW ITS WORK TIPLI
	var conditionsPopup = document.getElementById("how-it-work");
	var openConditionsPopup = document.getElementById("open-how-it-work");
	var closeConditionsPopup = document.querySelector(".how-it-work-popup-close");
	var videoIframe = conditionsPopup.querySelector("iframe");

	if (openConditionsPopup) {
		openConditionsPopup.onclick = function () {
			conditionsPopup.style.display = "flex";
		}
	}

	closeConditionsPopup.onclick = function () {
		conditionsPopup.style.display = "none";
		stopVideo();
	}

	window.onclick = function(event) {
		if (event.target == conditionsPopup) {
			conditionsPopup.style.display = "none";
			stopVideo();
		}
	}

	function stopVideo() {
		var videoSrc = videoIframe.src;
		videoIframe.src = "";
		videoIframe.src = videoSrc;
	}

</script>
