
{block title}
	{$shop->getName()}
{/block}

{block ogSiteName}{_'newFront.head.gambleTitle'}{/block}

{block metaDescription}
	{if $pageExtension && $pageExtension->isOverride()}
		{$pageExtension->getMetaDescription()}
	{elseif $pageExtension && $pageExtension->getMetaDescription()}
		{$pageExtension->getMetaDescription()}{elseif
		$descriptionBlock('short_description')}{$descriptionBlock('short_description')[0]->getDescription()
	|stripHtml|truncate:160}
	{else}
		{_'front.head.description'}
	{/if}
{/block}

{block metaKeywords}
	{if $pageExtension &&
	$pageExtension->getMetaKeywords()}{$pageExtension->getMetaKeywords()}{else}{_'front.shops.shop.shop.metaKeywords.' .
	($shop->isCashbackActive() ? 'cashback' : 'withoutCashback')}
	{/if}
{/block}

{block metaTitle}
	{var $title = $pageExtension && $pageExtension->getMetaTitle() ? $pageExtension->getMetaTitle() : $shop->getName()}

	{if $pageExtension && $pageExtension->isOverride()}
		{$pageExtension->getMetaTitle()}
	{elseif $pageExtension && $pageExtension->isDisableGeneratedMetaTitle()}
		{$title}
	{else}
		{var $currentMonth = $translator->translate('newFront.calendar.months.' . date('n'))}
		{var $titleOfferType = null}

		{*        {cache 'shop-title-reward' . $cashbackCacheKeyPostfix, 'expire' => ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}*}
		{capture $titleReward |trim}
			{var $titleContainsDealPrefix = false}

			{foreach explode('|', $translator->translate('model.shops.dealPrefixes')) as $dealPrefix} {*kod|kupon|sleva|slevy*}
				{if \Nette\Utils\Strings::contains(\Nette\Utils\Strings::webalize($title), $dealPrefix)}
					{var $titleContainsDealPrefix = true}
					{breakIf $titleContainsDealPrefix}
				{/if}
			{/foreach}

			{var $topDeal = $topDeal()}
			{if $topDeal && ($topDeal->getValue() || $topDeal->isProductType()) && !($topDeal->isProductType() && $shop->isCashbackActive())}
				{if !$titleContainsDealPrefix}
					{* Kupon/Sleva *}
					{$topDeal->isCouponType() ? $translator->translate('front.deals.deal.coupon') : $translator->translate('front.deals.deal.sale') |lower}
				{/if}

				{if $topDeal->isProductType()}
					{* 100 kc *}
					{$topDeal->getOriginalPrice() - $topDeal->getPrice()} {$topDeal->getPriceCurrency() |currency}
				{else}
					{* 20 % *}
					-{$topDeal->getValue() |amount: 2}
					{if !($topDeal->getUnit() == 'percentage' && $topDeal->getLocalization()->isPolish())}
					{/if}{$topDeal->getUnitSymbol()}
				{/if}

				{var $titleOfferType = 'topDeal'}
			{elseif $shop->isCashbackActive()}
				{$shop|reward:false,'complete' |noescape}

				{var $titleOfferType = 'cashback'}
			{elseif !$titleContainsDealPrefix}
				{$translator->translate('model.shops.rewardFilter.withoutOffer')}

				{var $titleOfferType = 'other'}
			{/if}
		{/capture}

		{if $localization->isPolish()}
			{capture $titleReward |replace: [' %', ' %'], '%'}
				{$titleReward}
			{/capture}
		{/if}

		{if $pageExtension && $pageExtension->getMetaTitle()}
			{$title}
		{else}
			{$titleReward}
		{/if}

		{$currentMonth . ' ' . date('Y')}
		{*        {/cache}*}
	{/if}
{/block}


{define logo}
	{var $shopCookie = "shop-cookie-" . $shop->getId()}
	{if $shop->isCashbackAllowed()}
		{if $shop->isCashbackActive()}
			{if $isUserLoggedIn}
				{if $popup}
					<a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
							class="shop-profile__logo-img-wrapper w-full" data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
							title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
							data-action="click" data-label="cashbackPopupBtnLogOut"
							data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
						<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
					</a>
				{else}
					<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
																							target="_blank"
																							data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
																							data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
																							title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
																							data-action="click" data-label="cashbackBtnLogIn">
						<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
					</a>
				{/if}
			{else}
				<a data-ajax-call="js-open-popup"
						class="open-register-popup shop-profile__logo-img-wrapper w-full"
						data-shop-id="{$shop->getId()}">
					<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
				</a>
			{/if}
		{else}
			<div class="shop-profile__logo-img-wrapper w-full">
				<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
			</div>
		{/if}
	{else}
		{if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
			<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
					class="ajax shop-profile__logo-img-wrapper w-full" data-ajax-call="js-open-popup" data-hit="event"
					data-category="shopDetail" data-action="click" data-label="noCashbackPopupBtnLogOut">
				<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
			</a>
		{else}
			{if $isUserLoggedIn}
				<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
																						target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
																						data-label="noCashbackRedirectBtnLogIn">
					<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
				</a>
			{else}
				<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
																						target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
																						data-label="noCashbackRedirectBtnLogOut">
					<img class="m-auto max-w-[170px] max-h-[100px] w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
				</a>
			{/if}
		{/if}
	{/if}
{/define}

{define ctaLink}
	{var $shopCookie = "shop-cookie-" . $shop->getId()}
	{if $shop->isCashbackAllowed()}
		{if $shop->isCashbackActive()}
			{if $isUserLoggedIn}
				{if $popup}
					<a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
							class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
							data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
							title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
							data-action="click" data-label="cashbackPopupBtnLogOut"
							data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
						{if $shop->getShopData()->getRedirectLabel()}
							{$shop->getShopData()->getRedirectLabel()}
						{else}
							{_'newFront.nocashbackShop.goToShopGamble'}
						{/if}
					</a>
				{else}
					<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
							class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
							data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
							data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
							title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
							data-action="click" data-label="cashbackBtnLogIn">
						{if $shop->getShopData()->getRedirectLabel()}
							{$shop->getShopData()->getRedirectLabel()}
						{else}
							{_'newFront.nocashbackShop.goToShopGamble'}
						{/if}
					</a>
				{/if}
			{else}
				<a data-ajax-call="js-open-popup"
						class="open-register-popup flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
						data-shop-id="{$shop->getId()}">
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'newFront.nocashbackShop.registerShopGamble'}
					{/if}
				</a>
			{/if}
		{else}
			{if $shop->isPaused() === true}
				<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
					class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
					data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
					data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'newFront.nocashbackShop.goToShopGamble'}
					{/if}
				</a>
			{else}
				<span class="shop-detail__button shop-detail__button--disabled">
					{if $shop->getShopData()->getRedirectLabel()}
						{$shop->getShopData()->getRedirectLabel()}
					{else}
						{_'newFront.nocashbackShop.goToShopGamble'}
					{/if}
				</span>
			{/if}
		{/if}
	{else}
		{if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
			<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
					class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
					data-ajax-call="js-open-popup" data-hit="event" data-category="shopDetail" data-action="click"
					data-label="noCashbackPopupBtnLogOut">
				{if $shop->getShopData()->getRedirectLabel()}
					{$shop->getShopData()->getRedirectLabel()}
				{else}
					{_'newFront.nocashbackShop.registerShopGamble'}
				{/if}
			</a>
		{else}
			<a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
				class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
				data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
				data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
				{if $shop->getShopData()->getRedirectLabel()}
					{$shop->getShopData()->getRedirectLabel()}
				{else}
					{_'newFront.nocashbackShop.goToShopGamble'}
				{/if}
			</a>
		{/if}
	{/if}
{/define}

{define offersWithConditions}
	<div class="hidden mb-5 xl:block" n:if="$shop->isActive() && $shop->isCashbackActive()">
		<div class="bg-white pt-[21px] px-5 pb-[25px] rounded-2xl">
			<div class="px-5 flex justify-between items-center">
				<div class="text-lg text-dark-1 font-medium leading-[31.5px]">
					{_newFront.shops.shop.shop.conditions.gambleTitle}
				</div>
				<button id="open-conditions-popup"
						class="hidden text-sm text-dark-2 leading-[24.5px] hover:cursor-pointer hover:underline">
					{_newFront.shops.shop.shop.conditions.conditionsTitle} »
				</button>
			</div>

			<div class="w-full h-px bg-light-5 mt-[19px] mb-[17px]"></div>
			{if count($offers) >= 1}
				<div id="offer-list">
					{foreach $offers as $offer}
						<div class="flex justify-between items-center bg-[#FAFAFB] rounded-lg px-5 py-2 mb-1 {if $iterator->counter > 6}hidden extra-offers{/if} {if $iterator->counter == 6}opacity-10{/if}">
							<div class="text-sm text-dark-1 leading-[24.5px]">{$offer->getName()}</div>
							<div class="shop-offers text-xl text-secondary-green font-bold leading-[35px]">
								{$offer |reward:true,'common'|noescape}
							</div>
						</div>
					{/foreach}
				</div>

				{if count($offers) >= 6}
					<div id="show-more-offers" class="relative top-[-53px] flex items-center justify-center gap-1.5 cursor-pointer mt-3 text-sm leading-[24.5px] underline text-dark-2 xl:hover:no-underline">
						{_newFront.shops.shop.shop.offers.more}
						<svg xmlns="http://www.w3.org/2000/svg" width="10" height="7" viewBox="0 0 10 7" fill="none">
							<path d="M1 1L4.38967 5.23708C4.70256 5.6282 5.29744 5.6282 5.61033 5.23708L9 1" stroke="#646C7C"/>
						</svg>
					</div>
				{/if}
			{/if}

			<div class="mb-5"></div>

			{*Pre-redirect message for web*}
			<div n:if="$warningMessage = $shop->getWarningMessage()" class="bg-pastel-orange-light text-primary-orange text-sm px-5 py-[15px] rounded-lg leading-[24.5px] font-medium mb-2.5">
				{$warningMessage |noescape}
			</div>

			<div n:if="$infoMessage = $shop->getInfoMessage()" class="bg-[#F8F3EE] text-[#6B5E62] text-sm px-5 py-[15px] font-medium rounded-lg leading-[24.5px] mb-2.5">
				{$infoMessage}
			</div>

			<div n:if="$cashbackConditions = $shop->getConditions()" class="bg-[#F8F8F9] text-slate-700 text-sm px-5 py-[15px] font-medium rounded-lg leading-[24.5px] mb-2.5">
				{$cashbackConditions}
			</div>
		</div>
	</div>
{/define}

{block styles}

	<link rel="preload" fetchpriority="high" as="image" href="/new-design/wp-mobile-bg-orange-small.svg" as="image" type="image/svg+xml">

	<style>
		.bg-img {
			left: 0;
			z-index: 10;
			position: absolute;
			width: 100%;
			height: 367px;
			background-image: url(/new-design/wp-mobile-bg-orange-small.svg);
			background-repeat: no-repeat;
			background-size: cover;
			background-position: bottom;
		}

		.no-scroll {
			overflow: hidden;
		}

		.description-transparency::after {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			width: 100%;
			height: 1.5em;
			background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1) 80%);
		}

		@media only screen and (max-width: 768px) {
			.description-transparency::after {
				background: linear-gradient(to right, rgba(255,255,255,0), rgba(244, 244, 246,1) 75%);
			}
		}
	</style>
{/block}

{block scripts}
	<script src="{$basePath}/js/shop.front-new.js?v=0.04" defer></script>
{/block}

{block footerBackgroundColor}bg-light-6{/block}

{block content}
{var $offers = $shopOffers()}
{var $campaignBonusAmount = $getCampaignBonusAmount()}

<div class="bg-light-6 relative">
	<div class="hidden md:block absolute w-full h-[220px] bg-repeat z-10" style="background-image:url('{$basePath}/new-design/profile-shop/bg-profile-shop.svg'); background-position: 0 -166px;">
	</div>

	<div class="bg-img lg:hidden"></div>

	<div class="container relative z-20 xl:p-0">
		<div id="conditions-modal"
			 class="hidden fixed z-30 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center px-5">
			<div class="relative bg-white m-auto p-5 w-full max-w-[500px] rounded-2xl">
				<div id="close-conditions-modal" class="hover:cursor-pointer absolute top-[-19px] right-[-28px]">
					<img src="{$basePath}/new-design/close-btn.svg" alt="close" loading="lazy">
				</div>
				<div class="text-lg text-dark-1 font-bold leading-[31.5px]">{_newFront.shops.shop.shop.conditions.gambleTitle}
				</div>
				<div class="w-full h-px bg-light-5 mt-[19px] mb-[20px]"></div>

				{if count($offers) >= 1}
					{foreach $offers as $offer}
						<div class="flex justify-between items-center bg-[#FAFAFB] rounded-lg px-5 py-2 mb-1 ">
							<div class="text-sm text-dark-1 leading-[24.5px]">{$offer->getName()}</div>
							<div class="text-secondary-green font-bold leading-[35px]">{$offer|reward:true|noescape}</div>
						</div>
					{/foreach}
				{/if}

				<div class="mb-5"></div>

				<div n:if="$warningMessage = $shop->getMobileWarningMessage()"
						class="bg-pastel-orange-light text-primary-orange text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5">
					{$warningMessage |noescape}
				</div>

				<div class="bg-[#F8F3EE] text-[#6B5E62] text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5"
						n:if="$infoMessage = $shop->getInfoMessage()">
					{$infoMessage}
				</div>

				<div class="bg-[#F8F8F9] text-slate-700 text-xs px-5 py-[15px] rounded-lg leading-[21px] font-medium mt-2 mb-5"
						n:if="$cashbackConditions = $shop->getConditions()">
					{$cashbackConditions}
				</div>
			</div>
		</div>

		<div
				class="hidden xl:flex px-5 justify-between items-center text-dark-2 text-xs leading-[21px] md:text-sm md:leading-[24.5px] mb-6 md:mb-[25px] md:mt-5">
			<div class="flex gap-[10px] items-center">
				<svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12"
					 fill="none">
					<path
							d="M2.46667 6.95165V11H5.4V8.05575C5.4 7.86049 5.47726 7.67329 5.61477 7.53525C5.7523 7.39722 5.93886 7.31968 6.13334 7.31968H6.86667C7.06115 7.31968 7.24766 7.39722 7.38523 7.53525C7.52276 7.67329 7.6 7.86049 7.6 8.05575V11H10.5333V6.95165M1 6.21559L5.98129 1.21575C6.04939 1.14735 6.13026 1.09309 6.21923 1.05607C6.30826 1.01905 6.40364 1 6.5 1C6.59636 1 6.69174 1.01905 6.78072 1.05607C6.86975 1.09309 6.95061 1.14735 7.01871 1.21575L12 6.21559"
							stroke="#646C7C" stroke-width="0.858714" stroke-linecap="round"
							stroke-linejoin="round" />
				</svg>

				{if $navigationTag}
					<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
						<path
								d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z"
								fill="#646C7C" />
					</svg>
					<a class="hover:underline hover:cursor-pointer"
							n:href=":NewFront:Shops:Shops:, $navigationTag">
						{$navigationTag->getName()}
					</a>
				{/if}

				<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
					<path
							d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z"
							fill="#646C7C" />
				</svg>
				<div>{$shop->getName()}</div>
			</div>

			{if $isUserLoggedIn}
				{if $isShopFavorite}
					<a n:href="removeFromFavourites!"
							class="hidden xl:flex items-center gap-[10px] hover:cursor-pointer xl:hover:underline">
						{_newFront.shops.shop.shop.favorites.remove}
						<svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16"
							 fill="none">
							<path
									d="M15.9867 2.13876C15.6713 1.7789 15.2917 1.49243 14.8715 1.29667C14.4512 1.10093 13.9986 1 13.5413 1C13.0841 1 12.6315 1.10093 12.2112 1.29667C11.7909 1.49243 11.4114 1.7789 11.096 2.13876L9 4.49434L6.904 2.13876C6.58862 1.7789 6.20907 1.49243 5.7888 1.29667C5.36848 1.10093 4.91595 1 4.45867 1C4.00139 1 3.54885 1.10093 3.12853 1.29667C2.7082 1.49243 2.3288 1.7789 2.01333 2.13876C1.36292 2.87932 1 3.86378 1 4.8876C1 5.91161 1.36292 6.89601 2.01333 7.63653L8.25511 14.6533C8.35129 14.7629 8.46685 14.8502 8.59485 14.9098C8.72285 14.9693 8.8608 15 9 15C9.1392 15 9.27715 14.9693 9.40516 14.9098C9.53315 14.8502 9.64871 14.7629 9.74489 14.6533L15.9867 7.63653C16.6372 6.89601 17 5.91161 17 4.8876C17 3.86378 16.6372 2.87932 15.9867 2.13876Z"
									fill="url(#paint0_linear_827_2476)" stroke="url(#paint1_linear_827_2476)"
									stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
							<defs>
								<linearGradient id="paint0_linear_827_2476" x1="1" y1="15" x2="13.9429" y2="2.99748"
												gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A" />
									<stop offset="1" stop-color="#FFA439" />
								</linearGradient>
								<linearGradient id="paint1_linear_827_2476" x1="1" y1="15" x2="13.9429" y2="2.99748"
												gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A" />
									<stop offset="1" stop-color="#FFA439" />
								</linearGradient>
							</defs>
						</svg>
					</a>
				{else}
					<a n:href="addToFavourites!"
							class="hidden xl:flex items-center gap-[10px] hover:cursor-pointer xl:hover:underline">
						{_newFront.shops.shop.shop.favorites.add}
						<img src="{$basePath}/new-design/profile-shop/heart.svg" alt="heart" loading="lazy">
					</a>
				{/if}
			{/if}
		</div>

		<div class="flex flex-col xl:flex-row xl:gap-5">
			<div class="mt-10 xl:mt-0 xl:w-[310px] flex-shrink-0">
				<div class="mx-5 relative border-2 rounded-2xl border-primary-orange mb-5 xl:mx-0"
					 style="background: linear-gradient(180deg, #FFF 33.01%, #FEF3E9 77.61%)">
					<div class="absolute right-[20px] top-[21px] xl:hidden">
						{if $isUserLoggedIn}
							{if $isShopFavorite}
								<a n:href="removeFromFavourites!">
									<svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16" fill="none">
										<path
												d="M15.9867 2.13876C15.6713 1.7789 15.2917 1.49243 14.8715 1.29667C14.4512 1.10093 13.9986 1 13.5413 1C13.0841 1 12.6315 1.10093 12.2112 1.29667C11.7909 1.49243 11.4114 1.7789 11.096 2.13876L9 4.49434L6.904 2.13876C6.58862 1.7789 6.20907 1.49243 5.7888 1.29667C5.36848 1.10093 4.91595 1 4.45867 1C4.00139 1 3.54885 1.10093 3.12853 1.29667C2.7082 1.49243 2.3288 1.7789 2.01333 2.13876C1.36292 2.87932 1 3.86378 1 4.8876C1 5.91161 1.36292 6.89601 2.01333 7.63653L8.25511 14.6533C8.35129 14.7629 8.46685 14.8502 8.59485 14.9098C8.72285 14.9693 8.8608 15 9 15C9.1392 15 9.27715 14.9693 9.40516 14.9098C9.53315 14.8502 9.64871 14.7629 9.74489 14.6533L15.9867 7.63653C16.6372 6.89601 17 5.91161 17 4.8876C17 3.86378 16.6372 2.87932 15.9867 2.13876Z"
												fill="url(#paint0_linear_827_2476_1)" stroke="url(#paint1_linear_827_2476_1)"
												stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
										<defs>
											<linearGradient id="paint0_linear_827_2476_1" x1="1" y1="15" x2="13.9429" y2="2.99748" gradientUnits="userSpaceOnUse">
												<stop stop-color="#EF7F1A" />
												<stop offset="1" stop-color="#FFA439" />
											</linearGradient>
											<linearGradient id="paint1_linear_827_2476_1" x1="1" y1="15" x2="13.9429" y2="2.99748" gradientUnits="userSpaceOnUse">
												<stop stop-color="#EF7F1A" />
												<stop offset="1" stop-color="#FFA439" />
											</linearGradient>
										</defs>
									</svg>

								</a>
							{else}
								<a n:href="addToFavourites!">
									<img src="{$basePath}/new-design/profile-shop/heart.svg" alt="heart" loading="lazy">
								</a>
							{/if}
						{/if}
					</div>
					<div class="py-5 px-5 h-[140px] flex items-center justify-center">
						{include logo}
					</div>
					<div>
						<img class="m-auto" src="{$basePath}/new-design/divider-profile-shop.svg" alt="divider" loading="lazy">
					</div>

					<div class="text-center leading-[28px] text-lg text-dark-2 {if $isUserLoggedIn}my-[30px] md:my-[35px]{else}my-[23px] md:my-[35px] reward-box--small{/if}">
						{cache 'shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' => ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
							{$shop |reward:true,'complete' |noescape}
						{/cache}
					</div>

					<div class="px-5 mb-4">
						{include ctaLink}
					</div>

					<div id="open-conditions-modal"
						 class="flex items-center justify-center text-xs text-dark-2 mb-4 xl:hidden hover:cursor-pointer">
						<svg class="mr-1.5" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15"
							 fill="none">
							<path
									d="M7.5 14C11.0898 14 14 11.0898 14 7.5C14 3.91015 11.0898 1 7.5 1C3.91015 1 1 3.91015 1 7.5C1 11.0898 3.91015 14 7.5 14Z"
									stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
							<path d="M7.42871 6.72852V10.7148" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round"
								  stroke-linejoin="round" />
							<circle cx="7.49974" cy="4.90013" r="0.483333" stroke="currentColor" stroke-width="0.1" />
						</svg>
						{_newFront.shops.shop.shop.conditions.title}
					</div>
				</div>


				{if $presenter->getHttpRequest()->getCookie('d2s0KZA1rp9pwsRI9n0l')}
					<div class="text-block shop-profile--text-block mb-3">
						<div class="_content">
							<a n:href="aqPopup-open!, aqPopup-type => exit, aqPopup-shopId => $shop->getId()" class="ajax"
																											  data-ajax-call="js-open-popup">EXIT POPUP (s obchodem)</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
									class="ajax" data-ajax-call="js-open-popup">SHOP REDIRECTION POPUP</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => default" class="ajax"
																			   data-ajax-call="js-open-popup">DEFAULT POPUP</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => password" class="ajax"
																				data-ajax-call="js-open-popup">PASSWORD POPUP</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => education, aqPopup-shopId => $shop->getId()"
									class="ajax" data-ajax-call="js-open-popup">EDUCATION POPUP</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()"
									class="ajax" data-ajax-call="js-open-popup">REDIRECT POPUP</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()" class="ajax"
																											   data-ajax-call="js-open-popup">ADDON</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => shopWarning, aqPopup-shopId => $shop->getId()"
									class="ajax" data-ajax-call="js-open-popup">SHOP WARNING</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => addonAfterRegistration" class="ajax"
																							  data-ajax-call="js-open-popup">ADDON After Registration</a>
							<br>
							<a n:href="aqPopup-open!, aqPopup-type => virtualCoupon,  aqPopup-shopId => $shop->getId()" class="ajax"
																														data-ajax-call="js-open-popup">Virtual coupon
							</a><br>
							<button class="open-register-popup"
									data-ajax-call="js-open-popup">Registration popup
							</button>
						</div>
					</div>
				{/if}

				<div id="desktop-sidebar">
					{cache 'shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
						{var $similarShops = $recommendedShops()}
						<div class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$similarShops">
							<div class="text-lg text-dark-1 font-medium leading-[31.5px]">
								{_newFront.shops.shop.shop.relatedGambleShopsTitle}</div>
							<div class="w-full h-px bg-light-5 my-5"></div>

							{foreach $similarShops as $shopItem}
								<a href="{plink Shop:default, $shopItem}"
								   class="flex gap-4 items-center mb-5 last:mb-0 hover:underline">
									<div
											class="border border-light-5 rounded-xl flex justify-center items-center w-[97px] h-[55px] shadow-hover">
										<img class="max-w-[58px] max-h-[38px]" alt="{$shopItem->getName()}"
											 src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}"  loading="lazy" />
									</div>

									<div class="text-sm text-dark-1 leading-[18.75px]">
										<div class="leading-[24.5px] mb-1.5 line-clamp-1">{$shopItem->getName()}</div>
									</div>
								</a>
							{/foreach}
						</div>
					{/cache}

                    {cache 'reviews-' . $shop->getId() . '-' . $localization->getLocale(), expire => '168 hours', tags => ['shop/' . $shop->getId()]}
						<div id="shopReview" class="hidden bg-white p-5 rounded-2xl mb-5 xl:block" n:if="$reviews->isEmpty() === false">
							<div class="text-lg text-dark-1 font-medium leading-[31.5px]">
								{_newFront.shops.shop.shop.sidebarMenu.review, [shop => $shop->getName()]}
							</div>

							<div class="w-full h-px bg-light-5 mt-[18px] mb-5"></div>

							{foreach $reviews as $review}
								<div class="flex items-center gap-2 mb-[3px]">
									<div class="text-sm text-dark-1 font-medium leading-[24.5px]">{$review->getShortUsername()}
									</div>
									<div>
										{include stars.latte, 'rate' => $review->getRate()}
									</div>
								</div>
								<div class="text-sm text-dark-2 leading-[24.5px] mb-5 last:mb-0">
									{$review->getText() |truncate: 100}
								</div>
							{/foreach}

							<div class="w-full h-px bg-light-5 mt-[18px] mb-[25px]"></div>

							<div class="flex items-center gap-2 text-sm text-dark-1 leading-[24.5px]">
								{var $averageShopReview = $getAverageShopReview()}
								<div>
									{include stars.latte, 'rate' => $review->getRate()}
								</div>
								<div class="font-bold">{number_format($averageShopReview, 1)}
									<span class="font-normal">({$reviews->getTotalCount()})</span>
								</div>
							</div>
						</div>
					{/cache}
				</div>
			</div>

			<div>
				<div class="md:bg-white md:pt-0 p-5 mb-5 rounded-2xl">
					<h1 class="text-[26px] text-dark-1 font-bold leading-[39px] xl:text-[40px] xl:leading-[58px] mb-2">
						{$shop->getName()}
					</h1>

					{cache 'shopShortDescriptionBlock-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
						{var $shortDescriptionBlock = $descriptionBlock('short_description')}
						<div class="text-sm leading-[24.5px] text-dark-1 relative">
							{if isset($shortDescriptionBlock[0])}
								{var $length = strlen(strip_tags($shortDescriptionBlock[0]->getDescription()))}

								<div id="description" class="description-transparency shop-description text-block shop-profile--text-block line-clamp-2">
									<div class="absolute right-[-17px] -top-[20px]">
										<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-short_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
											<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
										</a>
									</div>
									{$shortDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
								</div>
							{/if}

							<div id="show-more" n:if="$shortDescriptionBlock" class="hidden md:block absolute right-0 top-[24px] bg-light-6 md:bg-white text-end text-sm leading-[24.5px] cursor-pointer underline xl:hover:no-underline">
								{_newFront.shops.shop.shop.showAllText}
							</div>
							<div id="show-more-mobile" n:if="$shortDescriptionBlock" class="mr-10 absolute md:hidden text-primary-orange right-0 top-[24px] bg-light-6 text-end text-sm leading-[24.5px] cursor-pointer underline">
								{_newFront.shops.shop.shop.showAllText}
							</div>
						</div>
					{/cache}
				</div>

				{if $user->isLoggedIn()}
					{include offersWithConditions}
				{/if}

				{if $shop->isActive() && $shop->isCashbackActive() && $userEntity?->isActiveUser() === false}
					{include _guide.latte}
				{/if}

				{cache 'bestTipsDescriptionBlock-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
					<div class="">
						{var $bestTipsDescriptionBlock = $descriptionBlock('best_tips_for_sale')}

						<div class="relative bg-white rounded-2xl mt-10 px-5 md:px-10 pt-7 pb-[18px]" n:if="$bestTipsDescriptionBlock">

							<div class="absolute right-[-17px] -top-[20px]">
								<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-best_tips_for_sale" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
									<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
								</a>
							</div>

							<div class="flex items-center gap-[23px]">
								<img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

								<div class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
									{if $bestTipsDescriptionBlock[0]->getTitle()}
										{$bestTipsDescriptionBlock[0]->getTitle()}
									{else}
										{_newFront.shops.shop.shop.tips.title}
									{/if}
								</div>
							</div>

							<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

							<div class="content-block">
								{$bestTipsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
							</div>
						</div>

						{if $shop->getShopQuestions()->count() > 0}
							{include '../snippets/question-answer.latte'}
						{/if}

						{var $couponsDescriptionBlock = $descriptionBlock('coupon_instructions')}

						<div id="couponsDescription" class="relative bg-white rounded-2xl mt-5 px-5 md:px-10 pt-7 pb-[18px]"
									n:if="$couponsDescriptionBlock">

									<div class="absolute right-[-17px] -top-[20px]">
										<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-coupon_instructions" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
											<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
										</a>
									</div>

									<div class="flex items-center gap-[23px]">
										<img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

										<div class="text-dark-1 text:base md:text-lg font-medium md:leading-7">
											<h2 class="shop-detail__section-title">
												{_'front.shops.shop.shop.couponsDescriptionTitle',
												[name => $shop->getName()]}</h2>
										</div>
									</div>

									<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

									<div class="content-block">
										{$couponsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
									</div>
						</div>

						{var $longDescriptionBlock = $descriptionBlock('long_description')}

						<div class="relative bg-white rounded-2xl mt-5 px-5 md:px-10 pt-7 pb-[18px] mb-[50px] md:mb-5"
								n:if="$longDescriptionBlock">

							<div class="absolute right-[-17px] -top-[20px]">
								<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-long_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
									<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
								</a>
							</div>

							<div class="flex items-center gap-[23px]">
								<img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

								<div class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
									{if $longDescriptionBlock[0]->getTitle()}
										{$longDescriptionBlock[0]->getTitle()}
									{/if}
								</div>
							</div>

							<div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

							<div class="content-block text-dark-1 text-sm leading[24.5px] mb-[30px]">
								{$longDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
							</div>
						</div>
					</div>
				{/cache}

				<div class="max-w-full">
					<div n:if="$user->isLoggedIn() === false"
							class="flex flex-col xl:hidden items-center justify-between bg-primary-blue-dark pt-[37px] pb-[34px] px-10 xl:py-10 xl:pl-[44px] md:rounded-2xl relative xl:mx-0 xl:pr-2.5 mb-[50px] xl-[41px]">
						<div
								class="px-5 text-center text-base text-white font-bold leading-[28px] mb-4 xl:mb-0 xl:text-start xl:px-0 xl:leading-[35px] xl:text-[20px] xl:max-w-[62%]">
							{_newFront.shops.shop.shop.shopDetail.promo.promoTextMobile1, ['shop' =>$shop->getName(), 'count' => $countOfCashbackShops] |noescape}
						</div>
						<div
								class="bg-[#66b94080] uppercase text-xs font-bold text-white mb-6 py-2 pr-3 pl-[2px] xl:pl-[3px] rounded-full xl:text-sm xl:mb-0 xl:absolute xl:left-[393px] xl:bottom-[34px]">
							<span class="bg-secondary-green py-[7px] px-[9px] rounded-full mr-[6px]">{_newFront.shops.shop.shop.shopDetail.promo.bonusAmount, ['amount' => $campaignBonusAmount]}</span>
							{_newFront.shops.shop.shop.shopDetail.promo.bonus}
						</div>
						<button
								class="open-register-popup flex w-full xl:w-auto justify-center items-center gap-[10px] text-primary-orange font-bold leading-[28px] border border-primary-orange rounded-xl pt-[17px] pb-[16px] px-[19px]">
							{_newFront.shops.shop.shop.shopDetail.promo.cta}
						</button>
					</div>

					<div id="mobile-sidebar"></div>
				</div>
			</div>
		</div>
	</div>
</div>

{snippetArea signUp}
	{include gambleSignUpModal.latte, shop: $shop}
{/snippetArea}

