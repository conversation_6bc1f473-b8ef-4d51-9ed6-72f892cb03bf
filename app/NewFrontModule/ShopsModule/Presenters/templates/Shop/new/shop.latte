s{define logo}
    {var $shopCookie = "shop-cookie-" . $shop->getId()}
    {if $shop->isCashbackAllowed()}
        {if $shop->isCashbackActive()}
            {if $isUserLoggedIn}
                {if $popup}
                    <a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
                            class="shop-profile__logo-img-wrapper w-full" data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                            data-action="click" data-label="cashbackPopupBtnLogOut"
                            data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
                        <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
                    </a>
                {else}
                    <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
                                                                                            target="_blank"
                                                                                            data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
                                                                                            data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
                                                                                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                                                                                            data-action="click" data-label="cashbackBtnLogIn">
                        <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
                    </a>
                {/if}
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
                        class="shop-profile__logo-img-wrapper w-full"
                        onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
                        class="" data-shop-id="{$shop->getId()}">
                    <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
                </a>
            {/if}
        {else}
            <div class="shop-profile__logo-img-wrapper w-full">
                <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
            </div>
        {/if}
    {else}
        {if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
            <a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
                    class="ajax shop-profile__logo-img-wrapper w-full" data-ajax-call="js-open-popup" data-hit="event"
                    data-category="shopDetail" data-action="click" data-label="noCashbackPopupBtnLogOut">
                <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
            </a>
        {else}
            {if $isUserLoggedIn}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
                                                                                        target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
                                                                                        data-label="noCashbackRedirectBtnLogIn">
                    <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
                </a>
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" class="shop-profile__logo-img-wrapper w-full"
                                                                                        target="_blank" data-hit="event" data-category="shopDetail" data-action="click"
                                                                                        data-label="noCashbackRedirectBtnLogOut">
                    <img class="m-auto max-w-[80px] md:max-w-[115px] max-h-16 w-full" src="{$shop->getCurrentLogo() |image:340,0}" alt="logo">
                </a>
            {/if}
        {/if}
    {/if}
{/define}

{define ctaLink}
    {var $shopCookie = "shop-cookie-" . $shop->getId()}
    {if $shop->isCashbackAllowed()}
        {if $shop->isCashbackActive()}
            {if $isUserLoggedIn}
                {if $popup}
                    <a n:href="aqPopup-open!, aqPopup-type => $popup, aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl"
                            class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                            data-ajax-call="js-open-popup" data-toggle="tooltip" data-placement="top"
                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                            data-action="click" data-label="cashbackPopupBtnLogOut"
                            data-popup-addon="{if $popup && $popup === 'addon'}{link aqPopup-open!, aqPopup-type => addon, aqPopup-shopId => $shop->getId()}{/if}">
                        {if $shop->getShopData()->getRedirectLabel()}
                            {$shop->getShopData()->getRedirectLabel()}
                        {else}
                            {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                        {/if}
                    </a>
                {else}
                    <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                                                                                            class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                                                                                            data-redirect-popup="{link aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $shop->getId()}"
                                                                                            data-shop-id="{$shop->getId()}" data-toggle="tooltip" data-placement="top"
                                                                                            title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
                                                                                            data-action="click" data-label="cashbackBtnLogIn">
                        {if $shop->getShopData()->getRedirectLabel()}
                            {$shop->getShopData()->getRedirectLabel()}
                        {else}
                            {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                        {/if}
                    </a>
                {/if}
            {else}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl, userId => $unLoggedRedirectionUserId"
                        class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                        onclick="javascript:window.open({link aqPopup-open!, aqPopup-type => 'shopRedirection', aqPopup-shopId => $shop->getId(), deepUrl => $deepUrl}, '_blank');"
                        data-shop-id="{$shop->getId()}">
                    {if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.shops.shop.shop.shopDetail.btntitle'} 🛒
                    {/if}
                </a>
            {/if}
        {else}
            {if $shop->isPaused() === true}
                <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                    class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                    data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
                    data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
                    {if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.nocashbackShop.goToShop'} 🛒
                    {/if}
                </a>
            {else}
                <span class="shop-detail__button shop-detail__button--disabled">
					{if $shop->getShopData()->getRedirectLabel()}
                        {$shop->getShopData()->getRedirectLabel()}
                    {else}
                        {_'front.shops.shop.shop.btnNoCashback'} 🛒
                    {/if}
				</span>
            {/if}
        {/if}
    {else}
        {if !$isUserLoggedIn && $presenter->getHttpRequest()->getCookie($shopCookie) == null}
            <a n:href="aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()"
                    class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                    data-ajax-call="js-open-popup" data-hit="event" data-category="shopDetail" data-action="click"
                    data-label="noCashbackPopupBtnLogOut">
                {if $shop->isGamble()}
                    {_'newFront.nocashbackShop.goToShopGamble'}
                {else}
                    {_'front.nocashbackShop.goToShop'} 🛒
                {/if}
            </a>
        {else}
            <a n:href=":NewFront:Shops:Redirection:shop $shop, deepUrl => $deepUrl" target="_blank"
                class="flex justify-center w-full bg-orange-gradient rounded-xl leading-[28px] font-bold text-white py-3.5 cursor-pointer xl:hover:bg-orange-gradient-hover"
                data-hit="event" data-category="shopDetail" data-action="click" {if $isUserLoggedIn}
                data-label="noCashbackRedirectBtnLogIn" {else} data-label="noCashbackRedirectBtnLogOut" {/if}>
                {if $shop->isGamble()}
                    {_'newFront.nocashbackShop.goToShopGamble'}
                {else}
                    {_'front.nocashbackShop.goToShop'} 🛒
                {/if}
            </a>
        {/if}
    {/if}
{/define}

{block content}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js"></script>


<script n:syntax="off">
    document.addEventListener("DOMContentLoaded", () => {
        gsap.registerPlugin(ScrollTrigger);

        let sidebarTriggers = [];

        let timeout;
        function refresh() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                sidebarTriggers.forEach(t => t.kill());
                sidebarTriggers = [];

                ScrollTrigger.matchMedia({
                    "(min-width: 1024px)": () => {
                        [1,2,3].forEach(n => {
                            const content = document.querySelector(`.section-${n}-length`);
                            const sidebar = document.querySelector(`.sidebar-shop-detail-section-${n}`);
                            if (content && sidebar) {
                                const offset = n === 1 ? 98 : n === 3 ? -10 : 0;
                                const pin = Math.max(0, content.offsetHeight - sidebar.offsetHeight - offset);
                                if (pin > 0) {
                                    const trigger = ScrollTrigger.create({
                                        trigger: `.main-section-shop-detail-section-${n}`,
                                        start: "top-=100 top",
                                        end: `+=${pin}`,
                                        pin: `.sidebar-shop-detail-section-${n}`
                                    });
                                    sidebarTriggers.push(trigger);
                                }
                            }
                        });
                    }
                });
            }, 100);
        }

        refresh();

        const section3 = document.querySelector('.section-3-length');
        if (section3) {
            new ResizeObserver(refresh).observe(section3);
        }

        const tabs = document.querySelector('.sticky-tabs');

        if (tabs) {
            const isMobile = window.innerWidth <= 768;
            const startPosition = isMobile ? "top 20px" : "top 40px";

            ScrollTrigger.create({
                trigger: tabs,
                start: startPosition,
                end: "max",
                pin: true,
                pinSpacing: false,
                onUpdate: self => {
                    tabs.style.zIndex = "9999"
                    if (self.isActive) {
                        tabs.classList.add("has-bg");
                    } else {
                        tabs.classList.remove("has-bg");
                    }
                }
            });
        }
    });
</script>


<div class="bg-light-6">
    <div class="hidden lg:block absolute w-full lg:h-[278px] bg-repeat" style="background-image:url('{$basePath}/new-design/bg-profile-shop.svg'); background-position: 0"></div>
    <div class="lg:container relative md:px-5 lg:px-0 z-30 lg:pt-10">
        <div class="flex flex-col lg:flex-row lg:gap-[60px] main-section-shop-detail-section-1">
            <div class="h-max sidebar-shop-detail-section-1 w-full lg:max-w-[335px] shrink-0">
                <div class="lg:mb-[19px]">
                    <div class="lg:bg-white lg:pb-5 rounded-2xl relative z-10" style="box-shadow: 0 7px 15.2px 0 rgba(0, 0, 0, 0.03);">
                        <div class="relative mb-16">
                            {if $shop->getMobileCover()}
                                <img class="lg:rounded-2xl object-cover max-h-[130px] lg:min-h-[180px] w-full" src="{$shop->getMobileCover() |image}" alt="">
							{else}
								<span class="block bg-white lg:rounded-2xl object-cover h-[130px] lg:h-[200px] w-full"><span>
                            {/if}

                            <div style="box-shadow: 0 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="hidden lg:flex absolute left-1/2 shadow-sm bottom-0 transform -translate-x-1/2 bg-white rounded-2xl justify-center items-center w-[177px] min-h-[81px] h-auto p-2 {if $shop->getMobileCover()}translate-y-1/2{/if}">
                                {include logo}
                            </div>

                            <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white w-[163px] h-[53px] rounded-lg absolute shadow-sm bottom-0 transform translate-y-1/2">
                                <div class="flex items-center justify-center h-full w-full">
                                    <div class="flex items-center justify-center ml-auto">
                                        {include logo}
                                    </div>
                                    <div class="flex ml-auto">
                                        <div class="h-[41px] w-px bg-light-4"></div>
                                        <svg width="45" height="41" viewBox="0 0 45 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="45" height="41" rx="6" transform="matrix(-1 0 0 1 45 0)" fill="white"/>
                                            <path d="M20.0002 15.8H18.5602C17.6641 15.8 17.2157 15.8 16.8734 15.9744C16.5724 16.1278 16.3278 16.3724 16.1744 16.6734C16 17.0157 16 17.4641 16 18.3602V24.4402C16 25.3362 16 25.7841 16.1744 26.1263C16.3278 26.4274 16.5724 26.6724 16.8734 26.8258C17.2154 27 17.6632 27 18.5575 27H24.6425C25.5368 27 25.984 27 26.3259 26.8258C26.627 26.6724 26.8724 26.4271 27.0258 26.1261C27.2 25.7842 27.2 25.3368 27.2 24.4425V23M28 19V15M28 15H24M28 15L22.4 20.6" stroke="#080B10" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <div class="hidden lg:block w-full max-w-[178px] m-auto text-center text-dark-1 font-medium leading-[28px] mb-[22px]">
                                {_newFront.shops.shop.shop.ctaBox.getReward}

                                {if $isUserLoggedIn && $userIdentity->isActiveUser()}
                                    {cache 'new-shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
                                    ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                        {$shop |reward:true,'common' |noescape}
                                    {/cache}
                                {else}
                                    {cache 'new-shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
                                    ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                        {$shop |reward:true,'common' |noescape}
                                    {/cache}
                                {/if}

                                {_newFront.shops.shop.shop.ctaBox.fromPurchase}
                            </div>

                            <div class="mx-5">
                                {include ctaLink}
                            </div>
                        </div>
                    </div>

                    <div class="hidden lg:block relative z-5 text-center text-sm leading-[24.5px] border border-light-4 border-t-0 rounded-b-2xl relative top-[-14px] pt-8 pb-[25px]">
                        <div class="text-dark-1 font-medium mb-[5px]">{_newFront.shops.shop.shop.ctaBox.title}</div>
                        <div class="text-dark-2 mb-2.5 m-auto w-full max-w-[225px]">{_newFront.shops.shop.shop.ctaBox.newText}</div>
                        <a class="open-register-popup block text-primary-orange hover:underline hover:cursor-pointer font-medium">
                            {_newFront.shops.shop.shop.ctaBox.more}
                        </a>
                    </div>
                </div>

                <div class="hidden text-dark-1 font-medium leading-7 p-5 pb-0 border border-light-4 rounded-2xl mb-[30px] xl:block" n:if="
                    $descriptionBlock('short_description') ||
                    $descriptionBlock('coupon_instructions') ||
                    count($reviews) > 0 ||
                    ($hasShopAllowedDeals && !$user->isLoggedIn() && !$getExpiredCoupons()->isEmpty())
                ">
                    {_'newFront.shops.shop.shop.navigation'}
                    <div class="w-full h-px bg-light-4 mt-[17px] mb-5"></div>


                    <a href="#description"
                       class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]"
                            n:if="$descriptionBlock('short_description')">
                        {_'front.shops.shop.shop.shopDetail.nav.aboutShop', [shop => $shop->getName()]}
                    </a>

                    <a href="#shopReview" data-offset="70"
                       class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]"
                            n:if="count($reviews)">
                        {_'front.shops.shop.shop.sidebarMenu.review', [shop => $shop->getName()]}
                    </a>

                    <a href="#expired"
                            n:if="$hasShopAllowedDeals && !$user->isLoggedIn() && !$getExpiredCoupons()->isEmpty()"
                       data-offset="70"
                       class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]">
                        {if $shop->getId() === 50}
                            {_'front.shops.shop.shop.expiredCouponsTitleCedok', [shop => $shop->getName()]}
                        {else}
                            {_'front.shops.shop.shop.sidebarMenu.expired', [shop => $shop->getName()]}
                        {/if}
                    </a>

                    <a href="#couponsDescription" n:if="$descriptionBlock('coupon_instructions')" data-offset="70"
                       class="block text-sm text-dark-1 leading-[24.5px] hover:underline hover:cursor-pointer mb-[19px]">
                        {if $descriptionBlock('coupon_instructions')[0]->getTitle()}
                            {$descriptionBlock('coupon_instructions')[0]->getTitle()}
                        {else}
                            {_'front.shops.shop.shop.sidebarMenu.howApply', [name => $shop->getName()]}
                        {/if}
                    </a>
                </div>

                {cache 'new-shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $similarShops = $recommendedShops()}
                    <div class="hidden lg:block p-5 rounded-2xl border border-light-4 rounded-2xl" n:if="$similarShops">
                        <div class="text-dark-1 font-medium leading-7 mb-[3px]">{_newFront.shops.shop.shop.favoriteShops.title}</div>
                        <div class="text-sm text-dark-2 leading-[24.5px] mb-5" n:if="$shop->isLessDetailed() === false">
                            {_newFront.shops.shop.shop.favoriteShops.text,
                            ['shop' => $shop->getName()]}
                        </div>

                        <div class="grid grid-cols-3 gap-[10px] ">
                            {foreach $similarShops as $shopItem}
                                <a href="{plink Shop:default, $shopItem}" title="{$shopItem->getName()}" class="bg-white rounded-xl shadow-hover flex items-center justify-center h-[47px]">
                                    <img class="max-w-[50px] max-h-[22px]" alt="{$shopItem->getName()}" src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}" loading="lazy">
                                </a>
                            {/foreach}
                        </div>
                    </div>
                {/cache}
            </div>

            <div class="w-full min-w-0 section-1-length">
                <div>
                    <div class="hidden lg:flex gap-[10px] text-sm leading-[24.5px] text-dark-2 items-center mb-6 relative z-30">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                            <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#646C7C" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
						<svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
							<path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
						</svg>
                        <a class="underline hover:cursor-pointer" n:href=":NewFront:Shops:Shops:">
                            {_'front.shops.shop.shop.shopDetail.nav.shop'}
                        </a>

                        {if $navigationTag}
                            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                            </svg>
                            <a class="underline hover:cursor-pointer" n:href=":NewFront:Shops:Shops:, $navigationTag">
                                {$navigationTag->getName()}
                            </a>
                        {/if}

                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                        </svg>
                        <div class="text-dark-1">{$shop->getName()}</div>
                    </div>

                    <div class="relative z-60">
                        <div class="px-5 md:px-0 text-xl lg:text-[26px] leading-[35px] lg:leading-[39px] font-bold text-dark-1 mb-[5px] -mt-5 lg:mt-0">
                            {_newFront.shop.title, ['shop' => $shop->getName()]}
                        </div>

                        {cache 'new-shopShortDescriptionBlock-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                            {var $shortDescriptionBlock = $descriptionBlock('short_description')}
                            {if isset($shortDescriptionBlock[0])}
                                <div id="description" class="relative shop-description description-transparency px-5 md:px-0 text-dark-2 leading-[21px] lg:leading-[24.5px] line-clamp-2 text-xs lg:text-sm transition-all duration-300">
                                    <div class="absolute right-[-17px] -top-[20px]">
                                        <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-short_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                            <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                                        </a>
                                    </div>
                                    {$shortDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                                </div>
                            {/if}

                            <button n:if="$shortDescriptionBlock" id="toggle-description-mobile" class="z-[900] relative lg:hidden px-5 md:px-0 text-dark-2 underline text-xs lg:text-sm">
                                {_newFront.shops.shop.shop.showAllText}
                            </button>

                            <button n:if="$shortDescriptionBlock" id="toggle-description" class="hidden text-dark-2 lg:block absolute z-[900] right-0 top-[69px] bg-light-6 md:bg-white text-end text-sm leading-[24.5px] cursor-pointer underline xl:hover:no-underline">
                                {_newFront.shops.shop.shop.showAllText}
                            </button>
                        {/cache}

                        <div class="sticky-tabs ml-5 my-[25px] lg:mb-[43px] lg:ml-0 lg:mt-10 lg:before:content-[''] lg:before:absolute lg:before:inset-x-0 lg:before:h-[278px] lg:before:bg-repeat"
                             style="--bg-image: url('{$basePath}/new-design/bg-profile-shop.svg');">
                            <div class="flex items-center gap-[5px] md:gap-3 font-medium leading-7 overflow-hidden overflow-x-auto whitespace-nowrap">
                                <a href="#coupons" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] border border-light-4 bg-white  rounded-lg hover:text-primary-orange hover:border-primary-orange">{_newFront.shop.tabs.coupons}</a>
                                <a href="#cashback" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] border border-light-4 bg-white rounded-lg hover:text-primary-orange hover:border-primary-orange">{_newFront.shop.tabs.cashback, ['reward' => ($shop |reward:false, 'common')] |noescape}</a>
                                <a href="#content" class="text-sm lg:text-base py-2.5 lg:py-3 px-[15px] lg:px-[17px] mr-10 md:mr-0 border border-light-4 bg-white rounded-lg hover:text-primary-orange hover:border-primary-orange">{_newFront.shop.tabs.aboutShop, [shop => $shop->getName()]}</a>
                            </div>
                        </div>

                        {var $coupons = $getCoupons()}
                        {if $coupons->isEmpty() === false}

							<div id="coupons" class="px-5 md:px-0">
								<div class="inline-flex items-center gap-1 text-[10px] lg:text-xs text-secondary-green border border-secondary-green leading-[13px] lg:leading-[21px] font-medium px-2 py-1 bg-pastel-green-light rounded-full">
									<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
										<rect width="13" height="13" rx="6.5" fill="#66B940"/>
										<path d="M4 6.56429L5.59524 8L9 5" stroke="white" stroke-width="1.2087" stroke-linecap="round"/>
									</svg>
									{_newFront.shop.coupons.updated}
								</div>
								<span class="text-lg lg:text-xl font-bold text-dark-1 leading-[31.5px] lg:leading-[35px] ml-1">{_newFront.shop.coupons.title, ['shop' => $shop->getName()]} <span class="font-normal">({$coupons->getTotalCount()})</span></span>
							</div>

							<div class="px-5 md:px-0 text-xs lg:text-sm leading-[21px] lg:leading-[24.5px] text-dark-2 mb-5 lg:mb-[23px] mt-[5px] lg:mt-0">
								{_newFront.shop.coupons.text}
							</div>

							{var $topCoupon = $coupons->getFirst()}
							<div class="bg-[#182B4A] rounded-2xl px-[5px] pb-[5px] mb-5 lg:mb-10 mx-5 md:mx-0" style="box-shadow: 0px 34px 31.9px -10px rgba(42, 73, 123, 0.22);">
								<div class="flex items-center justify-between px-[15px] h-[38px]">
									<div class="text-xs lg:text-sm leading-[21px] text-white font-bold lg:leading-[24.5px]">🔥 TOP</div>
									<div class="flex gap-5 py-2">
										<div class="hidden flex items-center gap-1.5 text-white text-xs leading-[21px] font-bold">
											<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
												<path opacity="0.7" d="M5.00063 9L5.00063 1M9 4.83251L5 1.00089L1 4.83251" stroke="white" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
											</svg>
											<div>214</div>
											<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
												<path opacity="0.7" d="M5.1061 1L5.1061 9M9.10547 5.16749L5.10547 8.99911L1.10547 5.16749" stroke="white" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
											</svg>
										</div>
										<div
												class="hidden hover:cursor-pointer flex items-center gap-1.5 text-primary-orange font-medium text-xs leading-[21px]">
											<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
												<path d="M9 4.42857H3.13333C2.56757 4.42857 2.02492 4.64263 1.62485 5.02366C1.22478 5.40468 1 5.92146 1 6.46032V9M9 4.42857L5.57143 7.28571M9 4.42857L5.57143 1" stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round"/>
											</svg>
											Zdieľať
										</div>
									</div>
								</div>

								<div class="rounded-t-2xl rounded-b-[14px] p-5" style="background:linear-gradient(132deg, #FEF3E9 2.4%, #FFF 97.51%);">
									<div class="flex flex-col lg:flex-row gap-3 lg:gap-5 lg:items-center lg:mb-[15px]">
										<div class="flex items-center gap-3 lg:gap-5">
											<div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="rounded-[14px] p-4 bg-white text-[35px] text-primary-orange font-bold leading-[54px]">{$topCoupon->getValue()}<span class="text-[22px] font-medium leading-[38px]">{$topCoupon->getUnitSymbol()}</span></div>
											<div class="text-sm lg:text-base leading-[22px] w-full max-w-[312px] lg:leading-7 line-clamp-3">
												<span class="font-normal">{$topCoupon |dealName:true|noescape}</span>
												{*$topCoupon->getDescription()*}
											</div>
										</div>

										<div class="flex flex-col lg:hidden items-start gap-1 lg:gap-2.5">
											<div n:if="$shop->isCashbackWithCouponAllowed()" class="flex items-center gap-1 text-xs text-secondary-green border border-secondary-green/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
												{_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')] |noescape}
											</div>
                                            {if $topCoupon->getValidTillDays() === 0}
                                                <div class="flex items-center gap-1 text-xs text-secondary-red border border-secondary-red/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
                                                    {_'newFront.deals.deal.validTillToday'}
                                                </div>
                                            {elseif $topCoupon->getValidTillDays() <= 3}
                                                <div class="flex items-center gap-1 text-xs text-orange-500 border border-orange-500/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
                                                    {_'newFront.deals.deal.validTillDays', ['count' => $topCoupon->getValidTillDays()]}
                                                </div>
                                            {else}
                                                <div class="flex items-center gap-1 text-xs text-secondary-dark border border-secondary-dark/30 lg:leading-[21px] font-medium bg-white/50 px-2 lg:px-3 py-1 rounded-full">
                                                    {_'newFront.deals.deal.validTill', ['date' => ($topCoupon->getValidTillForUser()|localDate:'d.m.Y')]}
                                                </div>
                                            {/if}
										</div>

                                        {var $id = $topCoupon->getId()}
                                        {var $isDetailable = $topCoupon->getCode() && !empty($topCoupon->getCode())}
                                        {capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $topCoupon->getFullSlug()}{/capture}

										{if false}
											<a n:href="//:NewFront:Shops:Redirection:deal $topCoupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="lg:ml-auto relative min-w-[248px] max-w-[248px] cursor-pointer h-[58px] border border-dashed border-dark-4 hover:border-primary-orange hover:border-solid rounded-[10px] overflow-visible">
												<span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-black z-0">
													{if $topCoupon->getCode() !== null && Nette\Utils\Strings::length($topCoupon->getCode()) > 3}{mb_substr($topCoupon->getCode(), -3, 3)}
													{else}
														{$topCoupon->getCode()}
													{/if}
												</span>
												<div class="absolute top-[-1px] left-[-16px] z-10">
													<svg width="248" height="58" viewBox="0 0 211 56"  fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M199 0L10 0C4.47716 0 0 4.47715 0 10V46C0 51.5229 4.47716 56 10 56H211L199 0Z"
															fill="url(#paint0_linear_7837_2928)"/>
														<defs>
															<linearGradient id="paint0_linear_7837_2928" x1="211" y1="56" x2="180.319" y2="-37.8025"
																			gradientUnits="userSpaceOnUse">
																<stop stop-color="#EF7F1A"/>
																<stop offset="1" stop-color="#FFA439"/>
															</linearGradient>
														</defs>
													</svg>
												</div>
												<svg class="absolute top-0 right-[29px] z-20" xmlns="http://www.w3.org/2000/svg" width="30" height="56" viewBox="0 0 30 56" fill="none">
													<path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z" fill="#CE6D14"/>
												</svg>
												<span class="absolute inset-0 flex items-center justify-center z-30 text-white font-bold">
													{_'newFront.deals.deal.getCode'}
												</span>
											</a>
										{else}
											<a n:href="//:NewFront:Shops:Redirection:deal $topCoupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="group/get-cupon-button relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full lg:max-w-[248px] lg:ml-auto">
												<span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
													<span class="inline-block truncate text-right w-[87%] lg:w-[95%]">
														{if $topCoupon->getCode() !== null && Nette\Utils\Strings::length($topCoupon->getCode()) > 3}
															{mb_substr($topCoupon->getCode(), -3, 3)}
														{else}
															{$topCoupon->getCode()}
														{/if}
													</span>
												</span>
												<span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-orange-gradient text-white group-hover/get-cupon-button:bg-linen-100">{_'newFront.deals.deal.getCode'}</span>
												<svg width="30" height="56" viewBox="0 0 30 56" fill="#CE6D14" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z"></path></svg>
											</a>
										{/if}
									</div>

									<div class="hidden lg:flex items-center items-start gap-2.5">
										<div n:if="$shop->isCashbackWithCouponAllowed()" class="flex items-center gap-1 text-xs lg:text-sm text-secondary-green border border-secondary-green/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
											{_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')] |noescape}
										</div>
                                        {if $topCoupon->getValidTillDays() === 0}
                                            <div class="flex items-center gap-1 text-xs lg:text-sm text-secondary-red border border-secondary-red/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
                                                {_'newFront.deals.deal.validTillToday'}
                                            </div>
                                        {elseif $topCoupon->getValidTillDays() <= 3}
                                            <div class="flex items-center gap-1 text-xs lg:text-sm text-orange-500 border border-orange-500/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
                                                {_'newFront.deals.deal.validTillDays', ['count' => $topCoupon->getValidTillDays()]}
                                            </div>
                                        {else}
                                            <div class="flex items-center gap-1 text-xs lg:text-sm text-secondary-dark border border-secondary-dark/30 leading-[21px] font-medium bg-white/50 px-3 py-1 lg:py-2 rounded-full">
                                                {_'newFront.deals.deal.validTill', ['date' => ($topCoupon->getValidTillForUser()|localDate:'d.m.Y')]}
                                            </div>
                                        {/if}
									</div>
								</div>
							</div>
						{/if}
					</div>

                    <div class="mb-5 px-5 md:px-0">
                        <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                            <div class="flex flex-col md:flex-row md:items-center gap-[15px] justify-between">
                                <div class="flex items-center gap-[15px]">
                                    <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                        <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center">150<span class="font-medium text-lg leading-[38px]">Kč</span></div>
                                    </div>
                                    <div class="text-sm leading-[22x] text-dark-1 line-clamp-3 md:max-w-[245px]"><span class="font-normal">Slevový kupon <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress</span> </div>
                                </div>

                                <a class="group/get-cupon-button md:max-w-[248px] relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full" href="https://www.tipli.czlocal/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku">
                                <span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
                                    <span class="inline-block truncate text-right w-[87%]">
                                            PLI
                                    </span>
                                </span>
                                    <span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-linen-50 text-tango-500 group-hover/get-cupon-button:bg-linen-100">Získat kupón</span>
                                    <svg width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30 fill-linen-250" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z"></path></svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row gap-5 items-center mb-5 w-full px-5 md:px-0">
                        <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] w-full rounded-[15px]">
                            <div class="flex items-center gap-[15px] mb-[15px]">
                                <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                    <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center">150<span class="font-medium text-lg leading-[38px]">Kč</span></div>
                                </div>
                                <div class="text-sm leading-[22x] text-dark-1 line-clamp-3"><span class="font-normal">Slevový kupon <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress</span> </div>
                            </div>


                            <a class="group/get-cupon-button relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full" href="https://www.tipli.czlocal/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku">
															<span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
																<span class="inline-block truncate text-right w-[87%]">
																		PLI
																</span>
															</span>
                                <span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-linen-50 text-tango-500 group-hover/get-cupon-button:bg-linen-100">Získat kupón</span>
                                <svg width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30 fill-linen-250" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.20241.83628 26.6337Z"></path></svg>
                            </a>
                        </div>

                        <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] w-full rounded-[15px]">
                            <div class="flex items-center gap-[15px] mb-[15px]">
                                <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                    <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center">150<span class="font-medium text-lg leading-[38px]">Kč</span></div>
                                </div>
                                <div class="text-sm leading-[22x] text-dark-1 line-clamp-3"><span class="font-normal">Slevový kupon <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress</span> </div>
                            </div>


                            <a class="group/get-cupon-button relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full" href="https://www.tipli.czlocal/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku">
                                <span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
                                    <span class="inline-block truncate text-right w-[87%]">
                                            PLI
                                    </span>
                                </span>
                                <span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-linen-50 text-tango-500 group-hover/get-cupon-button:bg-linen-100">Získat kupón</span>
                                <svg width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30 fill-linen-250" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z"></path></svg>
                            </a>
                        </div>
                    </div>

                    <div class="flex flex-col">
                        <div class="order-1 relative" n:if="$coupons->count() > 1">
                            <div id="swiper-coupons" class="!pl-5 md:!pl-0 swiper swiper-profile-shop swiper-coupons relative mb-[50px] lg:mb-10">
                                <div class="swiper-wrapper">
                                    {foreach $coupons as $coupon}
                                        {continueIf $coupon->getId() === $topCoupon->getId()}

                                        {var $id = $coupon->getId()}
                                        {var $isDetailable = $coupon->getCode() && !empty($coupon->getCode())}
                                        {capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $coupon->getFullSlug()}{/capture}

                                        <div class="swiper-slide pb-10">
                                            <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                                <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                                    <div class="flex items-center gap-[15px] mb-[15px]">
                                                        <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                                            {if $coupon->getValue()}
                                                                <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center">{$coupon->getValue()}<span class="font-medium text-lg leading-[38px]">{$coupon->getUnitSymbol()}</span></div>
                                                            {else}
                                                                <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center"><span class="font-medium text-lg leading-[38px]">%</span></div>
                                                            {/if}
                                                        </div>
                                                        <div class="text-sm leading-[22x] text-dark-1 line-clamp-3"><span class="font-normal">{$coupon |dealName:true|noescape}</span> {*$coupon->getDescription()*}</div>
                                                    </div>

                                                    {var $couponId = $coupon->getId()}

													{if false}
														<a n:href="//:NewFront:Shops:Redirection:deal $coupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="block relative w-full max-w-[300px] h-[56px] cursor-pointer rounded-[10px] group">
															<div class="absolute inset-0 z-20">
																<div class="absolute">
																	<svg viewBox="0 0 216 56" xmlns="http://www.w3.org/2000/svg" class="w-full h-full max-w-[216px] relative z-30" fill="none" preserveAspectRatio="xMidYMid slice">
																		<path d="M204 0H10C4.47716 0 0 4.47715 0 10V46C0 51.5228 4.47715 56 10 56H216L204 0Z" fill="#FEF3E9"/>
																		<path d="M10 0.5C4.7533 0.500003 0.5 4.7533 0.5 10V46C0.5 51.2467 4.7533 55.5 10 55.5H215.381L203.596 0.5H10Z"
																			stroke="url(#paint0_linear_0_1)" stroke-opacity="0.2"/>
																		<path d="M187.836 26.6337L204 0L216 56L190 37.2761C186.613 34.8365 185.67 30.2024 187.836 26.6337Z" fill="#E9D6C5"/>
																		<defs>
																			<linearGradient id="paint0_linear_0_1" x1="216" y1="56" x2="185.896" y2="-38.2192" gradientUnits="userSpaceOnUse">
																				<stop stop-color="#EF7F1A"/>
																				<stop offset="1" stop-color="#FFA439"/>
																			</linearGradient>
																		</defs>
																	</svg>
																	<div class="absolute top-1/2 right-[-15px] -translate-y-1/2 text-dark-3 font-consolas text-[14px] z-20">
																		{if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}{mb_substr($coupon->getCode(), -3, 3)}
																		{else}
																			{$coupon->getCode()}
																		{/if}
																	</div>
																</div>
															</div>
															<div class="absolute z-10 inset-0 rounded-[10px] border border-dashed border-dark-4 group-hover:border-primary-orange group-hover:border-solid group-hover:z-20"></div>
															<span class="text-sm lg:text-base absolute inset-0 flex items-center justify-center z-30 font-bold text-primary-orange mr-8 lg:mr-0">
															{_'newFront.deals.deal.getCode'}
														</span>
														</a>
													{else}
														<a n:href="//:NewFront:Shops:Redirection:deal $coupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="group/get-cupon-button relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full">
															<span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
																<span class="inline-block truncate text-right w-[87%]">
																	{if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}
																		{mb_substr($coupon->getCode(), -3, 3)}
																	{else}
																		{$coupon->getCode()}
																	{/if}
																</span>
															</span>
															<span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-linen-50 text-tango-500 group-hover/get-cupon-button:bg-linen-100">{_'newFront.deals.deal.getCode'}</span>
															<svg width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30 fill-linen-250" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z"></path></svg>
														</a>
													{/if}
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="right-coupons-gradient lg:block hidden pointer-events-none absolute top-0 right-[-50px] h-full w-[153px] z-30"></div>

                                <div style="background: linear-gradient(451deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="left-coupons-gradient lg:block hidden pointer-events-none absolute top-0 left-[-66px] h-full w-[153px] z-30"></div>

                                <div class="swiper-pagination swiper-coupons-pagination absolute -bottom-6 left-1/2 -translate-x-1/2 z-20"></div>
                            </div>

                            <div class="swiper-button-prev swiper-coupons-prev" style="top: 94px; left: -26px;">
                                <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                            </div>
                            <div class="swiper-button-next swiper-coupons-next z-40" style="top: 94px; right: -48px;">
                                <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                            </div>
                        </div>

                        {var $deals = $getDeals(true)}

                        <div class="order-3 lg:order-2 lg:ml-[-30px]" n:if="$deals->isEmpty() === false">
                            <div
                                class="w-full h-full bg-no-repeat bg-cover bg-center lg:bg-white lg:rounded-2xl relative z-20"
                                style="background-image:url('{$basePath}/new-design/white-top.svg')"
                            >
                                <div class="pt-[50px] pb-14 lg:py-10 lg:mb-[50px]">
                                    <div class="px-5 lg:px-[30px] z-20">
                                        <div class="text-lg lg:text-xl text-dark-1 font-bold leading-[31.5px] leading-[35px] mb-1 lg:mb-2.5">{_newFront.shop.otherDeals.title} <span class="font-normal">({$deals->count()})</span></div>
                                        <div class="text-dark-2 text-xs lg:text-sm leading-[21px] lg:leading-[24.5px] mb-5 lg:mb-[25px]">
                                            {_newFront.shop.otherDeals.text}
                                        </div>

                                        <div class="grid grid-cols-1 xl:grid-cols-2 gap-[15px] lg:gap-5">
                                            {foreach $deals as $deal}
                                                {var $id = $deal->getId()}
                                                <a href="{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}" class="flex justify-between border border-pastel-green-light hover:border-secondary-green hover:cursor-pointer hover:-translate-y-0.5 transition-all duration-200 bg-pastel-green-light/50 flex items-center rounded-2xl">
                                                    <div class="flex flex-col items-center px-5 text-secondary-green font-medium leading-[23px] text-center">
                                                        <span>🤩</span>
                                                        {*_'newFront.deals.deal.' . $deal->getType()*}
														{_'newFront.deals.deal.sale'}
                                                    </div>
                                                    <div class="text-sm bg-white rounded-l-[3px] rounded-r-[15px] w-full xl:max-w-[235px] pt-3 pb-2.5 pl-[18px] pr-[20px]">
                                                        <div class="text-dark-1 leading-[22px] mb-3.5 line-clamp-2 min-h-[44px]">
                                                            <span class="font-normal"> {$deal |dealName:true|noescape}</span> {*$deal->getDescription() |striptags|truncate:65|noescape*}</div>
                                                        <div class="text-[10px] lg:text-sm leading-[17.5px] lg:leading-[24.5px]">
                                                            {if $deal->getValidTillDays() === 0}
                                                                <span class="text-secondary-red">{_'newFront.deals.deal.validTillToday'}</span>
                                                            {elseif $deal->getValidTillDays() <= 3}
                                                                <span class="text-orange-500">{_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}</span>
                                                            {else}
                                                                <span class="text-[#80899C]">{_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}</span>
                                                            {/if}
                                                        </div>
                                                    </div>
                                                    <div class="px-4">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                                            <path d="M1 14L14 1M13.9979 12.0163L13.9978 1.00041L2.98193 1.0004" stroke="#66B940" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                </a>
                                            {/foreach}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                class="lg:hidden w-full h-[293px] bg-no-repeat bg-cover bg-center mb-[30px] -mt-2 z-10 relative"
                                style="background-image:url('{$basePath}/new-design/bg-orange-profile.png');background-position: center bottom; background-repeat: no-repeat;"
                            >
                                <div class="text-lg text-white leading-[31.5px] pt-11 pl-5 w-ful max-w-[173px] font-bold mb-5">
                                    {_newFront.shop.bluescreen.shopBox.titleMobile, ['reward' => ($shop |reward:false,'common')] |noescape}
                                </div>
                                <div
                                        style="backdrop-filter: blur(4.951241970062256px); background: rgba(255, 255, 255, 0.80);"
                                        class="text-center w-full max-w-[213px] m-auto rounded-[20px] mt-5 relative"
                                >
                                    <div class="px-10 pt-[30px] text-xl leading-7 text-[#182B4A]">
                                        <span class="font-black">{_newFront.shop.bluescreen.promo1}</span><br>{_newFront.shop.bluescreen.promo2}

                                    </div>
                                    <div class="text-xs leading-[21px] text-primary-orange underline pt-7 pb-4">
                                        {_newFront.shop.bluescreen.howItWorks}
                                    </div>

                                    <img class="absolute left-[-73px] bottom-0" src="{$basePath}/new-design/oslik-bg-orange.png" alt="oslik">

                                    <div class="absolute flex flex-col right-[-26px] bottom-2">
                                        <img src="{$basePath}/new-design/bg-white-money.svg" alt="money">
                                        <img class="-mt-2" src="{$basePath}/new-design/bg-white-thumbs-up.svg" alt="thumbs up">
                                    </div>

                                    <div class="absolute -top-10 -right-11">
                                        <div class="flex flex-col items-center justify-center text-center">
                                            <img src="{$basePath}/new-design/bg-green-flower-sm.svg" alt="flower">
                                            <div class="absolute text-xs font-bold w-[60px] leading-tight text-center text-white leading-[18px]">
                                                {_newFront.shop.bluescreen.guarantee}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {var $expiredCoupons = $getExpiredCoupons()}
                        <div class="order-2 relative lg:order-3 pl-5 md:pl-0" n:if="$expiredCoupons->isEmpty() === false">
                            <div class="text-lg lg:text-xl font-bold leading-[31.5px] lg:leading-[35px] mb-[5px] w-full max-w-[196px] lg:max-w-full">
                                {_newFront.shop.expiredCoupons.title}
                                <span class="font-normal">({$expiredCoupons->count()})</span>
                            </div>
                            <div class="text-xs leading-[21px] lg:text-sm lg:leading-[24.5px] text-dark-2 mb-5 lg:mb-[25px] pr-5 lg:pr-0">
                                {_newFront.shop.expiredCoupons.text}
                            </div>

                            <div id="swiper-older-coupons" class="swiper swiper-profile-shop swiper-older-coupons relative mb-[50px] lg:mb-[100px]">
                                <div class="swiper-wrapper">
                                    {foreach $expiredCoupons as $coupon}
                                        <div class="swiper-slide pb-10">
                                            <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                                <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                                    <div class="flex items-center gap-[15px] mb-[15px]">
                                                        <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                                            <div class="text-dark-2 font-black text-[28px] leading-[54px]">{$coupon->getValue()}<span class="font-medium text-lg leading-[38px]">{$coupon->getUnitSymbol()}</span></div>
                                                        </div>
                                                        <div class="text-sm leading-[22x] text-dark-1 line-clamp-3"><span class="font-normal">{$coupon |dealName:true|noescape}</span> {*$coupon->getDescription() |striptags|truncate:65|noescape*}</div>
                                                    </div>

                                                    {var $id = $coupon->getId()}
                                                    {var $isDetailable = $coupon->getCode() && !empty($coupon->getCode())}
                                                    {capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $coupon->getFullSlug()}{/capture}

													{if false}
														<a n:href="//:NewFront:Shops:Redirection:deal $coupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} target="_blank" class="block relative w-full max-w-[300px] h-[56px] cursor-pointer rounded-[10px] overflow-hidden group">
															<div class="absolute inset-0 z-20">
																<div class="absolute">
																	<svg viewBox="0 0 216 56" xmlns="http://www.w3.org/2000/svg" class="w-full h-full max-w-[216px] relative z-30" fill="none" preserveAspectRatio="xMidYMid slice">
																		<path d="M204 0H10C4.47716 0 0 4.47715 0 10V46C0 51.5228 4.47715 56 10 56H216L204 0Z" fill="#FEF3E9"/>
																		<path d="M10 0.5C4.7533 0.500003 0.5 4.7533 0.5 10V46C0.5 51.2467 4.7533 55.5 10 55.5H215.381L203.596 0.5H10Z"
																			stroke="url(#paint0_linear_0_1)" stroke-opacity="0.2"/>
																		<path d="M187.836 26.6337L204 0L216 56L190 37.2761C186.613 34.8365 185.67 30.2024 187.836 26.6337Z" fill="#E9D6C5"/>
																		<defs>
																			<linearGradient id="paint0_linear_0_1" x1="216" y1="56" x2="185.896" y2="-38.2192" gradientUnits="userSpaceOnUse">
																				<stop stop-color="#EF7F1A"/>
																				<stop offset="1" stop-color="#FFA439"/>
																			</linearGradient>
																		</defs>
																	</svg>
																	<div class="absolute top-1/2 right-[-15px] -translate-y-1/2 text-dark-3 font-consolas text-[14px] z-20">
																		{if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}{mb_substr($coupon->getCode(), -3, 3)}
																		{else}
																			{$coupon->getCode()}
																		{/if}
																	</div>
																</div>
															</div>
															<div class="absolute z-10 inset-0 rounded-[10px] border border-dashed border-dark-4 group-hover:border-primary-orange group-hover:border-solid group-hover:z-20"></div>
															<span class="text-sm lg:text-base absolute inset-0 flex items-center justify-center z-30 mr-8 lg:mr-0 font-bold text-primary-orange">
																{_'newFront.deals.deal.getCode'}
															</span>
														</a>
													{else}
														<a n:href="//:NewFront:Shops:Redirection:deal $coupon" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="group/get-cupon-button relative inline-grid h-14 cursor-pointer place-items-center rounded-[0.625rem] px-4 w-full">
															<span class="absolute inset-0 z-20 grid size-full cursor-pointer items-center rounded-[inherit] border border-dashed border-dark-4 px-3.5 text-sm text-shuttle-gray-500">
																<span class="inline-block truncate text-right w-[87%]">
																	{if $coupon->getCode() !== null && Nette\Utils\Strings::length($coupon->getCode()) > 3}
																		{mb_substr($coupon->getCode(), -3, 3)}
																	{else}
																		{$coupon->getCode()}
																	{/if}
																</span>
															</span>
															<span class="absolute inset-0 z-20 grid size-full cursor-pointer place-items-center rounded-[inherit] text-sm font-bold transition-colors [clip-path:polygon(0_0,calc(83.5%-12px)_0,83.5%_100%,0_100%)] sm:text-base border border-linen-200 bg-linen-50 text-tango-500 group-hover/get-cupon-button:bg-linen-100">{_'newFront.deals.deal.getCode'}</span>
															<svg width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute right-[16.5%] z-30 fill-linen-250" aria-hidden="true"><path d="M1.83628 26.6337L18 0L30 56L4.00024 37.2761C0.612751 34.8365 -0.329528 30.2024 1.83628 26.6337Z"></path></svg>
														</a>
													{/if}


                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="right-older-gradient lg:block hidden pointer-events-none absolute top-0 right-[-50px] h-full w-[153px] z-30"></div>

                                <div style="background: linear-gradient(451deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="left-older-gradient lg:block hidden pointer-events-none absolute top-0 left-[-69px] h-full w-[153px] z-30"></div>

                                <div class="swiper-pagination swiper-older-coupons-pagination absolute -bottom-6 left-1/2 -translate-x-1/2 z-20"></div>
                            </div>

                            <div class="swiper-button-prev swiper-older-coupons-prev" style="top: 213px; left: -41px;">
                                <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                            </div>
                            <div class="swiper-button-next swiper-older-coupons-next z-40" style="top: 213px; right: -41px;">
                                <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="px-5 md:px-0 lg:hidden">
                    <div class="text-xs text-dark-2 leading-[21px] mt-10 mb-[30px]">
                        {_newFront.shop.bluescreen.text, ['shop' => $shop->getName(), 'reward' => ($shop |reward:false,'common')] |noescape}
                    </div>

                    <div class="mb-[50px]">
                        <div class="bg-white rounded-2xl p-[2px] pb-5 relative z-20" style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);">
                            <div class="relative w-full h-[180px] rounded-2xl z-20">
                                <img
                                        class="w-full h-full object-cover rounded-2xl"
                                        src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=1746&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                        alt=""
                                />
                                <div class="absolute inset-0 z-10" style="background: linear-gradient(82deg, rgba(0, 0, 0, 0.70) 38.89%, rgba(24, 43, 74, 0.70) 86.86%); backdrop-filter: blur(2.65px);border-radius: 1rem;"></div>
                                <div class="m-auto w-full max-w-[164px] absolute inset-0 flex items-center justify-center z-20 text-white text-center">
                                    <div>
                                        {_newFront.shops.shop.shop.ctaBox.getReward}

                                            {if $isUserLoggedIn && $userIdentity->isActiveUser()}
                                                        {cache 'new-shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
                                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                                            {$shop |reward:true,'common' |noescape}
                                                        {/cache}
                                                    {else}
                                                        {cache 'new-shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
                                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                                            {$shop |reward:true,'common' |noescape}
                                                        {/cache}
                                                    {/if}

                                            {_newFront.shops.shop.shop.ctaBox.fromPurchase}
                                    </div>
                                </div>
                                <div class="flex absolute left-1/2 z-30 shadow-sm bottom-0 transform -translate-x-1/2 translate-y-1/2 bg-white rounded-lg justify-center items-center w-[115px] h-[53px]">
                                    {include logo}
                                </div>
                            </div>
                            <div class="m-auto mt-10 text-sm text-dark-1 leading-[22px] text-center w-full max-w-[196px] mb-5">
                                {_newFront.shop.bluescreen.guest.promoMobile |noescape}
                            </div>
                            <div class="mx-5">
                                {include ctaLink}
                            </div>
                        </div>
                        <div class="bg-[#FAFAFB] -mt-[67px] pt-20 px-5 pb-2 rounded-b-2xl relative z-10 mb-[50px]">
                            <div class="divide-y divide-light-6">
                                {var $offers = $shopOffers()}

                                {foreach $offers as $offer}
                                    <div class="flex justify-between items-center text-dark-2 text-xs leading-[21px] py-2">
                                        <div>{$offer->getName()}</div>
                                        <div class="text-secondary-green font-bold [&_span]:text-xs">{$offer |reward:true |noescape}</div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>

                {cache 'new-mobile-shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $similarShops = $recommendedShops()}
                    <div n:if="$similarShops" class="lg:hidden">
                    <div>
                        <div class="pl-5 text-lg text-dark-1 leading-[31.5px] font-bold w-full max-w-[251px]">
                            {_newFront.shop.bluescreen.similarShops.title, ['countOfCashbackShops' => $countOfCashbackShops]}
                        </div>

                        <div
                                class="relative overflow-hidden w-full bg-transparent group py-5 flex flex-col gap-4"
                        >
                            <div class="flex gap-4 w-full relative -left-12">
                                {var $chunks = array_chunk($similarShops, ceil(count($similarShops) / 3))}

                                {var $chunk1 = $chunks[0] ?? []}
                                {var $chunk2 = $chunks[1] ?? []}
                                {var $chunk3 = $chunks[2] ?? []}

                                {foreach $chunk1 as $shopItem}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="{$shopItem->getName()}" src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]">
                                                    {$shopItem |reward:false,'complete' |noescape}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                            <div
                                    class="flex gap-4 w-full relative -left-28"
                            >
                                {foreach $chunk2 as $shopItem}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="{$shopItem->getName()}" src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]">
                                                    {$shopItem |reward:false,'complete' |noescape}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                            <div
                                    class="flex gap-4 w-full relative -left-40"
                            >
                                {foreach $chunk3 as $shopItem}
                                    <div style="box-shadow: 13px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="lg:hidden left-5 bg-white min-w-[187px] h-[53px] rounded-lg">
                                        <div class="flex items-center justify-center h-full w-full">
                                            <div class="flex items-center ml-auto">
                                                <img class="max-w-[68px] max-h-[30px]" alt="{$shopItem->getName()}" src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}">
                                            </div>

                                            <div class="flex items-center ml-auto">
                                                <div class="h-[41px] w-px bg-light-4"></div>
                                                <div class="text-primary-orange text-xs leading-[21px] p-[14px]">
                                                    {$shopItem |reward:false,'complete' |noescape}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>

                        <div class="text-center mt-2.5 mb-[50px]">
                            <a n:href=":NewFront:Shops:Shops:default" class="text-sm text-dark-1 underline leading-[24.5px] font-medium">
                                {_newFront.shop.bluescreen.similarShops.link}
                            </a>
                        </div>
                    </div>

                    <div class="w-full mb-10" n:if="$user->isLoggedIn() === false">
                        <div class="bg-primary-blue-dark"
                        >
                            <div class="px-5 pt-[50px] pb-10">
                                <div class="text-white text-lg leading-[31.5px] font-bold w-full max-w-[304px] mb-5">
                                    {_newFront.shop.bluescreen.guest.title |noescape}
                                    <span class="font-normal">{_newFront.shop.bluescreen.guest.title2}</span>
                                </div>
                                <div class="flex flex-col gap-[15px] mb-[30px]">
                                    <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-[2px] hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                        <img src="/new-design/money-bag.png" alt="money bag">
                                        <div class="text-sm">
                                            <div class="text-secondary-green font-bold">{_newFront.shop.bluescreen.guest.bonus.campaign.title}</div>
                                            <div class="text-dark-1">{_newFront.shop.bluescreen.guest.bonus.campaign.text}</div>
                                        </div>
                                    </div>
                                    <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-[2px] hover:cursor-pointer hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200">
                                        <img src="/new-design/champion.png" alt="bonus">
                                        <div class="text-sm">
                                            <div class="text-secondary-green font-bold">{_newFront.shop.bluescreen.guest.bonus.campaign.title}</div>
                                            <div class="text-dark-1">{_newFront.shop.bluescreen.guest.bonus.campaign.text}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-[30px]">
                                    <a href="#" class="text-sm text-white underline leading-[24.5px] font-medium">Načítať ďalšie bonusy <span class="font-normal">(6)</span></a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="px-5 md:px-0 lg:px-5 w-full">
                        <div class="flex justify-center gap-[57px] md:gap-[81px] mb-[60px] md:mb-[130px] flex-wrap">
                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">{_newFront.homepage.review.apple.ratingValue}</span> ({_newFront.homepage.review.apple.count})
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="108" height="26" viewBox="0 0 108 26" fill="none">
                                        <path d="M17.0515 14.2646C17.025 11.1005 19.6462 9.5582 19.7682 9.48929C18.2825 7.32689 15.9796 7.03009 15.1678 7.00889C13.231 6.80752 11.3579 8.16431 10.371 8.16431C9.36808 8.16431 7.85047 7.03009 6.21089 7.06191C4.1043 7.09369 2.1357 8.31271 1.05855 10.1995C-1.17007 14.0526 0.490798 19.7129 2.6292 22.8293C3.69571 24.3557 4.94271 26.0571 6.57703 25.9987C8.17417 25.9351 8.77378 24.9811 10.6999 24.9811C12.6102 24.9811 13.1726 25.9987 14.8388 25.9616C16.5527 25.9351 17.6352 24.4299 18.6645 22.8929C19.9009 21.144 20.3944 19.4215 20.4156 19.3367C20.3732 19.3101 17.0833 18.0541 17.0515 14.2646ZM13.9049 4.9578C14.7645 3.88191 15.3535 2.42443 15.189 0.94043C13.9473 0.993401 12.3873 1.799 11.4958 2.84843C10.7053 3.77591 9.99419 5.29169 10.18 6.7174C11.5702 6.81809 13.0081 6.01249 13.9049 4.9578Z" fill="#BDC2CC"></path>
                                        <path d="M40.197 21.4867H37.9631L36.7374 17.6495H32.4871L31.3197 21.4867H29.1442L33.3573 8.41696H35.9573L40.197 21.4867ZM36.3712 16.0383L35.2622 12.6252C35.1455 12.2754 34.9226 11.4539 34.6042 10.1607H34.5671C34.4397 10.7172 34.2275 11.5387 33.9463 12.6252L32.8585 16.0383H36.3712ZM51.0113 16.6584C51.0113 18.2591 50.576 19.5258 49.7058 20.4586C48.9254 21.2854 47.9546 21.6987 46.7978 21.6987C45.5455 21.6987 44.649 21.2535 44.1026 20.3631V25.308H42.0065V15.1586C42.0065 14.1516 41.9801 13.118 41.9269 12.0634H43.7731L43.8904 13.558H43.927C44.6278 12.4344 45.6891 11.8672 47.1107 11.8672C48.2252 11.8672 49.1537 12.3072 49.8969 13.187C50.6395 14.0668 51.0113 15.2275 51.0113 16.6584ZM48.878 16.7379C48.878 15.8211 48.6709 15.0632 48.2572 14.4696C47.8059 13.8495 47.196 13.542 46.4317 13.542C45.9173 13.542 45.4448 13.717 45.0254 14.0562C44.6067 14.4007 44.3303 14.8512 44.2033 15.4023C44.145 15.6091 44.1129 15.8263 44.1078 16.0383V17.6124C44.1078 18.2962 44.32 18.8738 44.7394 19.3508C45.1582 19.8226 45.7051 20.061 46.3785 20.061C47.1691 20.061 47.7847 19.7536 48.2252 19.1495C48.6548 18.5347 48.878 17.7343 48.878 16.7379ZM61.8675 16.6584C61.8675 18.2591 61.4327 19.5258 60.562 20.4586C59.7822 21.2854 58.8113 21.6987 57.6546 21.6987C56.4022 21.6987 55.5052 21.2535 54.9588 20.3631V25.308H52.8626V15.1586C52.8626 14.1516 52.8363 13.118 52.7831 12.0634H54.6299L54.7466 13.558H54.7838C55.484 12.4344 56.5453 11.8672 57.9675 11.8672C59.082 11.8672 60.0105 12.3072 60.7531 13.187C61.4911 14.0668 61.8675 15.2275 61.8675 16.6584ZM59.729 16.7379C59.729 15.8211 59.5225 15.0632 59.1083 14.4696C58.6574 13.8495 58.047 13.542 57.2833 13.542C56.7684 13.542 56.2958 13.717 55.8713 14.0562C55.4525 14.4007 55.1762 14.8512 55.0492 15.4023C54.9851 15.662 54.9485 15.874 54.9485 16.0383V17.6124C54.9485 18.2962 55.1608 18.8738 55.5796 19.3508C55.9989 19.8226 56.5453 20.061 57.2249 20.061C58.015 20.061 58.6306 19.7536 59.0711 19.1495C59.5116 18.5347 59.729 17.7343 59.729 16.7379ZM74.0029 17.8244C74.0029 18.9375 73.6156 19.8438 72.841 20.538C71.992 21.3012 70.798 21.6828 69.2751 21.6828C67.8689 21.6828 66.739 21.4126 65.8842 20.8666L66.3671 19.123C67.2853 19.6688 68.2985 19.9391 69.3969 19.9391C70.1876 19.9391 70.8031 19.759 71.2437 19.4038C71.6842 19.0487 71.9067 18.5718 71.9067 17.9782C71.9067 17.4482 71.7214 17.003 71.3604 16.6372C70.9994 16.2768 70.3998 15.9376 69.5565 15.6303C67.2641 14.777 66.1177 13.5262 66.1177 11.8885C66.1177 10.8179 66.521 9.93811 67.3328 9.25439C68.1395 8.57068 69.2116 8.22622 70.5486 8.22622C71.7374 8.22622 72.7294 8.43291 73.5201 8.84628L72.9949 10.5529C72.2517 10.1501 71.4187 9.95399 70.4793 9.95399C69.7419 9.95399 69.1584 10.1342 68.7442 10.4946C68.394 10.8179 68.219 11.2101 68.219 11.6765C68.219 12.1906 68.4209 12.6199 68.8242 12.9538C69.1744 13.2612 69.8054 13.6004 70.7236 13.9608C71.8484 14.4166 72.6762 14.9412 73.2071 15.5508C73.7375 16.1603 74.0029 16.9182 74.0029 17.8244ZM80.9539 13.6375H78.6404V18.2114C78.6404 19.3774 79.0488 19.955 79.8664 19.955C80.2428 19.955 80.5506 19.9233 80.8 19.8596L80.8584 21.4496C80.4448 21.6034 79.9036 21.6828 79.2296 21.6828C78.4018 21.6828 77.7542 21.4284 77.2873 20.925C76.8205 20.4214 76.5871 19.5734 76.5871 18.3863V13.6375H75.2129V12.0687H76.5871V10.3409L78.6461 9.72079V12.0687H80.9591L80.9539 13.6375ZM91.3593 16.7008C91.3593 18.1478 90.9457 19.3403 90.1178 20.2678C89.2528 21.227 88.1018 21.704 86.6635 21.704C85.2784 21.704 84.1749 21.243 83.3522 20.3314C82.5301 19.4144 82.1216 18.2538 82.1216 16.8599C82.1216 15.3971 82.5461 14.2046 83.3951 13.2718C84.2441 12.339 85.3848 11.8779 86.8225 11.8779C88.2076 11.8779 89.3221 12.339 90.1602 13.2559C90.956 14.141 91.3593 15.2911 91.3593 16.7008ZM89.1842 16.7486C89.1842 15.8847 88.9982 15.1427 88.6212 14.5279C88.1813 13.7806 87.5548 13.4043 86.7379 13.4043C85.8992 13.4043 85.2521 13.7806 84.8168 14.5279C84.4403 15.1479 84.2544 15.9006 84.2544 16.791C84.2544 17.6548 84.4403 18.3968 84.8168 19.017C85.2681 19.7642 85.9049 20.1406 86.7219 20.1406C87.5234 20.1406 88.1492 19.759 88.6058 19.0011C88.9931 18.3598 89.1842 17.6124 89.1842 16.7486ZM98.1782 13.9078C97.9602 13.8707 97.7376 13.8495 97.5145 13.8495C96.7771 13.8495 96.2096 14.1251 95.8062 14.6816C95.4561 15.1744 95.2811 15.7946 95.2811 16.5419V21.4867H93.1849V15.0314C93.1849 14.0403 93.1689 13.0544 93.1265 12.0634H94.9521L95.0259 13.8654H95.0842C95.3074 13.2452 95.6523 12.7471 96.1352 12.3708C96.57 12.0422 97.106 11.8672 97.6524 11.8672C97.8492 11.8672 98.0186 11.8832 98.1782 11.9044V13.9078ZM107.554 16.3299C107.559 16.6479 107.533 16.9659 107.48 17.2786H101.187C101.208 18.2114 101.516 18.9215 102.099 19.4144C102.63 19.8543 103.314 20.0716 104.158 20.0716C105.092 20.0716 105.941 19.9233 106.705 19.6264L107.034 21.0786C106.142 21.4655 105.087 21.6616 103.872 21.6616C102.407 21.6616 101.261 21.2323 100.423 20.3738C99.5895 19.5151 99.1702 18.3598 99.1702 16.9128C99.1702 15.4924 99.5575 14.3106 100.338 13.3672C101.15 12.3602 102.253 11.8567 103.638 11.8567C104.996 11.8567 106.026 12.3602 106.726 13.3672C107.273 14.1675 107.554 15.1532 107.554 16.3299ZM105.554 15.7892C105.57 15.1692 105.432 14.6339 105.145 14.178C104.784 13.5951 104.222 13.3036 103.474 13.3036C102.789 13.3036 102.232 13.5898 101.802 14.1568C101.452 14.6074 101.245 15.1532 101.181 15.784L105.554 15.7892Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">{_newFront.homepage.review.facebook.ratingValue}</span> ({_newFront.homepage.review.facebook.count})
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="105" height="26" viewBox="0 0 105 18" fill="none">
                                        <path d="M92.356 17.7011H95.8498V12.2781L100.725 17.7011H105L99.3244 11.4512L104.173 6.03195H100.283L95.8498 11.0267V0L92.356 0.439333V17.7011ZM84.2986 5.73304C80.4199 5.73304 77.7265 8.23589 77.7265 11.8647C77.7265 15.4935 80.4199 17.9964 84.2986 17.9964C88.1772 17.9964 90.8707 15.4935 90.8707 11.8647C90.8707 8.23589 88.1772 5.73304 84.2986 5.73304ZM84.2986 15.1391C82.5016 15.1391 81.2704 13.8064 81.2704 11.8647C81.2704 9.92295 82.5016 8.59029 84.2986 8.59029C86.0955 8.59029 87.3268 9.92295 87.3268 11.8647C87.3268 13.8064 86.0955 15.1391 84.2986 15.1391ZM70.1 5.73304C66.2214 5.73304 63.5279 8.23589 63.5279 11.8647C63.5279 15.4935 66.2214 17.9964 70.1 17.9964C73.9787 17.9964 76.6721 15.4935 76.6721 11.8647C76.6721 8.23589 73.9787 5.73304 70.1 5.73304ZM70.1 15.1391C68.3031 15.1391 67.0718 13.8064 67.0718 11.8647C67.0718 9.92295 68.3031 8.59029 70.1 8.59029C71.897 8.59029 73.1282 9.92295 73.1282 11.8647C73.1282 13.8064 71.897 15.1391 70.1 15.1391ZM56.7287 5.73304C55.1127 5.73304 53.7736 6.34212 52.9348 7.45694V0L49.4371 0.43558V17.6973H52.5385L52.6 15.8959C53.4273 17.2396 54.9087 17.9964 56.7287 17.9964C60.0456 17.9964 62.4736 15.4122 62.4736 11.8647C62.4736 8.31707 60.0571 5.73304 56.7287 5.73304ZM55.9015 15.1391C54.1045 15.1391 52.8733 13.8064 52.8733 11.8647C52.8733 9.92295 54.1045 8.59029 55.9015 8.59029C57.6984 8.59029 58.9297 9.92295 58.9297 11.8647C58.9297 13.8064 57.6984 15.1391 55.9015 15.1391ZM42.434 15.2904C40.5794 15.2904 39.248 14.4635 38.794 13.0386H47.4015C47.4863 12.6031 47.544 12.0862 47.544 11.7392C47.544 7.98484 45.3545 5.73304 41.676 5.73304C37.9629 5.73304 35.3924 8.23589 35.3924 11.8647C35.3924 15.5415 38.086 17.9964 42.0839 17.9964C44.1539 17.9964 46.1664 17.3319 47.4246 16.2281L46.1664 14.1018C44.9467 14.9102 43.7499 15.2904 42.434 15.2904ZM41.6529 8.33924C43.269 8.33924 44.3002 9.28055 44.3002 10.7387V10.7499H38.7209C39.0557 9.20312 40.0983 8.33924 41.6529 8.33924ZM29.9208 18C31.7293 18 33.4993 17.3355 34.7459 16.1838L33.38 13.98C32.4103 14.7035 31.3099 15.0948 30.2671 15.0948C28.3278 15.0948 27.0349 13.7953 27.0349 11.8683C27.0349 9.94137 28.3278 8.64191 30.2671 8.64191C31.2252 8.64191 32.268 8.97413 33.1145 9.56116L34.5036 7.30924C33.38 6.33473 31.6408 5.73667 29.9169 5.73667C26.123 5.73667 23.441 8.27648 23.441 11.8683C23.4448 15.4492 26.1268 18 29.9208 18ZM18.8121 6.03195L18.7506 7.84829C17.9232 6.49345 16.4418 5.73667 14.6218 5.73667C11.2934 5.73667 8.87694 8.32082 8.87694 11.8683C8.87694 15.416 11.3088 18 14.6218 18C16.4418 18 17.927 17.2432 18.7506 15.8995L18.8121 17.701H21.9134V6.03195H18.8121ZM15.449 15.1391C13.6522 15.1391 12.4208 13.8064 12.4208 11.8647C12.4208 9.92295 13.6522 8.59029 15.449 8.59029C17.246 8.59029 18.4773 9.92295 18.4773 11.8647C18.4773 13.8064 17.2421 15.1391 15.449 15.1391ZM8.61526 6.05389H5.4716V5.0571C5.4716 3.56571 6.07957 2.99358 7.65721 2.99358C8.14583 2.99358 8.54214 3.00461 8.76921 3.02678V0.468553C8.33818 0.354058 7.2878 0.239677 6.67983 0.239677C3.46684 0.239677 1.98544 1.69411 1.98544 4.83561V6.05014H0V8.87055H1.98544V17.6971H5.47539V8.87419H8.07662L8.61526 6.05389Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex flex-col items-center">
                                <div class="mb-2">
                                    <img src="/new-design/green-stars-narrower.svg" alt="green-stars" loading="lazy">
                                </div>
                                <div class="mb-[18px] leading-[24.5px] text-dark-1 text-xs md:text-sm">
                                    <span class="font-medium text-sm">{_newFront.homepage.review.google.ratingValue}</span> ({_newFront.homepage.review.facebook.count})
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="113" height="25" viewBox="0 0 113 25" fill="none">
                                        <path d="M57.0823 10.6854C54.7526 10.6854 52.9082 12.4432 52.9082 14.8846C52.9082 17.2283 54.7526 19.0837 57.0823 19.0837C59.412 19.0837 61.2569 17.3259 61.2569 14.8846C61.2569 12.4432 59.412 10.6854 57.0823 10.6854ZM57.0823 17.3259C55.8202 17.3259 54.7526 16.2517 54.7526 14.7869C54.7526 13.3221 55.8202 12.2479 57.0823 12.2479C58.3444 12.2479 59.412 13.2245 59.412 14.7869C59.412 16.3494 58.3444 17.3259 57.0823 17.3259ZM48.0537 10.6854C45.724 10.6854 43.8796 12.4432 43.8796 14.8846C43.8796 17.2283 45.724 19.0837 48.0537 19.0837C50.384 19.0837 52.2284 17.3259 52.2284 14.8846C52.2284 12.4432 50.384 10.6854 48.0537 10.6854ZM48.0537 17.3259C46.7922 17.3259 45.724 16.2517 45.724 14.7869C45.724 13.3221 46.7922 12.2479 48.0537 12.2479C49.3158 12.2479 50.384 13.2245 50.384 14.7869C50.384 16.3494 49.3158 17.3259 48.0537 17.3259ZM37.2782 11.9549V13.7127H41.4525C41.3555 14.6893 40.9672 15.4705 40.4818 15.9588C39.8993 16.5447 38.9285 17.2283 37.2782 17.2283C34.657 17.2283 32.7154 15.1775 32.7154 12.5409C32.7154 9.90425 34.7541 7.8535 37.2782 7.8535C38.6373 7.8535 39.7051 8.4394 40.4818 9.123L41.7438 7.8535C40.6759 6.87697 39.3168 6.09573 37.3752 6.09573C33.8804 6.09573 30.8709 9.02536 30.8709 12.5409C30.8709 16.0564 33.8804 18.986 37.3752 18.986C39.3168 18.986 40.6759 18.4001 41.8409 17.1306C43.0058 15.9588 43.3941 14.2986 43.3941 13.0292C43.3941 12.6385 43.3941 12.2479 43.297 11.9549H37.2782ZM81.3524 13.3221C80.964 12.3456 79.9928 10.6854 77.8575 10.6854C75.7217 10.6854 73.9743 12.3456 73.9743 14.8846C73.9743 17.2283 75.7217 19.0837 78.0514 19.0837C79.8958 19.0837 81.061 17.9119 81.4493 17.2283L80.0903 16.2517C79.6049 16.9353 79.0221 17.4235 78.0514 17.4235C77.0808 17.4235 76.4979 17.033 76.0126 16.1541L81.5463 13.8104L81.3524 13.3221ZM75.7217 14.6893C75.7217 13.1268 76.9838 12.2479 77.8575 12.2479C78.5368 12.2479 79.2166 12.6385 79.4105 13.1268L75.7217 14.6893ZM71.1587 18.6931H73.003V6.48635H71.1587V18.6931ZM68.2467 11.5643C67.7608 11.0761 66.9846 10.5878 66.0133 10.5878C63.975 10.5878 62.0331 12.4432 62.0331 14.7869C62.0331 17.1306 63.8781 18.8884 66.0133 18.8884C66.9846 18.8884 67.7608 18.4001 68.1491 17.9119H68.2467V18.4978C68.2467 20.0602 67.3729 20.9391 66.0133 20.9391C64.9457 20.9391 64.1689 20.1579 63.975 19.4743L62.4215 20.1579C62.9069 21.2321 64.072 22.5992 66.1109 22.5992C68.2467 22.5992 69.9941 21.3297 69.9941 18.3025V10.8808H68.2467V11.5643ZM66.1109 17.3259C64.8488 17.3259 63.7806 16.2517 63.7806 14.7869C63.7806 13.3221 64.8488 12.2479 66.1109 12.2479C67.3729 12.2479 68.3436 13.3221 68.3436 14.7869C68.3436 16.2517 67.3729 17.3259 66.1109 17.3259ZM89.7981 6.48635H85.4295V18.6931H87.2739V14.1033H89.7981C91.8364 14.1033 93.7783 12.6385 93.7783 10.2949C93.7783 7.95114 91.8364 6.48635 89.7981 6.48635ZM89.8951 12.4432H87.2739V8.24412H89.8951C91.2541 8.24412 92.0309 9.41599 92.0309 10.2949C91.9339 11.2714 91.1571 12.4432 89.8951 12.4432ZM101.059 10.6854C99.6998 10.6854 98.3408 11.2714 97.8554 12.5409L99.5059 13.2245C99.8943 12.5409 100.477 12.3456 101.156 12.3456C102.127 12.3456 103.001 12.9315 103.098 13.9081V14.0057C102.807 13.8104 102.03 13.5174 101.253 13.5174C99.5059 13.5174 97.7585 14.494 97.7585 16.2517C97.7585 17.9119 99.2145 18.986 100.768 18.986C102.03 18.986 102.612 18.4001 103.098 17.8142H103.195V18.7908H104.942V14.1033C104.748 11.8573 103.098 10.6854 101.059 10.6854ZM100.865 17.3259C100.283 17.3259 99.4089 17.033 99.4089 16.2517C99.4089 15.2752 100.477 14.9822 101.35 14.9822C102.127 14.9822 102.515 15.1776 103.001 15.3728C102.807 16.5447 101.836 17.3259 100.865 17.3259ZM111.058 10.8808L109.02 16.1541H108.923L106.787 10.8808H104.845L108.049 18.3025L106.204 22.4039H108.049L113 10.8808H111.058ZM94.7489 18.6931H96.5933V6.48635H94.7489V18.6931Z" fill="#BDC2CC"></path>
                                        <path d="M0.582458 25C0.194134 24.8047 0 24.3165 0 23.8282C0 23.7305 0 23.5352 0 23.4376C0 16.2112 0 8.98477 0 1.85604C0 1.46542 0.0970671 1.17249 0.194134 0.781871C0.291257 0.586531 0.485391 0.391248 0.679581 0.195965C4.85396 4.39506 8.93128 8.59415 13.0086 12.7933C8.83421 16.6994 4.7569 20.8009 0.582458 25Z" fill="#BDC2CC"></path>
                                        <path d="M17.0856 8.49588C15.9206 9.66775 14.6586 10.8396 13.4937 12.1091C9.61049 8.10526 5.63024 4.10145 1.74706 0.0976415C1.74706 0.0976415 1.74706 0 1.84412 0C6.89227 2.83194 11.9404 5.66394 17.0856 8.49588Z" fill="#BDC2CC"></path>
                                        <path d="M1.74706 24.9995C5.63024 21.0933 9.61049 17.1872 13.4937 13.281C14.5615 14.3552 15.7265 15.4294 16.9885 16.6013C11.9404 19.4333 6.89227 22.2652 1.84412 24.9995H1.74706Z" fill="#BDC2CC"></path>
                                        <path d="M17.6685 16.2107C16.4064 15.0389 15.2415 13.867 13.9795 12.6952C15.2415 11.4257 16.5036 10.1562 17.7655 8.88669C18.1539 9.08198 18.5422 9.27732 18.9305 9.57024C19.9013 10.1562 20.9692 10.6445 21.94 11.2304C22.2312 11.328 22.4254 11.5234 22.6195 11.8163C23.0078 12.3045 23.0078 12.7929 22.6195 13.2811C22.4254 13.4764 22.2312 13.6717 21.94 13.7694C20.5809 14.6483 19.1247 15.4295 17.6685 16.2107Z" fill="#BDC2CC"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="w-full h-px bg-light-4 mt-[50px] mb-10"></div>

                        <div class="text-lg font-bold leading-[31.5px] text-dark-1 mb-5">
                            {_newFront.shop.bluescreen.guest.signMobile}
                        </div>

                        <button class="open-register-popup text-sm lg:text-base w-full bg-orange-gradient py-4 rounded-[10px] text-center text-white font-bold leading-[24.5px]">
                            {_newFront.shop.bluescreen.guest.ctaMobile} 💰
                        </button>

                        <div class="w-full h-px bg-light-4 my-[50px]"></div>
                    </div>
                </div>
                {/cache}
            </div>
        </div>
    </div>


    <div n:if="$shop->isCashbackAllowed()" id="cashback" class="hidden lg:block w-full min-h-[1277px] h-auto bg-repeat"
         style="background-image:url('{$basePath}/new-design/bg-profile-shop-blue.svg'); background-position: 0">
        <div class="container pt-[110px]">
            <div class="flex gap-[30px] main-section-shop-detail-section-2">
                <div class="sidebar-shop-detail-section-2 h-max w-full max-w-[335px] shrink-0">
                    {cache 'new-shopSimilarShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                        {var $similarShops = $recommendedShops()}
                        <div class="text-white border border-white/15 rounded-2xl p-5 mb-[30px]" n:if="$similarShops">
                            <div class="leading-7 font-bold mb-5">
                                {_newFront.shop.bluescreen.similarShops.title, ['countOfCashbackShops' => $countOfCashbackShops]}
                            </div>

                            <div class="space-y-[14px] mb-5">
                                {foreach $similarShops as $shopItem}
                                    <a href="{plink Shop:default, $shopItem}" title="{$shopItem->getName()}" class="flex items-center gap-5 bg-white/5 p-[2px] rounded-lg hover:cursor-pointer hover:bg-white/10 hover:shadow-md hover:scale-[1.01] transition-all duration-200">
                                        <div>
                                            <div class="bg-white min-w-[113px] h-[49px] rounded-lg flex items-center justify-center h-[47px]">
                                                <img class="max-w-[70px] max-h-[27px]" alt="{$shopItem->getName()}" src="{$shopItem->getCurrentLogo() |image:116,0,'fit',false,$shopItem->getName()}" loading="lazy">
											</div>
										</div>
                                        <div class="small-shop-reward text-sm leading-[24.5px]">{$shopItem |reward:true,'extended'|noescape}</div>
                                    </a>
                                {/foreach}
                            </div>

                            <div class="text-center">
                                <a n:href="Shops:default" class="text-sm leading-[24.5px] underline font-medium hover:no-underline">
                                    {_newFront.shop.bluescreen.similarShops.link}
                                </a>
                            </div>
                        </div>
                    {/cache}

                    <div class="text-white border border-white/15 rounded-2xl p-5 mb-[30px]">
                        <div class="leading-7 font-bold mb-5">{_newFront.shop.bluescreen.rating.title}</div>

                        <div class="space-y-5">
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-app-store.svg" alt="app store">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.apple.ratingValue}</span> {_newFront.homepage.review.apple.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.apple.count}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-facebook.svg" alt="facebook">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.facebook.rating}</span> {_newFront.homepage.review.facebook.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.facebook.count}</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-5">
                                <img src="{$basePath}/new-design/reviews-google-play.svg" alt="google play">
                                <div>
                                    <div class="mb-[3px] text-sm"><span class="text-base font-bold">{_newFront.homepage.review.google.ratingValue}</span> {_newFront.homepage.review.google.from}</div>
                                    <img class="mb-2" src="{$basePath}/new-design/five-stars.svg" alt="review">
                                    <div class="text-xs opacity-70">{_newFront.homepage.review.google.count}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="w-full min-w-0 section-2-length">
                    <div class="rounded-[20px] mb-[60px]" style="background: linear-gradient(180deg, rgba(255, 255, 255,0.08) 0%,rgba(24,43, 74, 0.00) 100%)">
                        <div class="flex items-center pl-[50px] gap-[51px] pt-[39px] mb-[55px]">
                            <img class="w-[117px] h-[191px]" src="{$basePath}/new-design/donkey-profile-blue.png" alt="donkey">
                            <div class="pt-[19px]">
                                <div class="flex items-center gap-6">
                                    <div class="text-[26px] text-white w-full max-w-[297px] leading-[39px] mb-2">
                                        <span class="font-bold">{_newFront.shop.bluescreen.promo1} </span> {_newFront.shop.bluescreen.promo2}
                                    </div>
                                    <div>
                                        <img src="{$basePath}/new-design/flying-money-profile.svg" alt="icons">
                                    </div>
                                </div>

                                <div class="text-[#B9BFC8] text-sm leading-[24.5px] mb-2 [&_span]:text-white">
                                    {_newFront.shop.bluescreen.text, ['shop' => $shop->getName(), 'reward' => ($shop |reward:false,'common')] |noescape}
                                </div>

                                <a href="/" class="text-primary-orange underline font-medium text-sm leading-[24.5px] hover:no-underline">
                                    {_newFront.shop.bluescreen.howItWorks}
                                </a>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="bg-white ml-[30px] pb-5 rounded-2xl relative z-10 w-full max-w-[335px]">
                                <div class="relative mb-16">
									{if $shop->getMobileCover()}
										<img class="p-[2px] lg:rounded-2xl object-cover max-h-[130px] lg:max-h-[200px] w-full" src="{$shop->getMobileCover() |image}" alt="">
									{else}
										<span class="block bg-white lg:rounded-2xl object-cover h-[130px] lg:h-[200px] w-full"><span>
									{/if}

									<div class="absolute left-1/2 shadow-sm bottom-0 transform -translate-x-1/2 translate-y-1/2 bg-white rounded-2xl flex justify-center items-center w-[177px] h-[81px]">
                                        {include logo}
                                    </div>
                                </div>
                                <div class="w-full max-w-[178px] m-auto text-center text-dark-1 font-medium leading-[28px] mb-[22px]">
                                    {_newFront.shops.shop.shop.ctaBox.getReward}

                                {if $isUserLoggedIn && $userIdentity->isActiveUser()}
                                        {cache 'new-shop-logobox-header-active-' . $cashbackCacheKeyPostfix, 'expire' =>
                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                            {$shop |reward:true,'common' |noescape}
                                        {/cache}
                                    {else}
                                        {cache 'new-shop-logobox-header-inactive-' . $cashbackCacheKeyPostfix, 'expire' =>
                                        ($cashbackCacheDisabled ? '0 seconds' : '168 hours'), tags => ['shop/' . $shop->getId()]}
                                            {$shop |reward:true,'common' |noescape}
                                        {/cache}
                                    {/if}

                                {_newFront.shops.shop.shop.ctaBox.fromPurchase}
                                </div>

                                <div class="mx-5">
                                    {include ctaLink}
                                </div>
                            </div>

                            <div class="text-white bg-white/5 w-full max-w-[440px] mt-[26px] rounded-r-2xl h-max relative">
                                <div class="px-10 py-[30px]">
                                    <div class="text-xl leading-[35px] font-bold mb-3">{_newFront.shop.bluescreen.shopBox.title, ['shop' => $shop->getName()]} <span class="font-normal">({$shop |reward:false,'common'|noescape})</span></div>

                                    {var $offers = $shopOffers()}

                                    {foreach $offers as $offer}
                                        <div class="flex items-center justify-between border-b border-white/10 last:border-none py-2">
                                            <div class="text-sm leading-[24.5px]">{$offer->getName()}</div>
                                            <div class="medium-shop-reward text-secondary-green font-bold leading-7">{$offer |reward:true |noescape}</div>
                                        </div>
                                    {/foreach}
                                </div>

                                <div class="absolute top-[-56px] right-[-20px]">
                                    <div class="flex flex-col items-center justify-center text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="107" height="107" viewBox="0 0 107 107" fill="none">
                                            <path d="M48.708 2.8501C51.3303 0.429895 55.3279 0.353667 58.0361 2.62256L58.2939 2.8501L63.6562 7.79932C65.3098 9.32532 67.5424 10.0461 69.7627 9.7876L70.207 9.72217L77.3936 8.4585C80.908 7.84028 84.3116 9.93713 85.3633 13.3101L85.457 13.6401L87.293 20.7026C87.859 22.8806 89.3476 24.6943 91.3555 25.6772L91.7637 25.8628L98.4932 28.6841C101.784 30.064 103.513 33.6687 102.574 37.0747L102.475 37.4038L100.201 44.3374C99.4999 46.4755 99.7712 48.8059 100.929 50.7183L101.173 51.0952L105.308 57.1069C107.33 60.0471 106.837 64.0148 104.205 66.3726L103.943 66.5952L98.2822 71.1987C96.5362 72.6183 95.5053 74.7257 95.4453 76.9604L95.4463 77.4087L95.6748 84.7026C95.7864 88.2693 93.2256 91.3391 89.7373 91.8999L89.3975 91.9468L82.1455 92.7583C79.9091 93.0086 77.9022 94.2242 76.6436 96.0718L76.4023 96.4487L72.6514 102.708C70.8172 105.769 67.0034 106.968 63.7656 105.554L63.4541 105.409L56.9141 102.171C54.8976 101.173 52.5526 101.11 50.4951 101.984L50.0879 102.171L43.5479 105.409C40.3498 106.992 36.4934 105.939 34.5342 102.999L34.3506 102.708L30.5996 96.4487C29.443 94.5188 27.5039 93.1983 25.3008 92.8208L24.8564 92.7583L17.6045 91.9468C14.0581 91.5497 11.3838 88.5781 11.3252 85.0454L11.3271 84.7026L11.5557 77.4087C11.6262 75.1596 10.709 73.0005 9.05957 71.4917L8.71973 71.1987L3.05859 66.5952C0.289922 64.3441 -0.354287 60.3986 1.50586 57.395L1.69434 57.1069L5.8291 51.0952C7.10445 49.2412 7.50077 46.9282 6.92871 44.7671L6.80078 44.3374L4.52734 37.4038C3.4152 34.0131 5.00641 30.3457 8.19531 28.8247L8.50879 28.6841L15.2383 25.8628C17.3136 24.9926 18.8968 23.2606 19.584 21.1333L19.709 20.7026L21.5449 13.6401C22.4426 10.1867 25.7638 7.96237 29.2686 8.40674L29.6084 8.4585L36.7949 9.72217C39.0112 10.1121 41.2796 9.5114 43.0078 8.09326L43.3457 7.79932L48.708 2.8501Z" fill="#66B940" stroke="url(#paint0_linear_7837_3309)" stroke-width="0.625969"/>
                                            <defs>
                                                <linearGradient id="paint0_linear_7837_3309" x1="2.73046" y1="38.9648" x2="110.001" y2="89.8596" gradientUnits="userSpaceOnUse">
                                                    <stop stop-color="#66B940"/>
                                                    <stop offset="0.495" stop-color="white"/>
                                                    <stop offset="1" stop-color="#66B940"/>
                                                </linearGradient>
                                            </defs>
                                        </svg>
                                        <div class="absolute text-sm font-bold w-[60px] leading-tight text-center leading-[18px]">
                                            {_newFront.shop.bluescreen.guarantee}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pl-[30px] text-white">
                        <div class="text-xl font-bold leading-[35px] mb-[36px]">{_newFront.shop.bluescreen.guest.title |noescape} <span class="font-normal">{_newFront.shop.bluescreen.guest.title2}</span></div>

                        <div class="flex gap-5 mb-[30px]">
                            <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-1.5">
                                <img src="{$basePath}/new-design/money-bag.png" alt="money bag">
                                <div>
                                    <div class="text-secondary-green font-bold">{_newFront.shop.bluescreen.guest.bonus.campaign.title}</div>
                                    <div class="text-dark-1">{_newFront.shop.bluescreen.guest.bonus.campaign.text}</div>
                                </div>
                            </div>
                            <div class="bg-white leading-7 flex items-center gap-5 w-full rounded-2xl p-1.5">
                                <img src="{$basePath}/new-design/champion.png" alt="bonus">
                                <div>
                                    <div class="text-secondary-green font-bold">{_newFront.shop.bluescreen.guest.bonus.campaign.title}</div>
                                    <div class="text-dark-1">{_newFront.shop.bluescreen.guest.bonus.campaign.text}</div>
                                </div>
                            </div>
                        </div>

                        <a n:href=":NewFront:Sign:up" class="open-register-popup block bg-orange-gradient hover:bg-orange-gradient-hover hover:cursor-pointer py-[18px] rounded-[10px] text-center text-white font-bold text-lg leading-[31.5px] mb-[30px]">
                            <span class="mr-1">{_newFront.shop.bluescreen.guest.cta}</span> 🛒
                        </a>

                        <div class="text-center">
                            <a n:href=":NewFront:Shops:Redirection:shop $shop" target="_blank"><span class="underline hover:no-underline font-medium">{_newFront.shop.bluescreen.guest.linkToShop, ['shop' => $shop->getName()]}</span> {_newFront.shop.bluescreen.guest.linkToShop2}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="content" class="lg:container px-5 lg:px-0 relative lg:pt-[100px]">
        <div class="flex flex-col lg:flex-row gap-[30px] text-dark-1 main-section-shop-detail-section-3">
            <div class="sidebar-shop-detail-section-3 h-max hidden lg:block w-full max-w-[335px] shrink-0">
                {cache 'new-shopTags-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $countOfMainTags = 0}
                    {foreach $shop->getTags() as $tagItem}
                        {if $tagItem->hasMainNavigationGroupType()}
                            {var $countOfMainTags = $countOfMainTags + 1}
                            {breakIf $countOfMainTags > 1}
                        {/if}
                    {/foreach}

                    <div class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$countOfMainTags > 1">
                        <div class="leading-7 font-bold mb-5 leading-7">
                            {_newFront.shops.shop.shop.categoryTitle}
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <a n:href=":NewFront:Shops:Shops:default $tagItem" n:foreach="$shop->getTags() as $tagItem"
                                        n:if="$tagItem->hasMainNavigationGroupType()"
                                    class="text-xs text-dark-1 font-medium leading-[21px] py-[7px] px-2.5 rounded-md bg-white hover:shadow-md transition duration-200">
                                {$tagItem->getName()}
                            </a>
                        </div>
                    </div>
                {/cache}

                {cache 'new-shopForeignShops-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                    {var $foreignShops = $getForeignShops()}
                    <div class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$foreignShops">
                        <div class="leading-7 font-bold mb-5 leading-7">
                            {_newFront.shops.shop.shop.shopDetail.foreignShops, ['brand' => $shop->getName()]}
                        </div>

                        <div class="flex gap-2">
                            {foreach $foreignShops as $foreignShop}
                                <a href="{$foreignShop->getForeignShopUrl()}">
                                    <img class="rounded-full w-[30px] h-[30px]"
                                         src="{$basePath}/new-design/flags/{$foreignShop->getShop()->getLocalization()->getLocale()}.svg"
                                         loading="lazy"
                                         alt="{$foreignShop->getShop()->getName()}">
                                </a>
                            {/foreach}
                        </div>
                    </div>
                {/cache}

                {cache 'new-reviews-' . $shop->getId() . '-' . $localization->getLocale(), expire => '168 hours', tags => ['shop/' . $shop->getId()]}
                    <div id="shopReview" class="border border-light-4 p-5 rounded-2xl mb-[30px]" n:if="$reviews->isEmpty() === false">
                    <div class="leading-7 font-bold mb-5 leading-7">
                        {_newFront.shops.shop.shop.sidebarMenu.review, [shop => $shop->getName()]}
                    </div>
                    <div class="w-full h-px bg-light-4 mt-[17px] mb-5"></div>

                    <div class="space-y-5">
                        {foreach $reviews as $review}
                            <div>
                                <div class="flex items-center gap-2 mb-[3px]">
                                    <div class="text-sm text-dark-1 font-medium leading-[24.5px]">{$review->getShortUsername()}</div>
                                    <div>
                                        {include '../stars.latte', 'rate' => $review->getRate()}
                                    </div>

                                </div>
                                <div class="text-sm text-dark-2 leading-[24.5px]">{$review->getText() |truncate: 100}</div>
                            </div>
                        {/foreach}
                    </div>

                    <div class="w-full h-px bg-light-4 mt-[18px] mb-[25px]"></div>

                    <div class="flex items-center gap-2 text-sm mb-[11px]">
                        {var $averageShopReview = $getAverageShopReview()}
                        {include ../stars.latte, 'rate' => (int)$averageShopReview}
                        <span class="font-bold">{number_format($averageShopReview, 1)}</span> ({$reviews->getTotalCount()})
                    </div>

                    <a n:href=":NewFront:Account:Review:default, shopId => $shop->getId(), 'do' => 'review'" target="_blank" class="text-sm font-medium leading-[24.5px] underline hover:no-underline">Pridať novú recenziu</a>
                </div>
                {/cache}
            </div>

            <div class="w-full min-w-0 section-3-length">
            {cache 'new-otherDeals-' . $cashbackCacheKeyPostfix . '-' . $isAdmin, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
            {var $otherDeals = $getOtherDeals()}
                <div n:if="$otherDeals !== null && $otherDeals->isEmpty() === false" class="lg:pl-7 relative">
                    <div class="text-lg lg:text-xl text-[35px] font-bold mb-[5px]">
                        {_newFront.shop.dealsFromOtherShops.title, ['tag' => $navigationTag->getName()]}
                    </div>
                    <div class="lg:text-sm text-dark-2 text-xs leading-[21px] lg:leading-[24.5px] mb-2">
                        {_newFront.shop.dealsFromOtherShops.text}
                    </div>

                    {if $otherDeals && count($otherDeals)}
                        <div class="swiper swiper-profile-shop swiper-another-coupons relative mb-10 !mr-[-20px] lg:!mr-0">
                        <div class="swiper-wrapper">
                            {foreach $otherDeals as $deal}
                                <div class="swiper-slide pt-[30px] pb-[45px]">
                                    <div style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);" class="bg-white p-[2px] rounded-2xl">
                                        <div style="background: linear-gradient(142deg, #FEF3E9 2.28%, #FFF 47.52%);" class="p-[13px] rounded-[15px]">
                                            <div class="flex flex-col lg:flex-row justify-content-between w-full">
                                                <div class="flex-col w-full justify-between">
                                                    <div class="lg:hidden justify-center items-center bg-white rounded-lg w-[100px] h-[43px] mt-[-24px] mb-2.5">
                                                        <img class="max-w-[60px] max-h-[30px]" src="{$deal->getShop()->getCurrentLogo() |image:116,0,'fit',false,$deal->getShop()->getName()}" alt="{$deal->getShop()->getName()}">

                                                    </div>
                                                    <div class="flex gap-[15px] relative mb-[15px] lg:mb-0 lg:top-[1px]">
                                                        <div class="flex flex-col gap-[11px]">
                                                            <div style="box-shadow: 13px 7px 15.2px 0 rgba(0, 0, 0, 0.03);" class="bg-white py-2 pl-[14px] pr-2.5 rounded-[10px] min-w-[70px] flex-shrink-0">
                                                                <div class="text-primary-orange font-black text-[28px] leading-[54px] text-center">{$deal->getValue()}<span class="font-medium text-lg leading-[38px]">{$deal->getUnitSymbol()}</span></div>
                                                            </div>
                                                            <div class="m-auto hidden lg:block">
                                                                <img class="max-w-12 max-h-12" src="https://www.tipli.cz/upload/images/shops-shop-logo/789809.svg" alt="">
                                                            </div>
                                                        </div>
                                                        <div class="flex flex-col gap-[17px]">
                                                            <div class="text-sm leading-[22x] w-full max-w-[243px] text-dark-1 line-clamp-3 md:line-clamp-2"><span class="font-normal">{$deal |dealName:true|noescape}</span> {*$deal->getDescription() |striptags|truncate:65|noescape*}
                                                            </div>

                                                            <div class="hidden lg:flex flex-col gap-[5px] lg:gap-2.5 mb-[15px] lg:mb-0 lg:mt-[13px]">
                                                                {if $deal->getValidTillDays() === 0}
                                                                <div class="w-max text-red-400 text-xs ring-1 ring-inset ring-secondary-red/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                                    {_'newFront.deals.deal.validTillToday'}
                                                                </div>
                                                                {elseif $deal->getValidTillDays() <= 3}
                                                                <div class="w-max text-orange-500 text-xs ring-1 ring-inset ring-orange-500/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                                    {_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}
                                                                </div>
                                                                {else}
                                                                <div class="w-max text-dark-400 text-xs ring-1 ring-inset ring-dark-1/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                                    {_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}
                                                                </div>
                                                                {/if}

                                                                <div n:if="$shop->isCashbackWithCouponAllowed()" class="w-max text-xs text-secondary-green ring-1 ring-inset ring-secondary-green/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                                    {_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')] |noescape}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="lg:hidden flex flex-col gap-[5px] lg:gap-2.5 mb-[15px] lg:mb-0 lg:mt-[13px]">
                                                        {if $deal->getValidTillDays() === 0}
                                                        <div class="w-max text-red-400 text-xs ring-1 ring-inset ring-secondary-red/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {_'newFront.deals.deal.validTillToday'}
                                                        </div>
                                                        {elseif $deal->getValidTillDays() <= 3}
                                                        <div class="w-max text-orange-500 text-xs ring-1 ring-inset ring-orange-500/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}
                                                        </div>
                                                        {else}
                                                        <div class="w-max text-dark-400 text-xs ring-1 ring-inset ring-dark-1/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}
                                                        </div>
                                                        {/if}

                                                        <div n:if="$shop->isCashbackWithCouponAllowed()" class="w-max text-xs text-secondary-green ring-1 ring-inset ring-secondary-green/30 leading-[21px] font-medium bg-white/50 px-3 py-[3px] lg:py-1 rounded-full">
                                                            {_'newFront.shop.cashbackWithCouponAllowed', ['reward' => ($shop |reward:false, 'common')] |noescape}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex flex-col gap-[13px]">
                                                    <div class="relative coupon-code-copy cursor-pointer">
                                                        <div class="hidden lg:block w-[207px] h-[72px]">
                                                            <img src="{$basePath}/new-design/coupon-profile.svg" alt="kupon">
                                                        </div>
                                                        <div class="h-full w-full lg:hidden" >
                                                            <img class="w-full h-full" src="{$basePath}/new-design/coupon-border-dashed-mb.svg" alt="kupon">
                                                        </div>
                                                        <span class="font-consolas text-dark-1 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 truncate">
                                                              {$deal->getCode()}
                                                        </span>
                                                        <svg class="svg-copy-btn absolute top-1/2 right-2 -translate-x-1/2 -translate-y-1/2" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                                            <path d="M12 3.57812V1.6875C12 1.30781 11.6922 1 11.3125 1H1.6875C1.30781 1 1 1.3078 1 1.6875V11.3125C1 11.6922 1.30781 12 1.6875 12H3.57812" stroke="#80899C" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round" stroke-dasharray="3 3"/>
                                                            <path d="M6 6.6875C6 6.3078 6.30781 6 6.6875 6H16.3125C16.6922 6 17 6.30781 17 6.6875V16.3125C17 16.6922 16.6922 17 16.3125 17H6.6875C6.3078 17 6 16.6922 6 16.3125V6.6875Z" stroke="#80899C" stroke-width="1.5" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        {var $id = $deal->getId()}
                                                        {var $isDetailable = $deal->getCode() && !empty($deal->getCode())}
                                                        {capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}{/capture}

                                                        <a n:href="//:NewFront:Shops:Redirection:deal $deal" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="text-sm lg:text-base border border-primary-orange/20 hover:border-primary-orange rounded-[10px] flex justify-center items-center bg-pastel-orange-light h-[56px] leading-7 font-bold text-primary-orange">
                                                            {_'newFront.deals.deal.getCode'}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>

                        <div style="background: linear-gradient(271deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="right-another-gradient lg:block hidden pointer-events-none absolute top-0 right-[-50px] h-full w-[153px] z-30"></div>

                        <div style="background: linear-gradient(451deg, #F4F4F6 46.52%, rgba(244, 244, 246, 0.00) 99.52%);" class="left-another-gradient lg:block hidden pointer-events-none absolute top-0 left-[-60px] h-full w-[153px] z-30"></div>

                        <div class="swiper-pagination swiper-another-coupons-pagination absolute !top-[218px] -bottom-6 left-1/2 -translate-x-1/2 z-20 hidden lg:block"></div>
                    </div>
                    {/if}

                    <div class="swiper-button-prev swiper-another-prev" style="top: 208px; left: -6px;">
                        <img src="{$basePath}/new-design/swiper-arrow-prev.svg" alt="prev">
                    </div>
                    <div class="swiper-button-next swiper-another-next" style="top: 208px; right: -38px;">
                        <img src="{$basePath}/new-design/swiper-arrow-next.svg" alt="next">
                    </div>
                </div>
            {/cache}

            {cache 'new-bestTipsDescriptionBlock-' . $cashbackCacheKeyPostfix, 'expire' => '6 hours', tags => ['shop/' . $shop->getId()]}
                <div class="">
                    {var $bestTipsForSaleBoxBlock = $getDescriptionBlock('best_tips_for_sale_box')}

					{if $bestTipsForSaleBoxBlock && $bestTipsForSaleBoxBlock->getCountOfCharacters() > 0}
                        {include '../blocks/bestTipsForSaleBox.latte', block => $bestTipsForSaleBoxBlock, logo => $shop->getCurrentLogo()}
                    {else}
                        {var $bestTipsDescriptionBlock = $descriptionBlock('best_tips_for_sale')}

                        <div class="relative bg-white rounded-2xl mt-10 px-5 md:px-8 pt-7 pb-[18px] mb-5"
                                n:if="$bestTipsDescriptionBlock">

                            <div class="absolute right-[-17px] -top-[20px]">
                                <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-best_tips_for_sale" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                    <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                                </a>
                            </div>

                            <div class="flex items-center gap-[23px]">
                                <img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

                                <h2 class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
                                    {if $bestTipsDescriptionBlock[0]->getTitle()}
                                        {$bestTipsDescriptionBlock[0]->getTitle()}
                                    {else}
                                        {_newFront.shops.shop.shop.tips.title}
                                    {/if}
                                </h2>
                            </div>

                            <div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

                            <div class="content-block">
                                {$bestTipsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                            </div>
                        </div>
                    {/if}


					{if $shop->getShopQuestions()->count() > 0}
                        {include '../../snippets/question-answer.latte'}
                    {/if}

					{var $couponsDescriptionBoxBlock = $getDescriptionBlock('coupon_instructions_box')}

					{if $couponsDescriptionBoxBlock && $couponsDescriptionBoxBlock->getCountOfCharacters() > 0}
                        {include '../blocks/couponInstructionsBox.latte', block => $couponsDescriptionBoxBlock, logo => $shop->getCurrentLogo()}
                    {else}
                        {var $couponsDescriptionBlock = $descriptionBlock('coupon_instructions')}

                        <div id="couponsDescription" class="relative bg-white rounded-2xl px-5 md:px-8 pt-7 pb-[18px]"
                                n:if="$couponsDescriptionBlock">

                            <div class="absolute right-[-17px] -top-[20px]">
                                <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-coupon_instructions" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                    <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                                </a>
                            </div>

                            <div class="flex items-center gap-[23px]">
                                <img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

                                <div class="text-dark-1 text:base md:text-lg font-medium md:leading-7">
                                    <h2 class="shop-detail__section-title">
                                        {_'front.shops.shop.shop.couponsDescriptionTitle',
                                        [name => $shop->getName()]}</h2>
                                </div>
                            </div>

                            <div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

                            <div class="content-block">
                                {$couponsDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                            </div>
                        </div>
                    {/if}

					{var $longDescriptionBlock = $descriptionBlock('long_description')}

                    <div class="relative bg-white rounded-2xl mt-5 px-5 md:px-8 pt-7 pb-[18px] mb-[50px] md:mb-5"
                            n:if="$longDescriptionBlock">

                        <div class="absolute right-[-17px] -top-[20px]">
                            <a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin()" href="{plink :Admin:Shops:Shop:shop $shop->getId()}#content-long_description" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
                                <svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
                            </a>
                        </div>

                        <div class="flex items-center gap-[23px]">
                            <img class="max-w-20 max-h-[30px]" src="{$shop->getCurrentLogo() |image:170,0}" alt="logo" loading="lazy">

                            <h2 class="text-dark-1 text-base md:text-lg font-medium md:leading-7">
                                {if $longDescriptionBlock[0]->getTitle()}
                                    {$longDescriptionBlock[0]->getTitle()}
                                {/if}
                            </h2>
                        </div>

                        <div class="w-full h-px bg-light-5 mt-[22px] mb-[30px]"></div>

                        <div class="content-block text-dark-1 text-sm leading[24.5px] mb-[30px]">
                            {$longDescriptionBlock[0]->getDescription() |content:html,null,true |noescape}
                        </div>
                    </div>
                </div>
            {/cache}
            </div>
        </div>
    </div>
</div>

{if $user->isLoggedIn() === true && $user->getIdentity()->hasInstalledAddon() === false}

{/if}

{if $isLoginFromAddonPopupAllowed}
    {snippetArea signInFromAddon}
        {include ../signInModal.latte, shop: $shop}
    {/snippetArea}
{else}
    {snippetArea signUp}
        {include ../signUpModal.latte, shop: $shop}
    {/snippetArea}
{/if}

<style>
    @media only screen and (min-width: 1024px) {
        .description-transparency::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 1.5em;
            background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 80%);
        }
    }


    @media only screen and (max-width: 1024px) {
        .swiper-button-prev,
        .swiper-button-next {
            display: none !important;
        }
    }

    .swiper-button-prev.swiper-button-disabled {
        display: none !important;
    }
    .swiper-button-next::after,
    .swiper-button-prev::after {
        display: none !important;
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 80px;
        z-index: 40;
    }

    .swiper-button-next.swiper-button-disabled,
    .swiper-button-prev.swiper-button-disabled {
        display: none;
    }

    .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        top: 193px;
    }

    .swiper-pagination-bullet-active {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        width: 29px;
    }

    @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }
    .animate-marquee {
        animation: marquee var(--marquee-duration, 30s) linear infinite;
        will-change: transform;
    }

    @media only screen and (max-width: 768px) {
        .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
            top: 183px;
        }
    }

     .sticky-tabs::before {
         content: '';
         position: absolute;
         left: calc(0% - 50vw);
         width: 150vw;
         height: 278px;
         margin-top: -246px;
         background-repeat: repeat;
         background-image: none;
         transition: background-image 0.2s;
         z-index: -1;
     }
    .sticky-tabs.has-bg::before {
        background-image: var(--bg-image);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const desc = document.getElementById('description');
        const toggles = [
            document.getElementById('toggle-description'),
            document.getElementById('toggle-description-mobile'),
        ].filter(Boolean);

        if (desc && toggles.length) {
            const showAll = () => {
                desc.classList.remove('line-clamp-2', 'description-transparency');
                toggles.forEach(btn => {
                    btn.style.display = 'none'
                });
            };

            if (!desc.classList.contains('line-clamp-2')) {
                toggles.forEach(btn => btn.classList.add('hidden'));
            } else {
                toggles.forEach(btn => btn.addEventListener('click', showAll, { once: true }));
            }
        }

        /* SWIPER START CONFIG */
        const swiperConfigs = [
          {
            selector: '.swiper-coupons',
            prevEl: '.swiper-coupons-prev.swiper-coupons-prev',
            nextEl: '.swiper-coupons-next.swiper-coupons-next',
            paginationEl: '.swiper-coupons-pagination',
            slidesPerView: 1.3,
            breakpoints: {
                768: { slidesPerView: 1.3 },
                1024: { slidesPerView: 1.8 },
                1280: { slidesPerView: 2.6 }
            },
            gradientSelectors: { left: '.left-coupons-gradient', right: '.right-coupons-gradient' }
          },
          {
            selector: '.swiper-older-coupons',
            prevEl: '.swiper-button-prev.swiper-older-coupons-prev',
            nextEl: '.swiper-button-next.swiper-older-coupons-next',
            paginationEl: '.swiper-older-coupons-pagination',
            slidesPerView: 1.3,
            breakpoints: {
                768: { slidesPerView: 1.8 },
                1024: { slidesPerView: 1.8 },
                1280: { slidesPerView: 2.6 }
            },
            gradientSelectors: { left: '.left-older-gradient', right: '.right-older-gradient' }
          },
          {
            selector: '.swiper-another-coupons',
            prevEl: '.swiper-button-prev.swiper-another-prev',
            nextEl: '.swiper-button-next.swiper-another-next',
            paginationEl: '.swiper-another-coupons-pagination',
            slidesPerView: 1,
              autoHeight: true,
            breakpoints: {
                768: { slidesPerView: 1.3 },
                1024: { slidesPerView: 1 },
                1280: { slidesPerView: 1.3 }
            },
            gradientSelectors: { left: '.left-another-gradient', right: '.right-another-gradient' }
          }
        ];

        const toggleGradients = (prevBtnSel, nextBtnSel, leftGradSel, rightGradSel) => {
          const prevBtn = document.querySelector(prevBtnSel);
          const nextBtn = document.querySelector(nextBtnSel);
          const leftGrad = document.querySelector(leftGradSel);
          const rightGrad = document.querySelector(rightGradSel);

          if (leftGrad && prevBtn) {
            const disabled = prevBtn.classList.contains('swiper-button-disabled');
            leftGrad.style.opacity = disabled ? '0' : '1';
            leftGrad.style.pointerEvents = disabled ? 'none' : '';
          }
          if (rightGrad && nextBtn) {
            const disabled = nextBtn.classList.contains('swiper-button-disabled');
            rightGrad.style.opacity = disabled ? '0' : '1';
            rightGrad.style.pointerEvents = disabled ? 'none' : '';
          }
        };

        const swiperEvents = ['slideChange', 'toEdge', 'fromEdge', 'reachEnd', 'reachBeginning', 'init'];

        swiperConfigs.forEach(cfg => {
          new Swiper(cfg.selector, {
            direction: 'horizontal',
            spaceBetween: 20,
            slidesPerView: cfg.slidesPerView,
            navigation: { nextEl: cfg.nextEl, prevEl: cfg.prevEl },
            breakpoints: cfg.breakpoints,
            pagination: { el: cfg.paginationEl, type: 'bullets' },
            on: swiperEvents.reduce((handlers, event) => {
              handlers[event] = () => toggleGradients(cfg.prevEl, cfg.nextEl, cfg.gradientSelectors.left, cfg.gradientSelectors.right);
              return handlers;
            }, {})
          });
        });
        /* SWIPER END CONFIG */

        // Copy to clipboard
        const couponContainers = document.querySelectorAll('.coupon-code-copy');
        couponContainers.forEach(container => {
            const span = container.querySelector('span');
            const img = container.querySelector('img');
            if (!span || !img) return;

            const originalText = span.textContent;
            const originalSrc = img.src;
            const greenSrc = originalSrc.replace('coupon-profile.svg', 'coupon-profile-green.svg');

            container.addEventListener('click', function () {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(originalText);
                } else {
                    const textarea = document.createElement('textarea');
                    textarea.value = originalText;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                }

                span.textContent = {_'newFront.deals.deal.copied'};
                span.classList.remove('text-dark-1');
                span.classList.add('text-secondary-green');
                img.src = greenSrc;

                const svg = container.querySelector('.svg-copy-btn');
                const svgPaths = svg ? svg.querySelectorAll('path') : [];
                const originalStroke = '#80899C';
                const copiedStroke = '#66B940';
                svgPaths.forEach(path => path.setAttribute('stroke', copiedStroke));

                setTimeout(function () {
                    span.textContent = originalText;
                    span.classList.remove('text-secondary-green');
                    span.classList.add('text-dark-1');
                    img.src = originalSrc;
                    svgPaths.forEach(path => path.setAttribute('stroke', originalStroke));
                }, 1500);
            });
        });

        // Marquee effect
        document.querySelectorAll('.animate-marquee').forEach(function(track) {
            track.innerHTML += track.innerHTML;
            const items = Array.from(track.children).slice(0, track.children.length / 2);
            const totalWidth = items.reduce((acc, el) => acc + el.offsetWidth + 16, 0);
            track.style.minWidth = totalWidth * 2 + 'px';
        });
    });
</script>

<script n:syntax="off">
    document.addEventListener("DOMContentLoaded", function() {
        const stickyTabs = document.querySelector('.sticky-tabs');
        const sections = ['#coupons', '#cashback', '#content'];

        const sectionConfig = {
            '#coupons': 478,
            'default': () => stickyTabs.offsetHeight + 10
        };

        function getOffset(sectionId) {
            return sectionConfig[sectionId] || sectionConfig.default();
        }

        function setActiveTab(activeIndex) {
            sections.forEach((sectionId, index) => {
                const tab = document.querySelector(`.sticky-tabs a[href="${sectionId}"]`);
                if (!tab) return;

                tab.classList.remove(
                    'border-primary-orange', 'text-primary-orange', 'font-bold',
                    'border-light-4', 'text-dark-2'
                );
                tab.classList.add('bg-white', 'rounded-lg');

                if (index === activeIndex) {
                    tab.classList.add('border-primary-orange', 'text-primary-orange', 'font-bold');
                } else {
                    tab.classList.add('border-light-4', 'text-dark-2');
                }
            });
        }

        function scrollToSection(sectionId) {
            const section = document.querySelector(sectionId);
            if (!section) return;

            const offset = getOffset(sectionId);
            const targetPosition = section.offsetTop - offset;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }

        function getCurrentSection() {
            const scrollY = window.scrollY;
            const firstSection = document.querySelector(sections[0]);

            if (scrollY < firstSection.offsetTop - getOffset(sections[0])) {
                return 0;
            }

            for (let i = sections.length - 1; i >= 0; i--) {
                const section = document.querySelector(sections[i]);
                if (!section) continue;

                const offset = getOffset(sections[i]);
                const sectionTop = section.offsetTop - offset;

                if (scrollY >= sectionTop) {
                    return i;
                }
            }

            return -1;
        }

        sections.forEach((sectionId, index) => {
            const tab = document.querySelector(`.sticky-tabs a[href="${sectionId}"]`);
            if (!tab) return;

            tab.addEventListener('click', function(e) {
                e.preventDefault();
                setActiveTab(index);
                scrollToSection(sectionId);
            });
        });

        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const activeSection = getCurrentSection();
                setActiveTab(activeSection);
            }, 10);
        });

        setActiveTab(getCurrentSection());
    });
</script>

